/*
 * SIP version 0.7.8
 * Copyright (c) 2014-2017 Junction Networks, Inc <http://www.onsip.com>
 * Homepage: http://sipjs.com
 * License: http://sipjs.com/license/
 *
 *
 * ~~~SIP.js contains substantial portions of JsSIP under the following license~~~
 * Homepage: http://jssip.net
 * Copyright (c) 2012-2013 <PERSON> <http://www.versatica.com>
 *
 * Permission is hereby granted, free of charge, to any person obtaining
 * a copy of this software and associated documentation files (the
 * "Software"), to deal in the Software without restriction, including
 * without limitation the rights to use, copy, modify, merge, publish,
 * distribute, sublicense, and/or sell copies of the Software, and to
 * permit persons to whom the Software is furnished to do so, subject to
 * the following conditions:
 *
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
 * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
 * OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
 * WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 *
 * ~~~ end JsSIP license ~~~
 */


!function(a){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=a();else if("function"==typeof define&&define.amd)define([],a);else{var b;b="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this,b.SIP=a()}}(function(){return function a(b,c,d){function e(g,h){if(!c[g]){if(!b[g]){var i="function"==typeof require&&require;if(!h&&i)return i(g,!0);if(f)return f(g,!0);var j=new Error("Cannot find module '"+g+"'");throw j.code="MODULE_NOT_FOUND",j}var k=c[g]={exports:{}};b[g][0].call(k.exports,function(a){var c=b[g][1][a];return e(c?c:a)},k,k.exports,a,b,c,d)}return c[g].exports}for(var f="function"==typeof require&&require,g=0;g<d.length;g++)e(d[g]);return e}({1:[function(a,b,c){function d(){this._events=this._events||{},this._maxListeners=this._maxListeners||void 0}function e(a){return"function"==typeof a}function f(a){return"number"==typeof a}function g(a){return"object"==typeof a&&null!==a}function h(a){return void 0===a}b.exports=d,d.EventEmitter=d,d.prototype._events=void 0,d.prototype._maxListeners=void 0,d.defaultMaxListeners=10,d.prototype.setMaxListeners=function(a){if(!f(a)||0>a||isNaN(a))throw TypeError("n must be a positive number");return this._maxListeners=a,this},d.prototype.emit=function(a){var b,c,d,f,i,j;if(this._events||(this._events={}),"error"===a&&(!this._events.error||g(this._events.error)&&!this._events.error.length)){if(b=arguments[1],b instanceof Error)throw b;throw TypeError('Uncaught, unspecified "error" event.')}if(c=this._events[a],h(c))return!1;if(e(c))switch(arguments.length){case 1:c.call(this);break;case 2:c.call(this,arguments[1]);break;case 3:c.call(this,arguments[1],arguments[2]);break;default:for(d=arguments.length,f=new Array(d-1),i=1;d>i;i++)f[i-1]=arguments[i];c.apply(this,f)}else if(g(c)){for(d=arguments.length,f=new Array(d-1),i=1;d>i;i++)f[i-1]=arguments[i];for(j=c.slice(),d=j.length,i=0;d>i;i++)j[i].apply(this,f)}return!0},d.prototype.addListener=function(a,b){var c;if(!e(b))throw TypeError("listener must be a function");if(this._events||(this._events={}),this._events.newListener&&this.emit("newListener",a,e(b.listener)?b.listener:b),this._events[a]?g(this._events[a])?this._events[a].push(b):this._events[a]=[this._events[a],b]:this._events[a]=b,g(this._events[a])&&!this._events[a].warned){var c;c=h(this._maxListeners)?d.defaultMaxListeners:this._maxListeners,c&&c>0&&this._events[a].length>c&&(this._events[a].warned=!0,console.error("(node) warning: possible EventEmitter memory leak detected. %d listeners added. Use emitter.setMaxListeners() to increase limit.",this._events[a].length),"function"==typeof console.trace&&console.trace())}return this},d.prototype.on=d.prototype.addListener,d.prototype.once=function(a,b){function c(){this.removeListener(a,c),d||(d=!0,b.apply(this,arguments))}if(!e(b))throw TypeError("listener must be a function");var d=!1;return c.listener=b,this.on(a,c),this},d.prototype.removeListener=function(a,b){var c,d,f,h;if(!e(b))throw TypeError("listener must be a function");if(!this._events||!this._events[a])return this;if(c=this._events[a],f=c.length,d=-1,c===b||e(c.listener)&&c.listener===b)delete this._events[a],this._events.removeListener&&this.emit("removeListener",a,b);else if(g(c)){for(h=f;h-->0;)if(c[h]===b||c[h].listener&&c[h].listener===b){d=h;break}if(0>d)return this;1===c.length?(c.length=0,delete this._events[a]):c.splice(d,1),this._events.removeListener&&this.emit("removeListener",a,b)}return this},d.prototype.removeAllListeners=function(a){var b,c;if(!this._events)return this;if(!this._events.removeListener)return 0===arguments.length?this._events={}:this._events[a]&&delete this._events[a],this;if(0===arguments.length){for(b in this._events)"removeListener"!==b&&this.removeAllListeners(b);return this.removeAllListeners("removeListener"),this._events={},this}if(c=this._events[a],e(c))this.removeListener(a,c);else for(;c.length;)this.removeListener(a,c[c.length-1]);return delete this._events[a],this},d.prototype.listeners=function(a){var b;return b=this._events&&this._events[a]?e(this._events[a])?[this._events[a]]:this._events[a].slice():[]},d.listenerCount=function(a,b){var c;return c=a._events&&a._events[b]?e(a._events[b])?1:a._events[b].length:0}},{}],2:[function(a,b,c){b.exports={name:"sip.js",title:"SIP.js",description:"A simple, intuitive, and powerful JavaScript signaling library",version:"0.7.8",main:"src/index.js",browser:{"./src/environment.js":"./src/environment_browser.js"},homepage:"http://sipjs.com",author:"OnSIP <<EMAIL>> (http://sipjs.com/authors/)",contributors:[{url:"https://github.com/onsip/SIP.js/blob/master/THANKS.md"}],repository:{type:"git",url:"https://github.com/onsip/SIP.js.git"},keywords:["sip","websocket","webrtc","library","javascript"],devDependencies:{beefy:"^2.1.5",browserify:"^4.1.8",grunt:"~0.4.0","grunt-browserify":"^4.0.1","grunt-cli":"~0.1.6","grunt-contrib-copy":"^0.5.0","grunt-contrib-jasmine":"^1.0.3","grunt-contrib-jshint":">0.5.0","grunt-contrib-uglify":"~0.2.0","grunt-peg":"~1.3.1","grunt-trimtrailingspaces":"^0.4.0",pegjs:"^0.8.0"},engines:{node:">=0.12"},license:"MIT",scripts:{repl:"beefy test/repl.js --open",build:"grunt build",prepublish:"cd src/Grammar && mkdir -p dist && pegjs --extra-options-file peg.json src/Grammar.pegjs dist/Grammar.js",test:"grunt travis --verbose"},dependencies:{ws:"^1.0.1"},optionalDependencies:{promiscuous:"^0.6.0"}}},{}],3:[function(a,b,c){"use strict";b.exports=function(a){var b;b=function(b,c,d,e){var f=d;if(void 0===d)throw new TypeError("Not enough arguments");if(this.ua=b,this.logger=b.getLogger("sip.clientcontext"),this.method=c,d=b.normalizeTarget(d),!d)throw new TypeError("Invalid target: "+f);e=Object.create(e||Object.prototype),e.extraHeaders=(e.extraHeaders||[]).slice(),this.request=new a.OutgoingRequest(this.method,d,this.ua,e.params,e.extraHeaders),e.body&&(this.body={},this.body.body=e.body,e.contentType&&(this.body.contentType=e.contentType),this.request.body=this.body),this.localIdentity=this.request.from,this.remoteIdentity=this.request.to,this.data={}},b.prototype=Object.create(a.EventEmitter.prototype),b.prototype.send=function(){return new a.RequestSender(this,this.ua).send(),this},b.prototype.cancel=function(b){b=b||{},b.extraHeaders=(b.extraHeaders||[]).slice();var c=a.Utils.getCancelReason(b.status_code,b.reason_phrase);this.request.cancel(c,b.extraHeaders),this.emit("cancel")},b.prototype.receiveResponse=function(b){var c=a.Utils.getReasonPhrase(b.status_code);switch(!0){case/^1[0-9]{2}$/.test(b.status_code):this.emit("progress",b,c);break;case/^2[0-9]{2}$/.test(b.status_code):this.ua.applicants[this]&&delete this.ua.applicants[this],this.emit("accepted",b,c);break;default:this.ua.applicants[this]&&delete this.ua.applicants[this],this.emit("rejected",b,c),this.emit("failed",b,c)}},b.prototype.onRequestTimeout=function(){this.emit("failed",null,a.C.causes.REQUEST_TIMEOUT)},b.prototype.onTransportError=function(){this.emit("failed",null,a.C.causes.CONNECTION_ERROR)},a.ClientContext=b}},{}],4:[function(a,b,c){"use strict";b.exports=function(a,b){return{USER_AGENT:a+"/"+b,SIP:"sip",SIPS:"sips",causes:{CONNECTION_ERROR:"Connection Error",REQUEST_TIMEOUT:"Request Timeout",SIP_FAILURE_CODE:"SIP Failure Code",INTERNAL_ERROR:"Internal Error",BUSY:"Busy",REJECTED:"Rejected",REDIRECTED:"Redirected",UNAVAILABLE:"Unavailable",NOT_FOUND:"Not Found",ADDRESS_INCOMPLETE:"Address Incomplete",INCOMPATIBLE_SDP:"Incompatible SDP",AUTHENTICATION_ERROR:"Authentication Error",DIALOG_ERROR:"Dialog Error",WEBRTC_NOT_SUPPORTED:"WebRTC Not Supported",WEBRTC_ERROR:"WebRTC Error",CANCELED:"Canceled",NO_ANSWER:"No Answer",EXPIRES:"Expires",NO_ACK:"No ACK",NO_PRACK:"No PRACK",USER_DENIED_MEDIA_ACCESS:"User Denied Media Access",BAD_MEDIA_DESCRIPTION:"Bad Media Description",RTP_TIMEOUT:"RTP Timeout"},supported:{UNSUPPORTED:"none",SUPPORTED:"supported",REQUIRED:"required"},SIP_ERROR_CAUSES:{REDIRECTED:[300,301,302,305,380],BUSY:[486,600],REJECTED:[403,603],NOT_FOUND:[404,604],UNAVAILABLE:[480,410,408,430],ADDRESS_INCOMPLETE:[484],INCOMPATIBLE_SDP:[488,606],AUTHENTICATION_ERROR:[401,407]},ACK:"ACK",BYE:"BYE",CANCEL:"CANCEL",INFO:"INFO",INVITE:"INVITE",MESSAGE:"MESSAGE",NOTIFY:"NOTIFY",OPTIONS:"OPTIONS",REGISTER:"REGISTER",UPDATE:"UPDATE",SUBSCRIBE:"SUBSCRIBE",REFER:"REFER",PRACK:"PRACK",REASON_PHRASE:{100:"Trying",180:"Ringing",181:"Call Is Being Forwarded",182:"Queued",183:"Session Progress",199:"Early Dialog Terminated",200:"OK",202:"Accepted",204:"No Notification",300:"Multiple Choices",301:"Moved Permanently",302:"Moved Temporarily",305:"Use Proxy",380:"Alternative Service",400:"Bad Request",401:"Unauthorized",402:"Payment Required",403:"Forbidden",404:"Not Found",405:"Method Not Allowed",406:"Not Acceptable",407:"Proxy Authentication Required",408:"Request Timeout",410:"Gone",412:"Conditional Request Failed",413:"Request Entity Too Large",414:"Request-URI Too Long",415:"Unsupported Media Type",416:"Unsupported URI Scheme",417:"Unknown Resource-Priority",420:"Bad Extension",421:"Extension Required",422:"Session Interval Too Small",423:"Interval Too Brief",428:"Use Identity Header",429:"Provide Referrer Identity",430:"Flow Failed",433:"Anonymity Disallowed",436:"Bad Identity-Info",437:"Unsupported Certificate",438:"Invalid Identity Header",439:"First Hop Lacks Outbound Support",440:"Max-Breadth Exceeded",469:"Bad Info Package",470:"Consent Needed",478:"Unresolvable Destination",480:"Temporarily Unavailable",481:"Call/Transaction Does Not Exist",482:"Loop Detected",483:"Too Many Hops",484:"Address Incomplete",485:"Ambiguous",486:"Busy Here",487:"Request Terminated",488:"Not Acceptable Here",489:"Bad Event",491:"Request Pending",493:"Undecipherable",494:"Security Agreement Required",500:"Internal Server Error",501:"Not Implemented",502:"Bad Gateway",503:"Service Unavailable",504:"Server Time-out",505:"Version Not Supported",513:"Message Too Large",580:"Precondition Failure",600:"Busy Everywhere",603:"Decline",604:"Does Not Exist Anywhere",606:"Not Acceptable"},OPTION_TAGS:{"100rel":!0,199:!0,answermode:!0,"early-session":!0,eventlist:!0,explicitsub:!0,"from-change":!0,"geolocation-http":!0,"geolocation-sip":!0,gin:!0,gruu:!0,histinfo:!0,ice:!0,join:!0,"multiple-refer":!0,norefersub:!0,nosub:!0,outbound:!0,path:!0,policy:!0,precondition:!0,pref:!0,privacy:!0,"recipient-list-invite":!0,"recipient-list-message":!0,"recipient-list-subscribe":!0,replaces:!0,"resource-priority":!0,"sdp-anat":!0,"sec-agree":!0,tdialog:!0,timer:!0,uui:!0}}}},{}],5:[function(a,b,c){"use strict";b.exports=function(a){var b;return b=function(a,b,c){this.dialog=a,this.applicant=b,this.request=c,this.reattempt=!1,this.reattemptTimer=null},b.prototype={send:function(){var b=this,c=new a.RequestSender(this,this.dialog.owner.ua);c.send(),this.request.method===a.C.INVITE&&c.clientTransaction.state!==a.Transactions.C.STATUS_TERMINATED&&(this.dialog.uac_pending_reply=!0,c.clientTransaction.on("stateChanged",function d(){(this.state===a.Transactions.C.STATUS_ACCEPTED||this.state===a.Transactions.C.STATUS_COMPLETED||this.state===a.Transactions.C.STATUS_TERMINATED)&&(this.removeListener("stateChanged",d),b.dialog.uac_pending_reply=!1,b.dialog.uas_pending_reply===!1&&b.dialog.owner.onReadyToReinvite())}))},onRequestTimeout:function(){this.applicant.onRequestTimeout()},onTransportError:function(){this.applicant.onTransportError()},receiveResponse:function(b){var c=this;408===b.status_code||481===b.status_code?this.applicant.onDialogError(b):b.method===a.C.INVITE&&491===b.status_code?this.reattempt?this.applicant.receiveResponse(b):(this.request.cseq.value=this.dialog.local_seqnum+=1,this.reattemptTimer=a.Timers.setTimeout(function(){c.applicant.owner.status!==a.Session.C.STATUS_TERMINATED&&(c.reattempt=!0,c.request_sender.send())},this.getReattemptTimeout())):this.applicant.receiveResponse(b)}},b}},{}],6:[function(a,b,c){"use strict";b.exports=function(b){var c,d=a("./Dialog/RequestSender")(b),e={STATUS_EARLY:1,STATUS_CONFIRMED:2};c=function(a,c,d,f){var g;return this.uac_pending_reply=!1,this.uas_pending_reply=!1,c.hasHeader("contact")?(f=c instanceof b.IncomingResponse?c.status_code<200?e.STATUS_EARLY:e.STATUS_CONFIRMED:f||e.STATUS_CONFIRMED,g=c.parseHeader("contact"),"UAS"===d?(this.id={call_id:c.call_id,local_tag:c.to_tag,remote_tag:c.from_tag,toString:function(){return this.call_id+this.local_tag+this.remote_tag}},this.state=f,this.remote_seqnum=c.cseq,this.local_uri=c.parseHeader("to").uri,this.remote_uri=c.parseHeader("from").uri,this.remote_target=g.uri,this.route_set=c.getHeaders("record-route"),this.invite_seqnum=c.cseq,this.local_seqnum=c.cseq):"UAC"===d&&(this.id={call_id:c.call_id,local_tag:c.from_tag,remote_tag:c.to_tag,toString:function(){return this.call_id+this.local_tag+this.remote_tag}},this.state=f,this.invite_seqnum=c.cseq,this.local_seqnum=c.cseq,this.local_uri=c.parseHeader("from").uri,this.pracked=[],this.remote_uri=c.parseHeader("to").uri,this.remote_target=g.uri,this.route_set=c.getHeaders("record-route").reverse(),this.state!==e.STATUS_EARLY||a.hasOffer||(this.mediaHandler=a.mediaHandlerFactory(a))),this.logger=a.ua.getLogger("sip.dialog",this.id.toString()),this.owner=a,a.ua.dialogs[this.id.toString()]=this,this.logger.log("new "+d+" dialog created with status "+(this.state===e.STATUS_EARLY?"EARLY":"CONFIRMED")),void a.emit("dialog",this)):{error:"unable to create a Dialog without Contact header field"}},c.prototype={update:function(a,b){this.state=e.STATUS_CONFIRMED,this.logger.log("dialog "+this.id.toString()+"  changed to CONFIRMED state"),"UAC"===b&&(this.route_set=a.getHeaders("record-route").reverse())},terminate:function(){this.logger.log("dialog "+this.id.toString()+" deleted"),this.mediaHandler&&this.state!==e.STATUS_CONFIRMED&&this.mediaHandler.peerConnection.close(),delete this.owner.ua.dialogs[this.id.toString()]},createRequest:function(a,c,d){var e,f;return c=(c||[]).slice(),this.local_seqnum||(this.local_seqnum=Math.floor(1e4*Math.random())),e=a===b.C.CANCEL||a===b.C.ACK?this.invite_seqnum:this.local_seqnum+=1,f=new b.OutgoingRequest(a,this.remote_target,this.owner.ua,{cseq:e,call_id:this.id.call_id,from_uri:this.local_uri,from_tag:this.id.local_tag,to_uri:this.remote_uri,to_tag:this.id.remote_tag,route_set:this.route_set},c,d),f.dialog=this,f},checkInDialogRequest:function(a){var c=this;if(this.remote_seqnum){if(a.cseq<this.remote_seqnum)return a.method!==b.C.ACK&&a.reply(500),a.cseq===this.invite_seqnum?!0:!1;a.cseq>this.remote_seqnum&&(this.remote_seqnum=a.cseq)}else this.remote_seqnum=a.cseq;switch(a.method){case b.C.INVITE:if(this.uac_pending_reply===!0)a.reply(491);else{if(this.uas_pending_reply===!0){var d=(10*Math.random()|0)+1;return a.reply(500,null,["Retry-After:"+d]),!1}this.uas_pending_reply=!0,a.server_transaction.on("stateChanged",function e(){(this.state===b.Transactions.C.STATUS_ACCEPTED||this.state===b.Transactions.C.STATUS_COMPLETED||this.state===b.Transactions.C.STATUS_TERMINATED)&&(this.removeListener("stateChanged",e),c.uas_pending_reply=!1,c.uac_pending_reply===!1&&c.owner.onReadyToReinvite())})}a.hasHeader("contact")&&a.server_transaction.on("stateChanged",function(){this.state===b.Transactions.C.STATUS_ACCEPTED&&(c.remote_target=a.parseHeader("contact").uri)});break;case b.C.NOTIFY:a.hasHeader("contact")&&a.server_transaction.on("stateChanged",function(){this.state===b.Transactions.C.STATUS_COMPLETED&&(c.remote_target=a.parseHeader("contact").uri)})}return!0},sendRequest:function(a,b,c){c=c||{};var e=(c.extraHeaders||[]).slice(),f=null;c.body&&(c.body.body?f=c.body:(f={},f.body=c.body,c.contentType&&(f.contentType=c.contentType)));var g=this.createRequest(b,e,f),h=new d(this,a,g);return h.send(),g},receiveRequest:function(a){this.checkInDialogRequest(a)&&this.owner.receiveRequest(a)}},c.C=e,b.Dialog=c}},{"./Dialog/RequestSender":5}],7:[function(a,b,c){"use strict";b.exports=function(a){var b;return b=function(a){this.logger=a.getLogger("sipjs.digestauthentication"),this.username=a.configuration.authorizationUser,this.password=a.configuration.password,this.cnonce=null,this.nc=0,this.ncHex="00000000",this.response=null},b.prototype.authenticate=function(b,c){if(this.algorithm=c.algorithm,this.realm=c.realm,this.nonce=c.nonce,this.opaque=c.opaque,this.stale=c.stale,this.algorithm){if("MD5"!==this.algorithm)return this.logger.warn('challenge with Digest algorithm different than "MD5", authentication aborted'),!1}else this.algorithm="MD5";if(!this.realm)return this.logger.warn("challenge without Digest realm, authentication aborted"),!1;if(!this.nonce)return this.logger.warn("challenge without Digest nonce, authentication aborted"),!1;if(c.qop)if(c.qop.indexOf("auth")>-1)this.qop="auth";else{if(!(c.qop.indexOf("auth-int")>-1))return this.logger.warn('challenge without Digest qop different than "auth" or "auth-int", authentication aborted'),!1;this.qop="auth-int"}else this.qop=null;return this.method=b.method,this.uri=b.ruri,this.cnonce=a.createRandomToken(12),this.nc+=1,this.updateNcHex(),4294967296===this.nc&&(this.nc=1,this.ncHex="00000001"),this.calculateResponse(),!0},b.prototype.calculateResponse=function(){var b,c;b=a.calculateMD5(this.username+":"+this.realm+":"+this.password),"auth"===this.qop?(c=a.calculateMD5(this.method+":"+this.uri),this.response=a.calculateMD5(b+":"+this.nonce+":"+this.ncHex+":"+this.cnonce+":auth:"+c)):"auth-int"===this.qop?(c=a.calculateMD5(this.method+":"+this.uri+":"+a.calculateMD5(this.body?this.body:"")),this.response=a.calculateMD5(b+":"+this.nonce+":"+this.ncHex+":"+this.cnonce+":auth-int:"+c)):null===this.qop&&(c=a.calculateMD5(this.method+":"+this.uri),this.response=a.calculateMD5(b+":"+this.nonce+":"+c))},b.prototype.toString=function(){var a=[];if(!this.response)throw new Error("response field does not exist, cannot generate Authorization header");return a.push("algorithm="+this.algorithm),a.push('username="'+this.username+'"'),a.push('realm="'+this.realm+'"'),a.push('nonce="'+this.nonce+'"'),a.push('uri="'+this.uri+'"'),a.push('response="'+this.response+'"'),this.opaque&&a.push('opaque="'+this.opaque+'"'),this.qop&&(a.push("qop="+this.qop),a.push('cnonce="'+this.cnonce+'"'),a.push("nc="+this.ncHex)),"Digest "+a.join(", ")},b.prototype.updateNcHex=function(){var a=Number(this.nc).toString(16);this.ncHex="00000000".substr(0,8-a.length)+a},b}},{}],8:[function(a,b,c){"use strict";var d=a("events").EventEmitter;b.exports=function(a){function b(){d.call(this)}return b.prototype=Object.create(d.prototype,{constructor:{value:b,enumerable:!1,writable:!0,configurable:!0}}),b.prototype.off=function(b,c){var d="";return d+="SIP.EventEmitter#off is deprecated and may be removed in future SIP.js versions.\n",d+="Please use removeListener or removeAllListeners instead.\n",d+="See here for more details:\n",d+="http://nodejs.org/api/events.html#events_emitter_removelistener_event_listener",a.warn(d),arguments.length<2?this.removeAllListeners.apply(this,arguments):this.removeListener(b,c)},b}},{events:1}],9:[function(a,b,c){"use strict";b.exports={ConfigurationError:function(){var a=function(a,b){this.code=1,this.name="CONFIGURATION_ERROR",this.parameter=a,this.value=b,this.message=this.value?"Invalid value "+JSON.stringify(this.value)+' for parameter "'+this.parameter+'"':"Missing parameter: "+this.parameter};return a.prototype=new Error,a}(),InvalidStateError:function(){var a=function(a){this.code=2,this.name="INVALID_STATE_ERROR",this.status=a,this.message="Invalid status: "+a};return a.prototype=new Error,a}(),NotSupportedError:function(){var a=function(a){this.code=3,this.name="NOT_SUPPORTED_ERROR",this.message=a};return a.prototype=new Error,a}(),GetDescriptionError:function(){var a=function(a){this.code=4,this.name="GET_DESCRIPTION_ERROR",this.message=a};return a.prototype=new Error,a}()}},{}],10:[function(a,b,c){"use strict";var d=a("./Grammar/dist/Grammar");b.exports=function(a){return{parse:function(b,c){var e={startRule:c,SIP:a};try{d.parse(b,e)}catch(f){e.data=-1}return e.data}}}},{"./Grammar/dist/Grammar":11}],11:[function(a,b,c){b.exports=function(){function a(a,b){function c(){this.constructor=a}c.prototype=b.prototype,a.prototype=new c}function b(a,b,c,d,e,f){this.message=a,this.expected=b,this.found=c,this.offset=d,this.line=e,this.column=f,this.name="SyntaxError"}function c(a){function c(){return a.substring(s,r)}function d(){return s}function e(b){function c(b,c,d){var e,f;for(e=c;d>e;e++)f=a.charAt(e),"\n"===f?(b.seenCR||b.line++,b.column=1,b.seenCR=!1):"\r"===f||"\u2028"===f||"\u2029"===f?(b.line++,b.column=1,b.seenCR=!0):(b.column++,b.seenCR=!1)}return t!==b&&(t>b&&(t=0,u={line:1,column:1,seenCR:!1}),c(u,t,b),t=b),u}function f(a){v>r||(r>v&&(v=r,w=[]),w.push(a))}function g(c,d,f){function g(a){var b=1;for(a.sort(function(a,b){return a.description<b.description?-1:a.description>b.description?1:0});b<a.length;)a[b-1]===a[b]?a.splice(b,1):b++}function h(a,b){function c(a){function b(a){return a.charCodeAt(0).toString(16).toUpperCase()}return a.replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/\x08/g,"\\b").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\f/g,"\\f").replace(/\r/g,"\\r").replace(/[\x00-\x07\x0B\x0E\x0F]/g,function(a){return"\\x0"+b(a)}).replace(/[\x10-\x1F\x80-\xFF]/g,function(a){return"\\x"+b(a)}).replace(/[\u0180-\u0FFF]/g,function(a){return"\\u0"+b(a)}).replace(/[\u1080-\uFFFF]/g,function(a){return"\\u"+b(a)})}var d,e,f,g=new Array(a.length);for(f=0;f<a.length;f++)g[f]=a[f].description;return d=a.length>1?g.slice(0,-1).join(", ")+" or "+g[a.length-1]:g[0],e=b?'"'+c(b)+'"':"end of input","Expected "+d+" but "+e+" found."}var i=e(f),j=f<a.length?a.charAt(f):null;return null!==d&&g(d),new b(null!==c?c:h(d,j),d,j,f,i.line,i.column)}function h(a){var b,c=new Array(a.length);for(b=0;b<a.length;b++)c[b]=a.charCodeAt(b)-32;return c}function i(b){function c(a){return"[object Array]"===Object.prototype.toString.apply(a)?[]:a}for(var d,e,g=q[b],h=0,j=[],k=g.length,l=[],n=[];;){for(;k>h;)switch(g[h]){case 0:n.push(c(p[g[h+1]])),h+=2;break;case 1:n.push(r),h++;break;case 2:n.pop(),h++;break;case 3:r=n.pop(),h++;break;case 4:n.length-=g[h+1],h+=2;break;case 5:n.splice(-2,1),h++;break;case 6:n[n.length-2].push(n.pop()),h++;break;case 7:n.push(n.splice(n.length-g[h+1],g[h+1])),h+=2;break;case 8:n.pop(),n.push(a.substring(n[n.length-1],r)),h++;break;case 9:l.push(k),j.push(h+3+g[h+1]+g[h+2]),n[n.length-1]?(k=h+3+g[h+1],h+=3):(k=h+3+g[h+1]+g[h+2],h+=3+g[h+1]);break;case 10:l.push(k),j.push(h+3+g[h+1]+g[h+2]),n[n.length-1]===m?(k=h+3+g[h+1],h+=3):(k=h+3+g[h+1]+g[h+2],h+=3+g[h+1]);break;case 11:l.push(k),j.push(h+3+g[h+1]+g[h+2]),n[n.length-1]!==m?(k=h+3+g[h+1],h+=3):(k=h+3+g[h+1]+g[h+2],h+=3+g[h+1]);break;case 12:n[n.length-1]!==m?(l.push(k),j.push(h),k=h+2+g[h+1],h+=2):h+=2+g[h+1];break;case 13:l.push(k),j.push(h+3+g[h+1]+g[h+2]),a.length>r?(k=h+3+g[h+1],h+=3):(k=h+3+g[h+1]+g[h+2],h+=3+g[h+1]);break;case 14:l.push(k),j.push(h+4+g[h+2]+g[h+3]),a.substr(r,p[g[h+1]].length)===p[g[h+1]]?(k=h+4+g[h+2],h+=4):(k=h+4+g[h+2]+g[h+3],h+=4+g[h+2]);break;case 15:l.push(k),j.push(h+4+g[h+2]+g[h+3]),a.substr(r,p[g[h+1]].length).toLowerCase()===p[g[h+1]]?(k=h+4+g[h+2],h+=4):(k=h+4+g[h+2]+g[h+3],h+=4+g[h+2]);break;case 16:l.push(k),j.push(h+4+g[h+2]+g[h+3]),p[g[h+1]].test(a.charAt(r))?(k=h+4+g[h+2],h+=4):(k=h+4+g[h+2]+g[h+3],h+=4+g[h+2]);break;case 17:n.push(a.substr(r,g[h+1])),r+=g[h+1],h+=2;break;case 18:n.push(p[g[h+1]]),r+=p[g[h+1]].length,h+=2;break;case 19:n.push(m),0===x&&f(p[g[h+1]]),h+=2;break;case 20:s=n[n.length-1-g[h+1]],h+=2;break;case 21:s=r,h++;break;case 22:for(d=g.slice(h+4,h+4+g[h+3]),e=0;e<g[h+3];e++)d[e]=n[n.length-1-d[e]];n.splice(n.length-g[h+2],g[h+2],p[g[h+1]].apply(null,d)),h+=4+g[h+3];break;case 23:n.push(i(g[h+1])),h+=2;break;case 24:x++,h++;break;case 25:x--,h++;break;default:throw new Error("Invalid opcode: "+g[h]+".")}if(!(l.length>0))break;k=l.pop(),h=j.pop()}return n[0]}function j(a,b){return[a].concat(b)}var k,l=arguments.length>1?arguments[1]:{},m={},n={Contact:118,Name_Addr_Header:155,Record_Route:175,Request_Response:81,SIP_URI:45,Subscription_State:185,Supported:190,Require:181,Via:193,absoluteURI:84,Call_ID:117,Content_Disposition:129,Content_Length:134,Content_Type:135,CSeq:145,displayName:121,Event:148,From:150,host:52,Max_Forwards:153,Min_SE:212,Proxy_Authenticate:156,quoted_string:40,Refer_To:177,Replaces:178,Session_Expires:209,stun_URI:216,To:191,turn_URI:223,uuid:226,WWW_Authenticate:208,challenge:157},o=118,p=["\r\n",{type:"literal",value:"\r\n",description:'"\\r\\n"'},/^[0-9]/,{type:"class",value:"[0-9]",description:"[0-9]"},/^[a-zA-Z]/,{type:"class",value:"[a-zA-Z]",description:"[a-zA-Z]"},/^[0-9a-fA-F]/,{type:"class",value:"[0-9a-fA-F]",description:"[0-9a-fA-F]"},/^[\0-\xFF]/,{type:"class",value:"[\\0-\\xFF]",description:"[\\0-\\xFF]"},/^["]/,{type:"class",value:'["]',description:'["]'}," ",{type:"literal",value:" ",description:'" "'},"	",{type:"literal",value:"	",description:'"\\t"'},/^[a-zA-Z0-9]/,{type:"class",value:"[a-zA-Z0-9]",description:"[a-zA-Z0-9]"},";",{type:"literal",value:";",description:'";"'},"/",{type:"literal",value:"/",description:'"/"'},"?",{type:"literal",value:"?",description:'"?"'},":",{type:"literal",value:":",description:'":"'},"@",{type:"literal",value:"@",description:'"@"'},"&",{type:"literal",value:"&",description:'"&"'},"=",{type:"literal",value:"=",description:'"="'},"+",{type:"literal",value:"+",description:'"+"'},"$",{type:"literal",value:"$",description:'"$"'},",",{type:"literal",value:",",description:'","'},"-",{type:"literal",value:"-",description:'"-"'},"_",{type:"literal",value:"_",description:'"_"'},".",{type:"literal",value:".",description:'"."'},"!",{type:"literal",value:"!",description:'"!"'},"~",{type:"literal",value:"~",description:'"~"'},"*",{type:"literal",value:"*",description:'"*"'},"'",{type:"literal",value:"'",description:'"\'"'},"(",{type:"literal",value:"(",description:'"("'},")",{type:"literal",value:")",description:'")"'},m,"%",{type:"literal",value:"%",description:'"%"'},null,[],function(){return" "},function(){return":"},/^[!-~]/,{type:"class",value:"[!-~]",description:"[!-~]"},/^[\x80-\uFFFF]/,{type:"class",value:"[\\x80-\\uFFFF]",description:"[\\x80-\\uFFFF]"},/^[\x80-\xBF]/,{type:"class",value:"[\\x80-\\xBF]",description:"[\\x80-\\xBF]"},/^[a-f]/,{type:"class",value:"[a-f]",description:"[a-f]"},"`",{type:"literal",value:"`",description:'"`"'},"<",{type:"literal",value:"<",description:'"<"'},">",{type:"literal",value:">",description:'">"'},"\\",{type:"literal",value:"\\",description:'"\\\\"'},"[",{type:"literal",value:"[",description:'"["'},"]",{type:"literal",value:"]",description:'"]"'},"{",{type:"literal",value:"{",description:'"{"'},"}",{type:"literal",value:"}",description:'"}"'},function(){return"*"},function(){return"/"},function(){return"="},function(){return"("},function(){return")"},function(){return">"},function(){return"<"},function(){return","},function(){return";"},function(){return":"},function(){return'"'},/^[!-']/,{type:"class",value:"[!-']",description:"[!-']"},/^[*-[]/,{type:"class",value:"[*-[]",description:"[*-[]"},/^[\]-~]/,{type:"class",value:"[\\]-~]",description:"[\\]-~]"},function(a){return a},/^[#-[]/,{type:"class",value:"[#-[]",description:"[#-[]"},/^[\0-\t]/,{type:"class",value:"[\\0-\\t]",description:"[\\0-\\t]"},/^[\x0B-\f]/,{type:"class",value:"[\\x0B-\\f]",description:"[\\x0B-\\f]"},/^[\x0E-]/,{type:"class",value:"[\\x0E-]",description:"[\\x0E-]"},function(){l.data.uri=new l.SIP.URI(l.data.scheme,l.data.user,l.data.host,l.data.port),delete l.data.scheme,delete l.data.user,delete l.data.host,delete l.data.host_type,delete l.data.port},function(){l.data.uri=new l.SIP.URI(l.data.scheme,l.data.user,l.data.host,l.data.port,l.data.uri_params,l.data.uri_headers),delete l.data.scheme,delete l.data.user,delete l.data.host,delete l.data.host_type,delete l.data.port,delete l.data.uri_params,"SIP_URI"===l.startRule&&(l.data=l.data.uri)},"sips",{type:"literal",value:"sips",description:'"sips"'},"sip",{type:"literal",value:"sip",description:'"sip"'},function(a){l.data.scheme=a},function(){l.data.user=decodeURIComponent(c().slice(0,-1))},function(){l.data.password=c()},function(){return l.data.host=c(),l.data.host},function(){return l.data.host_type="domain",c()},/^[a-zA-Z0-9_\-]/,{type:"class",value:"[a-zA-Z0-9_\\-]",description:"[a-zA-Z0-9_\\-]"},/^[a-zA-Z0-9\-]/,{type:"class",value:"[a-zA-Z0-9\\-]",description:"[a-zA-Z0-9\\-]"},function(){return l.data.host_type="IPv6",c()},"::",{type:"literal",value:"::",description:'"::"'},function(){return l.data.host_type="IPv6",c()},function(){return l.data.host_type="IPv4",c()},"25",{type:"literal",value:"25",description:'"25"'},/^[0-5]/,{type:"class",value:"[0-5]",description:"[0-5]"},"2",{type:"literal",value:"2",description:'"2"'},/^[0-4]/,{type:"class",value:"[0-4]",description:"[0-4]"},"1",{type:"literal",value:"1",description:'"1"'},/^[1-9]/,{type:"class",value:"[1-9]",description:"[1-9]"},function(a){return a=parseInt(a.join("")),l.data.port=a,a},"transport=",{type:"literal",value:"transport=",description:'"transport="'},"udp",{type:"literal",value:"udp",description:'"udp"'},"tcp",{type:"literal",value:"tcp",description:'"tcp"'},"sctp",{type:"literal",value:"sctp",description:'"sctp"'},"tls",{type:"literal",value:"tls",description:'"tls"'},function(a){l.data.uri_params||(l.data.uri_params={}),l.data.uri_params.transport=a.toLowerCase()},"user=",{type:"literal",value:"user=",description:'"user="'},"phone",{type:"literal",value:"phone",description:'"phone"'},"ip",{type:"literal",value:"ip",description:'"ip"'},function(a){l.data.uri_params||(l.data.uri_params={}),l.data.uri_params.user=a.toLowerCase()},"method=",{type:"literal",value:"method=",description:'"method="'},function(a){l.data.uri_params||(l.data.uri_params={}),l.data.uri_params.method=a},"ttl=",{type:"literal",value:"ttl=",description:'"ttl="'},function(a){l.data.params||(l.data.params={}),l.data.params.ttl=a},"maddr=",{type:"literal",value:"maddr=",description:'"maddr="'},function(a){l.data.uri_params||(l.data.uri_params={}),l.data.uri_params.maddr=a},"lr",{type:"literal",value:"lr",description:'"lr"'},function(){l.data.uri_params||(l.data.uri_params={}),l.data.uri_params.lr=void 0},function(a,b){l.data.uri_params||(l.data.uri_params={}),b=null===b?void 0:b[1],l.data.uri_params[a.toLowerCase()]=b&&b.toLowerCase()},function(a,b){a=a.join("").toLowerCase(),b=b.join(""),l.data.uri_headers||(l.data.uri_headers={}),l.data.uri_headers[a]?l.data.uri_headers[a].push(b):l.data.uri_headers[a]=[b]},function(){"Refer_To"===l.startRule&&(l.data.uri=new l.SIP.URI(l.data.scheme,l.data.user,l.data.host,l.data.port,l.data.uri_params,l.data.uri_headers),delete l.data.scheme,delete l.data.user,delete l.data.host,delete l.data.host_type,delete l.data.port,delete l.data.uri_params)},"//",{type:"literal",value:"//",description:'"//"'},function(){l.data.scheme=c()},{type:"literal",value:"SIP",description:'"SIP"'},function(){l.data.sip_version=c()},"INVITE",{type:"literal",value:"INVITE",description:'"INVITE"'},"ACK",{type:"literal",value:"ACK",description:'"ACK"'},"VXACH",{type:"literal",value:"VXACH",description:'"VXACH"'},"OPTIONS",{type:"literal",value:"OPTIONS",description:'"OPTIONS"'},"BYE",{type:"literal",value:"BYE",description:'"BYE"'},"CANCEL",{type:"literal",value:"CANCEL",description:'"CANCEL"'},"REGISTER",{type:"literal",value:"REGISTER",description:'"REGISTER"'},"SUBSCRIBE",{type:"literal",value:"SUBSCRIBE",description:'"SUBSCRIBE"'},"NOTIFY",{type:"literal",value:"NOTIFY",description:'"NOTIFY"'},"REFER",{type:"literal",value:"REFER",description:'"REFER"'},function(){return l.data.method=c(),l.data.method},function(a){l.data.status_code=parseInt(a.join(""))},function(){l.data.reason_phrase=c()},function(){l.data=c()},function(){var a,b;for(b=l.data.multi_header.length,a=0;b>a;a++)if(null===l.data.multi_header[a].parsed){l.data=null;break}null!==l.data?l.data=l.data.multi_header:l.data=-1},function(){
var a;l.data.multi_header||(l.data.multi_header=[]);try{a=new l.SIP.NameAddrHeader(l.data.uri,l.data.displayName,l.data.params),delete l.data.uri,delete l.data.displayName,delete l.data.params}catch(b){a=null}l.data.multi_header.push({position:r,offset:d(),parsed:a})},function(a){a=c().trim(),'"'===a[0]&&(a=a.substring(1,a.length-1)),l.data.displayName=a},"q",{type:"literal",value:"q",description:'"q"'},function(a){l.data.params||(l.data.params={}),l.data.params.q=a},"expires",{type:"literal",value:"expires",description:'"expires"'},function(a){l.data.params||(l.data.params={}),l.data.params.expires=a},function(a){return parseInt(a.join(""))},"0",{type:"literal",value:"0",description:'"0"'},function(){return parseFloat(c())},function(a,b){l.data.params||(l.data.params={}),b=null===b?void 0:b[1],l.data.params[a.toLowerCase()]=b},"render",{type:"literal",value:"render",description:'"render"'},"session",{type:"literal",value:"session",description:'"session"'},"icon",{type:"literal",value:"icon",description:'"icon"'},"alert",{type:"literal",value:"alert",description:'"alert"'},function(){"Content_Disposition"===l.startRule&&(l.data.type=c().toLowerCase())},"handling",{type:"literal",value:"handling",description:'"handling"'},"optional",{type:"literal",value:"optional",description:'"optional"'},"required",{type:"literal",value:"required",description:'"required"'},function(a){l.data=parseInt(a.join(""))},function(){l.data=c()},"text",{type:"literal",value:"text",description:'"text"'},"image",{type:"literal",value:"image",description:'"image"'},"audio",{type:"literal",value:"audio",description:'"audio"'},"video",{type:"literal",value:"video",description:'"video"'},"application",{type:"literal",value:"application",description:'"application"'},"message",{type:"literal",value:"message",description:'"message"'},"multipart",{type:"literal",value:"multipart",description:'"multipart"'},"x-",{type:"literal",value:"x-",description:'"x-"'},function(a){l.data.value=parseInt(a.join(""))},function(a){l.data=a},function(a){l.data.event=a.toLowerCase()},function(){var a=l.data.tag;l.data=new l.SIP.NameAddrHeader(l.data.uri,l.data.displayName,l.data.params),a&&l.data.setParam("tag",a)},"tag",{type:"literal",value:"tag",description:'"tag"'},function(a){l.data.tag=a},function(a){l.data=parseInt(a.join(""))},function(a){l.data=a},function(){l.data=new l.SIP.NameAddrHeader(l.data.uri,l.data.displayName,l.data.params)},"digest",{type:"literal",value:"Digest",description:'"Digest"'},"realm",{type:"literal",value:"realm",description:'"realm"'},function(a){l.data.realm=a},"domain",{type:"literal",value:"domain",description:'"domain"'},"nonce",{type:"literal",value:"nonce",description:'"nonce"'},function(a){l.data.nonce=a},"opaque",{type:"literal",value:"opaque",description:'"opaque"'},function(a){l.data.opaque=a},"stale",{type:"literal",value:"stale",description:'"stale"'},"true",{type:"literal",value:"true",description:'"true"'},function(){l.data.stale=!0},"false",{type:"literal",value:"false",description:'"false"'},function(){l.data.stale=!1},"algorithm",{type:"literal",value:"algorithm",description:'"algorithm"'},"md5",{type:"literal",value:"MD5",description:'"MD5"'},"md5-sess",{type:"literal",value:"MD5-sess",description:'"MD5-sess"'},function(a){l.data.algorithm=a.toUpperCase()},"qop",{type:"literal",value:"qop",description:'"qop"'},"auth-int",{type:"literal",value:"auth-int",description:'"auth-int"'},"auth",{type:"literal",value:"auth",description:'"auth"'},function(a){l.data.qop||(l.data.qop=[]),l.data.qop.push(a.toLowerCase())},function(a){l.data.value=parseInt(a.join(""))},function(){var a,b;for(b=l.data.multi_header.length,a=0;b>a;a++)if(null===l.data.multi_header[a].parsed){l.data=null;break}null!==l.data?l.data=l.data.multi_header:l.data=-1},function(){var a;l.data.multi_header||(l.data.multi_header=[]);try{a=new l.SIP.NameAddrHeader(l.data.uri,l.data.displayName,l.data.params),delete l.data.uri,delete l.data.displayName,delete l.data.params}catch(b){a=null}l.data.multi_header.push({position:r,offset:d(),parsed:a})},function(){l.data=new l.SIP.NameAddrHeader(l.data.uri,l.data.displayName,l.data.params)},function(){l.data.replaces_from_tag&&l.data.replaces_to_tag||(l.data=-1)},function(){l.data={call_id:l.data}},"from-tag",{type:"literal",value:"from-tag",description:'"from-tag"'},function(a){l.data.replaces_from_tag=a},"to-tag",{type:"literal",value:"to-tag",description:'"to-tag"'},function(a){l.data.replaces_to_tag=a},"early-only",{type:"literal",value:"early-only",description:'"early-only"'},function(){l.data.early_only=!0},function(a){return a},function(a,b){return j(a,b)},function(a){"Require"===l.startRule&&(l.data=a||[])},function(a){l.data.value=parseInt(a.join(""))},"active",{type:"literal",value:"active",description:'"active"'},"pending",{type:"literal",value:"pending",description:'"pending"'},"terminated",{type:"literal",value:"terminated",description:'"terminated"'},function(){l.data.state=c()},"reason",{type:"literal",value:"reason",description:'"reason"'},function(a){"undefined"!=typeof a&&(l.data.reason=a)},function(a){"undefined"!=typeof a&&(l.data.expires=a)},"retry_after",{type:"literal",value:"retry_after",description:'"retry_after"'},function(a){"undefined"!=typeof a&&(l.data.retry_after=a)},"deactivated",{type:"literal",value:"deactivated",description:'"deactivated"'},"probation",{type:"literal",value:"probation",description:'"probation"'},"rejected",{type:"literal",value:"rejected",description:'"rejected"'},"timeout",{type:"literal",value:"timeout",description:'"timeout"'},"giveup",{type:"literal",value:"giveup",description:'"giveup"'},"noresource",{type:"literal",value:"noresource",description:'"noresource"'},"invariant",{type:"literal",value:"invariant",description:'"invariant"'},function(a){"Supported"===l.startRule&&(l.data=a||[])},function(){var a=l.data.tag;l.data=new l.SIP.NameAddrHeader(l.data.uri,l.data.displayName,l.data.params),a&&l.data.setParam("tag",a)},"ttl",{type:"literal",value:"ttl",description:'"ttl"'},function(a){l.data.ttl=a},"maddr",{type:"literal",value:"maddr",description:'"maddr"'},function(a){l.data.maddr=a},"received",{type:"literal",value:"received",description:'"received"'},function(a){l.data.received=a},"branch",{type:"literal",value:"branch",description:'"branch"'},function(a){l.data.branch=a},"rport",{type:"literal",value:"rport",description:'"rport"'},function(){"undefined"!=typeof response_port&&(l.data.rport=response_port.join(""))},function(a){l.data.protocol=a},{type:"literal",value:"UDP",description:'"UDP"'},{type:"literal",value:"TCP",description:'"TCP"'},{type:"literal",value:"TLS",description:'"TLS"'},{type:"literal",value:"SCTP",description:'"SCTP"'},function(a){l.data.transport=a},function(){l.data.host=c()},function(a){l.data.port=parseInt(a.join(""))},function(a){return parseInt(a.join(""))},function(a){"Session_Expires"===l.startRule&&(l.data.deltaSeconds=a)},"refresher",{type:"literal",value:"refresher",description:'"refresher"'},"uas",{type:"literal",value:"uas",description:'"uas"'},"uac",{type:"literal",value:"uac",description:'"uac"'},function(a){"Session_Expires"===l.startRule&&(l.data.refresher=a)},function(a){"Min_SE"===l.startRule&&(l.data=a)},"stuns",{type:"literal",value:"stuns",description:'"stuns"'},"stun",{type:"literal",value:"stun",description:'"stun"'},function(a){l.data.scheme=a},function(a){l.data.host=a},"?transport=",{type:"literal",value:"?transport=",description:'"?transport="'},"turns",{type:"literal",value:"turns",description:'"turns"'},"turn",{type:"literal",value:"turn",description:'"turn"'},function(){l.data.transport=transport},function(){l.data=c()}],q=[h('. ""2 3!'),h('0"""1!3#'),h('0$""1!3%'),h('0&""1!3\''),h("7'*# \"7("),h('0(""1!3)'),h('0*""1!3+'),h('.,""2,3-'),h('..""2.3/'),h('00""1!31'),h('.2""2233*\x89 ".4""2435*} ".6""2637*q ".8""2839*e ".:""2:3;*Y ".<""2<3=*M ".>""2>3?*A ".@""2@3A*5 ".B""2B3C*) ".D""2D3E'),h('7)*# "7,'),h('.F""2F3G*} ".H""2H3I*q ".J""2J3K*e ".L""2L3M*Y ".N""2N3O*M ".P""2P3Q*A ".R""2R3S*5 ".T""2T3U*) ".V""2V3W'),h('!!.Y""2Y3Z+7$7#+-%7#+#%\'#%$## X$"# X"# X+! (%'),h('!! \\7$,#&7$"+-$7 +#%\'"%$"# X"# X*# " [+@$ \\7$+&$,#&7$""" X+\'%4"6]" %$"# X"# X'),h('7.*# " ['),h('! \\7\'*# "7(,)&7\'*# "7("+A$.8""2839+1%7/+\'%4#6^# %$## X$"# X"# X'),h('!! \\72+&$,#&72""" X+o$ \\! \\7.,#&7."+-$72+#%\'"%$"# X"# X,@&! \\7.,#&7."+-$72+#%\'"%$"# X"# X"+#%\'"%$"# X"# X+! (%'),h('0_""1!3`*# "73'),h('0a""1!3b'),h('0c""1!3d'),h('7!*) "0e""1!3f'),h('! \\7)*\x95 ".F""2F3G*\x89 ".J""2J3K*} ".L""2L3M*q ".Y""2Y3Z*e ".P""2P3Q*Y ".H""2H3I*M ".@""2@3A*A ".g""2g3h*5 ".R""2R3S*) ".N""2N3O+\x9e$,\x9b&7)*\x95 ".F""2F3G*\x89 ".J""2J3K*} ".L""2L3M*q ".Y""2Y3Z*e ".P""2P3Q*Y ".H""2H3I*M ".@""2@3A*A ".g""2g3h*5 ".R""2R3S*) ".N""2N3O""" X+! (%'),h('! \\7)*\x89 ".F""2F3G*} ".L""2L3M*q ".Y""2Y3Z*e ".P""2P3Q*Y ".H""2H3I*M ".@""2@3A*A ".g""2g3h*5 ".R""2R3S*) ".N""2N3O+\x92$,\x8f&7)*\x89 ".F""2F3G*} ".L""2L3M*q ".Y""2Y3Z*e ".P""2P3Q*Y ".H""2H3I*M ".@""2@3A*A ".g""2g3h*5 ".R""2R3S*) ".N""2N3O""" X+! (%'),h('.T""2T3U*\xe3 ".V""2V3W*\xd7 ".i""2i3j*\xcb ".k""2k3l*\xbf ".:""2:3;*\xb3 ".D""2D3E*\xa7 ".2""2233*\x9b ".8""2839*\x8f ".m""2m3n*\x83 "7&*} ".4""2435*q ".o""2o3p*e ".q""2q3r*Y ".6""2637*M ".>""2>3?*A ".s""2s3t*5 ".u""2u3v*) "7\'*# "7('),h('! \\7)*\u012b ".F""2F3G*\u011f ".J""2J3K*\u0113 ".L""2L3M*\u0107 ".Y""2Y3Z*\xfb ".P""2P3Q*\xef ".H""2H3I*\xe3 ".@""2@3A*\xd7 ".g""2g3h*\xcb ".R""2R3S*\xbf ".N""2N3O*\xb3 ".T""2T3U*\xa7 ".V""2V3W*\x9b ".i""2i3j*\x8f ".k""2k3l*\x83 ".8""2839*w ".m""2m3n*k "7&*e ".4""2435*Y ".o""2o3p*M ".q""2q3r*A ".6""2637*5 ".s""2s3t*) ".u""2u3v+\u0134$,\u0131&7)*\u012b ".F""2F3G*\u011f ".J""2J3K*\u0113 ".L""2L3M*\u0107 ".Y""2Y3Z*\xfb ".P""2P3Q*\xef ".H""2H3I*\xe3 ".@""2@3A*\xd7 ".g""2g3h*\xcb ".R""2R3S*\xbf ".N""2N3O*\xb3 ".T""2T3U*\xa7 ".V""2V3W*\x9b ".i""2i3j*\x8f ".k""2k3l*\x83 ".8""2839*w ".m""2m3n*k "7&*e ".4""2435*Y ".o""2o3p*M ".q""2q3r*A ".6""2637*5 ".s""2s3t*) ".u""2u3v""" X+! (%'),h('!7/+A$.P""2P3Q+1%7/+\'%4#6w# %$## X$"# X"# X'),h('!7/+A$.4""2435+1%7/+\'%4#6x# %$## X$"# X"# X'),h('!7/+A$.>""2>3?+1%7/+\'%4#6y# %$## X$"# X"# X'),h('!7/+A$.T""2T3U+1%7/+\'%4#6z# %$## X$"# X"# X'),h('!7/+A$.V""2V3W+1%7/+\'%4#6{# %$## X$"# X"# X'),h('!.k""2k3l+1$7/+\'%4"6|" %$"# X"# X'),h('!7/+7$.i""2i3j+\'%4"6}" %$"# X"# X'),h('!7/+A$.D""2D3E+1%7/+\'%4#6~# %$## X$"# X"# X'),h('!7/+A$.2""2233+1%7/+\'%4#6# %$## X$"# X"# X'),h('!7/+A$.8""2839+1%7/+\'%4#6\x80# %$## X$"# X"# X'),h('!7/+1$7&+\'%4"6\x81" %$"# X"# X'),h('!7&+1$7/+\'%4"6\x81" %$"# X"# X'),h('!7=+W$ \\7G*) "7K*# "7F,/&7G*) "7K*# "7F"+-%7>+#%\'#%$## X$"# X"# X'),h('0\x82""1!3\x83*A "0\x84""1!3\x85*5 "0\x86""1!3\x87*) "73*# "7.'),h('!!7/+U$7&+K% \\7J*# "7K,)&7J*# "7K"+-%7&+#%\'$%$$# X$## X$"# X"# X+! (%'),h('!7/+`$7&+V%! \\7J*# "7K,)&7J*# "7K"+! (%+2%7&+(%4$6\x88$!!%$$# X$## X$"# X"# X'),h('7.*G ".L""2L3M*; "0\x89""1!3\x8a*/ "0\x86""1!3\x87*# "73'),h('!.m""2m3n+K$0\x8b""1!3\x8c*5 "0\x8d""1!3\x8e*) "0\x8f""1!3\x90+#%\'"%$"# X"# X'),h('!7N+Q$.8""2839+A%7O*# " [+1%7S+\'%4$6\x91$ %$$# X$## X$"# X"# X'),h('!7N+k$.8""2839+[%7O*# " [+K%7S+A%7_+7%7l*# " [+\'%4&6\x92& %$&# X$%# X$$# X$## X$"# X"# X'),h('!/\x93""1$3\x94*) "/\x95""1#3\x96+\' 4!6\x97!! %'),h('!7P+b$!.8""2839+-$7R+#%\'"%$"# X"# X*# " [+7%.:""2:3;+\'%4#6\x98# %$## X$"# X"# X'),h(' \\7+*) "7-*# "7Q+2$,/&7+*) "7-*# "7Q""" X'),h('.<""2<3=*q ".>""2>3?*e ".@""2@3A*Y ".B""2B3C*M ".D""2D3E*A ".2""2233*5 ".6""2637*) ".4""2435'),h('! \\7+*_ "7-*Y ".<""2<3=*M ".>""2>3?*A ".@""2@3A*5 ".B""2B3C*) ".D""2D3E,e&7+*_ "7-*Y ".<""2<3=*M ".>""2>3?*A ".@""2@3A*5 ".B""2B3C*) ".D""2D3E"+& 4!6\x99! %'),h('!7T+N$!.8""2839+-$7^+#%\'"%$"# X"# X*# " [+#%\'"%$"# X"# X'),h('!7U*) "7\\*# "7X+& 4!6\x9a! %'),h('! \\!7V+3$.J""2J3K+#%\'"%$"# X"# X,>&!7V+3$.J""2J3K+#%\'"%$"# X"# X"+G$7W+=%.J""2J3K*# " [+\'%4#6\x9b# %$## X$"# X"# X'),h(' \\0\x9c""1!3\x9d+,$,)&0\x9c""1!3\x9d""" X'),h('!0$""1!3%+A$ \\0\x9e""1!3\x9f,)&0\x9e""1!3\x9f"+#%\'"%$"# X"# X'),h('!.o""2o3p+A$7Y+7%.q""2q3r+\'%4#6\xa0# %$## X$"# X"# X'),h('!!7Z+\xbf$.8""2839+\xaf%7Z+\xa5%.8""2839+\x95%7Z+\x8b%.8""2839+{%7Z+q%.8""2839+a%7Z+W%.8""2839+G%7Z+=%.8""2839+-%7[+#%\'-%$-# X$,# X$+# X$*# X$)# X$(# X$\'# X$&# X$%# X$$# X$## X$"# X"# X*\u0838 "!.\xa1""2\xa13\xa2+\xaf$7Z+\xa5%.8""2839+\x95%7Z+\x8b%.8""2839+{%7Z+q%.8""2839+a%7Z+W%.8""2839+G%7Z+=%.8""2839+-%7[+#%\',%$,# X$+# X$*# X$)# X$(# X$\'# X$&# X$%# X$$# X$## X$"# X"# X*\u0795 "!.\xa1""2\xa13\xa2+\x95$7Z+\x8b%.8""2839+{%7Z+q%.8""2839+a%7Z+W%.8""2839+G%7Z+=%.8""2839+-%7[+#%\'*%$*# X$)# X$(# X$\'# X$&# X$%# X$$# X$## X$"# X"# X*\u070c "!.\xa1""2\xa13\xa2+{$7Z+q%.8""2839+a%7Z+W%.8""2839+G%7Z+=%.8""2839+-%7[+#%\'(%$(# X$\'# X$&# X$%# X$$# X$## X$"# X"# X*\u069d "!.\xa1""2\xa13\xa2+a$7Z+W%.8""2839+G%7Z+=%.8""2839+-%7[+#%\'&%$&# X$%# X$$# X$## X$"# X"# X*\u0648 "!.\xa1""2\xa13\xa2+G$7Z+=%.8""2839+-%7[+#%\'$%$$# X$## X$"# X"# X*\u060d "!.\xa1""2\xa13\xa2+-$7[+#%\'"%$"# X"# X*\u05ec "!.\xa1""2\xa13\xa2+-$7Z+#%\'"%$"# X"# X*\u05cb "!7Z+\xa5$.\xa1""2\xa13\xa2+\x95%7Z+\x8b%.8""2839+{%7Z+q%.8""2839+a%7Z+W%.8""2839+G%7Z+=%.8""2839+-%7[+#%\'+%$+# X$*# X$)# X$(# X$\'# X$&# X$%# X$$# X$## X$"# X"# X*\u0538 "!7Z+\xb6$!.8""2839+-$7Z+#%\'"%$"# X"# X*# " [+\x8b%.\xa1""2\xa13\xa2+{%7Z+q%.8""2839+a%7Z+W%.8""2839+G%7Z+=%.8""2839+-%7[+#%\'*%$*# X$)# X$(# X$\'# X$&# X$%# X$$# X$## X$"# X"# X*\u0494 "!7Z+\xc7$!.8""2839+-$7Z+#%\'"%$"# X"# X*# " [+\x9c%!.8""2839+-$7Z+#%\'"%$"# X"# X*# " [+q%.\xa1""2\xa13\xa2+a%7Z+W%.8""2839+G%7Z+=%.8""2839+-%7[+#%\')%$)# X$(# X$\'# X$&# X$%# X$$# X$## X$"# X"# X*\u03df "!7Z+\xd8$!.8""2839+-$7Z+#%\'"%$"# X"# X*# " [+\xad%!.8""2839+-$7Z+#%\'"%$"# X"# X*# " [+\x82%!.8""2839+-$7Z+#%\'"%$"# X"# X*# " [+W%.\xa1""2\xa13\xa2+G%7Z+=%.8""2839+-%7[+#%\'(%$(# X$\'# X$&# X$%# X$$# X$## X$"# X"# X*\u0319 "!7Z+\xe9$!.8""2839+-$7Z+#%\'"%$"# X"# X*# " [+\xbe%!.8""2839+-$7Z+#%\'"%$"# X"# X*# " [+\x93%!.8""2839+-$7Z+#%\'"%$"# X"# X*# " [+h%!.8""2839+-$7Z+#%\'"%$"# X"# X*# " [+=%.\xa1""2\xa13\xa2+-%7[+#%\'\'%$\'# X$&# X$%# X$$# X$## X$"# X"# X*\u0242 "!7Z+\u0114$!.8""2839+-$7Z+#%\'"%$"# X"# X*# " [+\xe9%!.8""2839+-$7Z+#%\'"%$"# X"# X*# " [+\xbe%!.8""2839+-$7Z+#%\'"%$"# X"# X*# " [+\x93%!.8""2839+-$7Z+#%\'"%$"# X"# X*# " [+h%!.8""2839+-$7Z+#%\'"%$"# X"# X*# " [+=%.\xa1""2\xa13\xa2+-%7Z+#%\'(%$(# X$\'# X$&# X$%# X$$# X$## X$"# X"# X*\u0140 "!7Z+\u0135$!.8""2839+-$7Z+#%\'"%$"# X"# X*# " [+\u010a%!.8""2839+-$7Z+#%\'"%$"# X"# X*# " [+\xdf%!.8""2839+-$7Z+#%\'"%$"# X"# X*# " [+\xb4%!.8""2839+-$7Z+#%\'"%$"# X"# X*# " [+\x89%!.8""2839+-$7Z+#%\'"%$"# X"# X*# " [+^%!.8""2839+-$7Z+#%\'"%$"# X"# X*# " [+3%.\xa1""2\xa13\xa2+#%\'(%$(# X$\'# X$&# X$%# X$$# X$## X$"# X"# X+& 4!6\xa3! %'),h('!7#+S$7#*# " [+C%7#*# " [+3%7#*# " [+#%\'$%$$# X$## X$"# X"# X'),h('!7Z+=$.8""2839+-%7Z+#%\'#%$## X$"# X"# X*# "7\\'),h('!7]+u$.J""2J3K+e%7]+[%.J""2J3K+K%7]+A%.J""2J3K+1%7]+\'%4\'6\xa4\' %$\'# X$&# X$%# X$$# X$## X$"# X"# X'),h('!.\xa5""2\xa53\xa6+3$0\xa7""1!3\xa8+#%\'"%$"# X"# X*\xa0 "!.\xa9""2\xa93\xaa+=$0\xab""1!3\xac+-%7!+#%\'#%$## X$"# X"# X*o "!.\xad""2\xad3\xae+7$7!+-%7!+#%\'#%$## X$"# X"# X*D "!0\xaf""1!3\xb0+-$7!+#%\'"%$"# X"# X*# "7!'),h('!!7!*# " [+c$7!*# " [+S%7!*# " [+C%7!*# " [+3%7!*# " [+#%\'%%$%# X$$# X$## X$"# X"# X+\' 4!6\xb1!! %'),h(' \\!.2""2233+-$7`+#%\'"%$"# X"# X,>&!.2""2233+-$7`+#%\'"%$"# X"# X"'),h('7a*A "7b*; "7c*5 "7d*/ "7e*) "7f*# "7g'),h('!/\xb2""1*3\xb3+b$/\xb4""1#3\xb5*G "/\xb6""1#3\xb7*; "/\xb8""1$3\xb9*/ "/\xba""1#3\xbb*# "76+(%4"6\xbc"! %$"# X"# X'),h('!/\xbd""1%3\xbe+J$/\xbf""1%3\xc0*/ "/\xc1""1"3\xc2*# "76+(%4"6\xc3"! %$"# X"# X'),h('!/\xc4""1\'3\xc5+2$7\x8f+(%4"6\xc6"! %$"# X"# X'),h('!/\xc7""1$3\xc8+2$7\xef+(%4"6\xc9"! %$"# X"# X'),h('!/\xca""1&3\xcb+2$7T+(%4"6\xcc"! %$"# X"# X'),h('!/\xcd""1"3\xce+R$!.>""2>3?+-$76+#%\'"%$"# X"# X*# " [+\'%4"6\xcf" %$"# X"# X'),h('!7h+T$!.>""2>3?+-$7i+#%\'"%$"# X"# X*# " [+)%4"6\xd0""! %$"# X"# X'),h('! \\7j+&$,#&7j""" X+! (%'),h('! \\7j+&$,#&7j""" X+! (%'),h('7k*) "7+*# "7-'),h('.o""2o3p*e ".q""2q3r*Y ".4""2435*M ".8""2839*A ".<""2<3=*5 ".@""2@3A*) ".B""2B3C'),h('!.6""2637+u$7m+k% \\!.<""2<3=+-$7m+#%\'"%$"# X"# X,>&!.<""2<3=+-$7m+#%\'"%$"# X"# X"+#%\'#%$## X$"# X"# X'),h('!7n+C$.>""2>3?+3%7o+)%4#6\xd1#"" %$## X$"# X"# X'),h(' \\7p*) "7+*# "7-+2$,/&7p*) "7+*# "7-""" X'),h(' \\7p*) "7+*# "7-,/&7p*) "7+*# "7-"'),h('.o""2o3p*e ".q""2q3r*Y ".4""2435*M ".6""2637*A ".8""2839*5 ".@""2@3A*) ".B""2B3C'),h('7\x90*# "7r'),h("!7\x8f+K$7'+A%7s+7%7'+-%7\x84+#%'%%$%# X$$# X$## X$\"# X\"# X"),h('7M*# "7t'),h('!7+G$.8""2839+7%7u*# "7x+\'%4#6\xd2# %$## X$"# X"# X'),h('!7v*# "7w+N$!.6""2637+-$7\x83+#%\'"%$"# X"# X*# " [+#%\'"%$"# X"# X'),h('!.\xd3""2\xd33\xd4+=$7\x80+3%7w*# " [+#%\'#%$## X$"# X"# X'),h('!.4""2435+-$7{+#%\'"%$"# X"# X'),h('!7z+5$ \\7y,#&7y"+#%\'"%$"# X"# X'),h('7**) "7+*# "7-'),h('7+*\x8f "7-*\x89 ".2""2233*} ".6""2637*q ".8""2839*e ".:""2:3;*Y ".<""2<3=*M ".>""2>3?*A ".@""2@3A*5 ".B""2B3C*) ".D""2D3E'),h('!7|+k$ \\!.4""2435+-$7|+#%\'"%$"# X"# X,>&!.4""2435+-$7|+#%\'"%$"# X"# X"+#%\'"%$"# X"# X'),h('! \\7~,#&7~"+k$ \\!.2""2233+-$7}+#%\'"%$"# X"# X,>&!.2""2233+-$7}+#%\'"%$"# X"# X"+#%\'"%$"# X"# X'),h(' \\7~,#&7~"'),h('7+*w "7-*q ".8""2839*e ".:""2:3;*Y ".<""2<3=*M ".>""2>3?*A ".@""2@3A*5 ".B""2B3C*) ".D""2D3E'),h('!7"+\x8d$ \\7"*G "7!*A ".@""2@3A*5 ".F""2F3G*) ".J""2J3K,M&7"*G "7!*A ".@""2@3A*5 ".F""2F3G*) ".J""2J3K"+\'%4"6\xd5" %$"# X"# X'),h('7\x81*# "7\x82'),h('!!7O+3$.:""2:3;+#%\'"%$"# X"# X*# " [+-$7S+#%\'"%$"# X"# X*# " ['),h(' \\7+*\x83 "7-*} ".B""2B3C*q ".D""2D3E*e ".2""2233*Y ".8""2839*M ".:""2:3;*A ".<""2<3=*5 ".>""2>3?*) ".@""2@3A+\x8c$,\x89&7+*\x83 "7-*} ".B""2B3C*q ".D""2D3E*e ".2""2233*Y ".8""2839*M ".:""2:3;*A ".<""2<3=*5 ".>""2>3?*) ".@""2@3A""" X'),h(' \\7y,#&7y"'),h('!/\x95""1#3\xd6+y$.4""2435+i% \\7!+&$,#&7!""" X+P%.J""2J3K+@% \\7!+&$,#&7!""" X+\'%4%6\xd7% %$%# X$$# X$## X$"# X"# X'),h('.\xd8""2\xd83\xd9'),h('.\xda""2\xda3\xdb'),h('.\xdc""2\xdc3\xdd'),h('.\xde""2\xde3\xdf'),h('.\xe0""2\xe03\xe1'),h('.\xe2""2\xe23\xe3'),h('.\xe4""2\xe43\xe5'),h('.\xe6""2\xe63\xe7'),h('.\xe8""2\xe83\xe9'),h('.\xea""2\xea3\xeb'),h('!7\x85*S "7\x86*M "7\x88*G "7\x89*A "7\x8a*; "7\x8b*5 "7\x8c*/ "7\x8d*) "7\x8e*# "76+& 4!6\xec! %'),h("!7\x84+K$7'+A%7\x91+7%7'+-%7\x93+#%'%%$%# X$$# X$## X$\"# X\"# X"),h("!7\x92+' 4!6\xed!! %"),h('!7!+7$7!+-%7!+#%\'#%$## X$"# X"# X'),h('! \\7**A "7+*; "7-*5 "73*/ "74*) "7\'*# "7(,G&7**A "7+*; "7-*5 "73*/ "74*) "7\'*# "7("+& 4!6\xee! %'),h('!7\xb5+_$ \\!7A+-$7\xb5+#%\'"%$"# X"# X,8&!7A+-$7\xb5+#%\'"%$"# X"# X"+#%\'"%$"# X"# X'),h('!79+R$!.:""2:3;+-$79+#%\'"%$"# X"# X*# " [+\'%4"6\xef" %$"# X"# X'),h('!7:*j "!7\x97+_$ \\!7A+-$7\x97+#%\'"%$"# X"# X,8&!7A+-$7\x97+#%\'"%$"# X"# X"+#%\'"%$"# X"# X+& 4!6\xf0! %'),h('!7L*# "7\x98+c$ \\!7B+-$7\x9a+#%\'"%$"# X"# X,8&!7B+-$7\x9a+#%\'"%$"# X"# X"+\'%4"6\xf1" %$"# X"# X'),h('!7\x99*# " [+A$7@+7%7M+-%7?+#%\'$%$$# X$## X$"# X"# X'),h('!!76+_$ \\!7.+-$76+#%\'"%$"# X"# X,8&!7.+-$76+#%\'"%$"# X"# X"+#%\'"%$"# X"# X*# "7H+\' 4!6\xf2!! %'),h('7\x9b*) "7\x9c*# "7\x9f'),h('!/\xf3""1!3\xf4+<$7<+2%7\x9e+(%4#6\xf5#! %$## X$"# X"# X'),h('!/\xf6""1\'3\xf7+<$7<+2%7\x9d+(%4#6\xf8#! %$## X$"# X"# X'),h('! \\7!+&$,#&7!""" X+\' 4!6\xf9!! %'),h('!.\xfa""2\xfa3\xfb+x$!.J""2J3K+S$7!*# " [+C%7!*# " [+3%7!*# " [+#%\'$%$$# X$## X$"# X"# X*# " [+\'%4"6\xfc" %$"# X"# X'),h('!76+N$!7<+-$7\xa0+#%\'"%$"# X"# X*# " [+)%4"6\xfd""! %$"# X"# X'),h('76*) "7T*# "7H'),h('!7\xa2+_$ \\!7B+-$7\xa3+#%\'"%$"# X"# X,8&!7B+-$7\xa3+#%\'"%$"# X"# X"+#%\'"%$"# X"# X'),h('!/\xfe""1&3\xff*G "/\u0100""1\'3\u0101*; "/\u0102""1$3\u0103*/ "/\u0104""1%3\u0105*# "76+& 4!6\u0106! %'),h('7\xa4*# "7\x9f'),h('!/\u0107""1(3\u0108+O$7<+E%/\u0109""1(3\u010a*/ "/\u010b""1(3\u010c*# "76+#%\'#%$## X$"# X"# X'),h('!76+_$ \\!7A+-$76+#%\'"%$"# X"# X,8&!7A+-$76+#%\'"%$"# X"# X"+#%\'"%$"# X"# X'),h('! \\7!+&$,#&7!""" X+\' 4!6\u010d!! %'),h("!7\xa8+& 4!6\u010e! %"),h('!7\xa9+s$7;+i%7\xae+_% \\!7B+-$7\xaf+#%\'"%$"# X"# X,8&!7B+-$7\xaf+#%\'"%$"# X"# X"+#%\'$%$$# X$## X$"# X"# X'),h('7\xaa*# "7\xab'),h('/\u010f""1$3\u0110*S "/\u0111""1%3\u0112*G "/\u0113""1%3\u0114*; "/\u0115""1%3\u0116*/ "/\u0117""1+3\u0118*# "7\xac'),h('/\u0119""1\'3\u011a*/ "/\u011b""1)3\u011c*# "7\xac'),h('76*# "7\xad'),h('!/\u011d""1"3\u011e+-$76+#%\'"%$"# X"# X'),h('7\xac*# "76'),h('!76+7$7<+-%7\xb0+#%\'#%$## X$"# X"# X'),h('76*# "7H'),h('!7\xb2+7$7.+-%7\x8f+#%\'#%$## X$"# X"# X'),h('! \\7!+&$,#&7!""" X+\' 4!6\u011f!! %'),h("!7\x9d+' 4!6\u0120!! %"),h('!7\xb5+d$ \\!7B+-$7\x9f+#%\'"%$"# X"# X,8&!7B+-$7\x9f+#%\'"%$"# X"# X"+(%4"6\u0121"!!%$"# X"# X'),h('!!77+k$ \\!.J""2J3K+-$77+#%\'"%$"# X"# X,>&!.J""2J3K+-$77+#%\'"%$"# X"# X"+#%\'"%$"# X"# X+! (%'),h('!7L*# "7\x98+c$ \\!7B+-$7\xb7+#%\'"%$"# X"# X,8&!7B+-$7\xb7+#%\'"%$"# X"# X"+\'%4"6\u0122" %$"# X"# X'),h('7\xb8*# "7\x9f'),h('!/\u0123""1#3\u0124+<$7<+2%76+(%4#6\u0125#! %$## X$"# X"# X'),h('! \\7!+&$,#&7!""" X+\' 4!6\u0126!! %'),h("!7\x9d+' 4!6\u0127!! %"),h('! \\7\x99,#&7\x99"+\x81$7@+w%7M+m%7?+c% \\!7B+-$7\x9f+#%\'"%$"# X"# X,8&!7B+-$7\x9f+#%\'"%$"# X"# X"+\'%4%6\u0128% %$%# X$$# X$## X$"# X"# X'),h("7\xbd"),h('!/\u0129""1&3\u012a+s$7.+i%7\xc0+_% \\!7A+-$7\xc0+#%\'"%$"# X"# X,8&!7A+-$7\xc0+#%\'"%$"# X"# X"+#%\'$%$$# X$## X$"# X"# X*# "7\xbe'),h('!76+s$7.+i%7\xbf+_% \\!7A+-$7\xbf+#%\'"%$"# X"# X,8&!7A+-$7\xbf+#%\'"%$"# X"# X"+#%\'$%$$# X$## X$"# X"# X'),h('!76+=$7<+3%76*# "7H+#%\'#%$## X$"# X"# X'),h('7\xc1*G "7\xc3*A "7\xc5*; "7\xc7*5 "7\xc8*/ "7\xc9*) "7\xca*# "7\xbf'),h('!/\u012b""1%3\u012c+7$7<+-%7\xc2+#%\'#%$## X$"# X"# X'),h("!7I+' 4!6\u012d!! %"),h('!/\u012e""1&3\u012f+\xa5$7<+\x9b%7D+\x91%7\xc4+\x87% \\! \\7\'+&$,#&7\'""" X+-$7\xc4+#%\'"%$"# X"# X,G&! \\7\'+&$,#&7\'""" X+-$7\xc4+#%\'"%$"# X"# X"+-%7E+#%\'&%$&# X$%# X$$# X$## X$"# X"# X'),h('7t*# "7w'),h('!/\u0130""1%3\u0131+7$7<+-%7\xc6+#%\'#%$## X$"# X"# X'),h("!7I+' 4!6\u0132!! %"),h('!/\u0133""1&3\u0134+<$7<+2%7I+(%4#6\u0135#! %$## X$"# X"# X'),h('!/\u0136""1%3\u0137+_$7<+U%!/\u0138""1$3\u0139+& 4!6\u013a! %*4 "!/\u013b""1%3\u013c+& 4!6\u013d! %+#%\'#%$## X$"# X"# X'),h('!/\u013e""1)3\u013f+T$7<+J%/\u0140""1#3\u0141*/ "/\u0142""1(3\u0143*# "76+(%4#6\u0144#! %$## X$"# X"# X'),h('!/\u0145""1#3\u0146+\x9e$7<+\x94%7D+\x8a%!7\xcb+k$ \\!.D""2D3E+-$7\xcb+#%\'"%$"# X"# X,>&!.D""2D3E+-$7\xcb+#%\'"%$"# X"# X"+#%\'"%$"# X"# X+-%7E+#%\'%%$%# X$$# X$## X$"# X"# X'),h('!/\u0147""1(3\u0148*/ "/\u0149""1$3\u014a*# "76+\' 4!6\u014b!! %'),h('!76+_$ \\!7A+-$76+#%\'"%$"# X"# X,8&!7A+-$76+#%\'"%$"# X"# X"+#%\'"%$"# X"# X'),h('!7\xce+K$7.+A%7\xce+7%7.+-%7\x8f+#%\'%%$%# X$$# X$## X$"# X"# X'),h('! \\7!+&$,#&7!""" X+\' 4!6\u014c!! %'),h('!7\xd0+c$ \\!7A+-$7\xd0+#%\'"%$"# X"# X,8&!7A+-$7\xd0+#%\'"%$"# X"# X"+\'%4"6\u014d" %$"# X"# X'),h('!7\x98+c$ \\!7B+-$7\x9f+#%\'"%$"# X"# X,8&!7B+-$7\x9f+#%\'"%$"# X"# X"+\'%4"6\u014e" %$"# X"# X'),h('!7L*T "7\x98*N "!7@*# " [+=$7t+3%7?*# " [+#%\'#%$## X$"# X"# X+c$ \\!7B+-$7\x9f+#%\'"%$"# X"# X,8&!7B+-$7\x9f+#%\'"%$"# X"# X"+\'%4"6\u014f" %$"# X"# X'),h('!7\xd3+c$ \\!7B+-$7\xd4+#%\'"%$"# X"# X,8&!7B+-$7\xd4+#%\'"%$"# X"# X"+\'%4"6\u0150" %$"# X"# X'),h("!7\x95+& 4!6\u0151! %"),h('!/\u0152""1(3\u0153+<$7<+2%76+(%4#6\u0154#! %$## X$"# X"# X*j "!/\u0155""1&3\u0156+<$7<+2%76+(%4#6\u0157#! %$## X$"# X"# X*: "!/\u0158""1*3\u0159+& 4!6\u015a! %*# "7\x9f'),h('!!76+o$ \\!7A+2$76+(%4"6\u015b"! %$"# X"# X,=&!7A+2$76+(%4"6\u015b"! %$"# X"# X"+)%4"6\u015c""! %$"# X"# X*# " [+\' 4!6\u015d!! %'),h('!7\xd7+_$ \\!7A+-$7\xd7+#%\'"%$"# X"# X,8&!7A+-$7\xd7+#%\'"%$"# X"# X"+#%\'"%$"# X"# X'),h('!7\x98+_$ \\!7B+-$7\x9f+#%\'"%$"# X"# X,8&!7B+-$7\x9f+#%\'"%$"# X"# X"+#%\'"%$"# X"# X'),h('! \\7!+&$,#&7!""" X+\' 4!6\u015e!! %'),h('!7\xda+_$ \\!7B+-$7\xdb+#%\'"%$"# X"# X,8&!7B+-$7\xdb+#%\'"%$"# X"# X"+#%\'"%$"# X"# X'),h('!/\u015f""1&3\u0160*; "/\u0161""1\'3\u0162*/ "/\u0163""1*3\u0164*# "76+& 4!6\u0165! %'),h('!/\u0166""1&3\u0167+<$7<+2%7\xdc+(%4#6\u0168#! %$## X$"# X"# X*\x83 "!/\xf6""1\'3\xf7+<$7<+2%7\x9d+(%4#6\u0169#! %$## X$"# X"# X*S "!/\u016a""1+3\u016b+<$7<+2%7\x9d+(%4#6\u016c#! %$## X$"# X"# X*# "7\x9f'),h('/\u016d""1+3\u016e*k "/\u016f""1)3\u0170*_ "/\u0171""1(3\u0172*S "/\u0173""1\'3\u0174*G "/\u0175""1&3\u0176*; "/\u0177""1*3\u0178*/ "/\u0179""1)3\u017a*# "76'),h('71*# " ['),h('!!76+o$ \\!7A+2$76+(%4"6\u015b"! %$"# X"# X,=&!7A+2$76+(%4"6\u015b"! %$"# X"# X"+)%4"6\u015c""! %$"# X"# X*# " [+\' 4!6\u017b!! %'),h('!7L*# "7\x98+c$ \\!7B+-$7\xe0+#%\'"%$"# X"# X,8&!7B+-$7\xe0+#%\'"%$"# X"# X"+\'%4"6\u017c" %$"# X"# X'),h('7\xb8*# "7\x9f'),h('!7\xe2+_$ \\!7A+-$7\xe2+#%\'"%$"# X"# X,8&!7A+-$7\xe2+#%\'"%$"# X"# X"+#%\'"%$"# X"# X'),h('!7\xe9+s$7.+i%7\xec+_% \\!7B+-$7\xe3+#%\'"%$"# X"# X,8&!7B+-$7\xe3+#%\'"%$"# X"# X"+#%\'$%$$# X$## X$"# X"# X'),h('7\xe4*; "7\xe5*5 "7\xe6*/ "7\xe7*) "7\xe8*# "7\x9f'),h('!/\u017d""1#3\u017e+<$7<+2%7\xef+(%4#6\u017f#! %$## X$"# X"# X'),h('!/\u0180""1%3\u0181+<$7<+2%7T+(%4#6\u0182#! %$## X$"# X"# X'),h('!/\u0183""1(3\u0184+B$7<+8%7\\*# "7Y+(%4#6\u0185#! %$## X$"# X"# X'),h('!/\u0186""1&3\u0187+<$7<+2%76+(%4#6\u0188#! %$## X$"# X"# X'),h('!/\u0189""1%3\u018a+T$!7<+5$ \\7!,#&7!"+#%\'"%$"# X"# X*# " [+\'%4"6\u018b" %$"# X"# X'),h('!7\xea+K$7;+A%76+7%7;+-%7\xeb+#%\'%%$%# X$$# X$## X$"# X"# X'),h('!/\x95""1#3\xd6*# "76+\' 4!6\u018c!! %'),h('!/\xb4""1#3\u018d*G "/\xb6""1#3\u018e*; "/\xba""1#3\u018f*/ "/\xb8""1$3\u0190*# "76+\' 4!6\u0191!! %'),h('!7\xed+H$!7C+-$7\xee+#%\'"%$"# X"# X*# " [+#%\'"%$"# X"# X'),h('!7U*) "7\\*# "7X+& 4!6\u0192! %'),h('!!7!*# " [+c$7!*# " [+S%7!*# " [+C%7!*# " [+3%7!*# " [+#%\'%%$%# X$$# X$## X$"# X"# X+\' 4!6\u0193!! %'),h('!!7!+C$7!*# " [+3%7!*# " [+#%\'#%$## X$"# X"# X+\' 4!6\u0194!! %'),h("7\xbd"),h('!7\x9d+d$ \\!7B+-$7\xf2+#%\'"%$"# X"# X,8&!7B+-$7\xf2+#%\'"%$"# X"# X"+(%4"6\u0195"!!%$"# X"# X'),h('7\xf3*# "7\x9f'),h('!.\u0196""2\u01963\u0197+N$7<+D%.\u0198""2\u01983\u0199*) ".\u019a""2\u019a3\u019b+(%4#6\u019c#! %$## X$"# X"# X'),h('!7\x9d+d$ \\!7B+-$7\x9f+#%\'"%$"# X"# X,8&!7B+-$7\x9f+#%\'"%$"# X"# X"+(%4"6\u019d"!!%$"# X"# X'),h('!76+7$70+-%7\xf6+#%\'#%$## X$"# X"# X'),h(' \\72*) "74*# "7.,/&72*) "74*# "7."'),h(' \\7%,#&7%"'),h('!7\xf9+=$.8""2839+-%7\xfa+#%\'#%$## X$"# X"# X'),h('!/\u019e""1%3\u019f*) "/\u01a0""1$3\u01a1+\' 4!6\u01a2!! %'),h('!7\xfb+N$!.8""2839+-$7^+#%\'"%$"# X"# X*# " [+#%\'"%$"# X"# X'),h('!7\\*) "7X*# "7\x82+\' 4!6\u01a3!! %'),h('! \\7\xfd*) "7-*# "7\xfe,/&7\xfd*) "7-*# "7\xfe"+! (%'),h('7"*S "7!*M ".F""2F3G*A ".J""2J3K*5 ".H""2H3I*) ".N""2N3O'),h('.L""2L3M*\x95 ".B""2B3C*\x89 ".<""2<3=*} ".R""2R3S*q ".T""2T3U*e ".V""2V3W*Y ".P""2P3Q*M ".@""2@3A*A ".D""2D3E*5 ".2""2233*) ".>""2>3?'),h('!7\u0100+h$.8""2839+X%7\xfa+N%!.\u01a4""2\u01a43\u01a5+-$7\xeb+#%\'"%$"# X"# X*# " [+#%\'$%$$# X$## X$"# X"# X'),h('!/\u01a6""1%3\u01a7*) "/\u01a8""1$3\u01a9+\' 4!6\u01a2!! %'),h('!7\xeb+Q$/\xb4""1#3\xb5*7 "/\xb6""1#3\xb7*+ " \\7+,#&7+"+\'%4"6\u01aa" %$"# X"# X'),h('!7\u0104+\x8f$.F""2F3G+%7\u0103+u%.F""2F3G+e%7\u0103+[%.F""2F3G+K%7\u0103+A%.F""2F3G+1%7\u0105+\'%4)6\u01ab) %$)# X$(# X$\'# X$&# X$%# X$$# X$## X$"# X"# X'),h('!7#+A$7#+7%7#+-%7#+#%\'$%$$# X$## X$"# X"# X'),h('!7\u0103+-$7\u0103+#%\'"%$"# X"# X'),h('!7\u0103+7$7\u0103+-%7\u0103+#%\'#%$## X$"# X"# X')],r=0,s=0,t=0,u={line:1,column:1,seenCR:!1},v=0,w=[],x=0;if("startRule"in l){if(!(l.startRule in n))throw new Error("Can't start parsing from rule \""+l.startRule+'".');o=n[l.startRule]}if(l.data={},k=i(o),k!==m&&r===a.length)return k;throw k!==m&&r<a.length&&f({type:"end",description:"end of input"}),g(null,w,v)}return a(b,Error),{SyntaxError:b,parse:c}}()},{}],12:[function(a,b,c){"use strict";b.exports=function(a){a=a;var b={AllBrowsers:{maskDtls:function(a){return a&&(a=a.replace(/ UDP\/TLS\/RTP\/SAVP/gim," RTP/SAVP")),a},unmaskDtls:function(a){return a.replace(/ RTP\/SAVP/gim," UDP/TLS/RTP/SAVP")}},Firefox:{isFirefox:function(){return"undefined"!=typeof mozRTCPeerConnection},cannotHandleExtraWhitespace:function(a){return this.isFirefox()&&a&&(a=a.replace(/ \r\n/g,"\r\n")),a},hasMissingCLineInSDP:function(a){var b,c;if(a.indexOf("c=")>a.indexOf("m=")){c=a.match(/m=.*\r\n.*/g);for(var d=0;d<c.length;d++)c[d].toString().search(/i=.*/)>=0?(b=a.indexOf(c[d].toString())+c[d].toString().length,"c="!==a.substr(b,2)&&(a=a.substr(0,b)+"\r\nc=IN IP4 0.0.0.0"+a.substr(b))):c[d].toString().search(/c=.*/)<0&&(b=a.indexOf(c[d].toString().match(/.*/))+c[d].toString().match(/.*/).toString().length,a=a.substr(0,b)+"\r\nc=IN IP4 0.0.0.0"+a.substr(b))}return a}},Chrome:{needsExplicitlyInactiveSDP:function(a){var c,d;return b.Firefox.isFirefox()&&(d=a.indexOf("m=video 0"),-1!==d)?(c=a.substr(d),c=c.replace(/\r\nc=IN IP4.*\r\n$/,"\r\nc=IN IP4 0.0.0.0\r\na=inactive\r\n"),a.substr(0,d)+c):a},getsConfusedAboutGUM:function(a){a.mediaHandler&&a.mediaHandler.close()}}};return b}},{}],13:[function(a,b,c){"use strict";var d={error:0,warn:1,log:2,debug:3};b.exports=function(a){function b(a,b,c){this.logger=a,this.category=b,this.label=c}var c=function(){var a,b=2,c=!0,e=null;this.loggers={},a=this.getLogger("sip.loggerfactory"),Object.defineProperties(this,{builtinEnabled:{get:function(){return c},set:function(b){"boolean"==typeof b?c=b:a.error('invalid "builtinEnabled" parameter value: '+JSON.stringify(b))}},level:{get:function(){return b},set:function(c){c>=0&&3>=c?b=c:c>3?b=3:d.hasOwnProperty(c)?b=d[c]:a.error('invalid "level" parameter value: '+JSON.stringify(c))}},connector:{get:function(){return e},set:function(b){null===b||""===b||void 0===b?e=null:"function"==typeof b?e=b:a.error('invalid "connector" parameter value: '+JSON.stringify(b))}}})};return c.prototype.print=function(b,c,d,e){if("string"==typeof e){var f=[new Date,c];d&&f.push(d),e=f.concat(e).join(" | ")}b.call(a,e)},Object.keys(d).forEach(function(e){b.prototype[e]=function(a){this.logger[e](this.category,this.label,a)},c.prototype[e]=function(b,c,f){this.level>=d[e]&&(this.builtinEnabled&&this.print(a[e],b,c,f),this.connector&&this.connector(e,b,c,f))}}),c.prototype.getLogger=function(a,c){var d;return c&&3===this.level?new b(this,a,c):this.loggers[a]?this.loggers[a]:(d=new b(this,a),this.loggers[a]=d,d)},c}},{}],14:[function(a,b,c){"use strict";b.exports=function(a){var b=function(a,b){a=a,b=b};return b.prototype=Object.create(a.prototype,{isReady:{value:function(){}},close:{value:function(){}},getDescription:{value:function(a){a=a}},hasDescription:{value:function(a){a=a}},setDescription:{value:function(a){a=a}}}),b}},{}],15:[function(a,b,c){"use strict";b.exports=function(a){var b;b=function(b,c,d){var e;if(!(b&&b instanceof a.URI))throw new TypeError('missing or invalid "uri" parameter');this.uri=b,this.parameters={};for(e in d)this.setParam(e,d[e]);Object.defineProperties(this,{friendlyName:{get:function(){return this.displayName||b.aor}},displayName:{get:function(){return c},set:function(a){c=0===a?"0":a}}})},b.prototype={setParam:function(a,b){a&&(this.parameters[a.toLowerCase()]="undefined"==typeof b||null===b?null:b.toString())},getParam:a.URI.prototype.getParam,hasParam:a.URI.prototype.hasParam,deleteParam:a.URI.prototype.deleteParam,clearParams:a.URI.prototype.clearParams,clone:function(){return new b(this.uri.clone(),this.displayName,JSON.parse(JSON.stringify(this.parameters)))},toString:function(){var a,b;a=this.displayName||0===this.displayName?'"'+this.displayName+'" ':"",a+="<"+this.uri.toString()+">";for(b in this.parameters)a+=";"+b,null!==this.parameters[b]&&(a+="="+this.parameters[b]);return a}},b.parse=function(b){return b=a.Grammar.parse(b,"Name_Addr_Header"),-1!==b?b:void 0},a.NameAddrHeader=b}},{}],16:[function(a,b,c){"use strict";b.exports=function(a){function b(a,b){var c=b,d=0,e=0;if(a.substring(c,c+2).match(/(^\r\n)/))return-2;for(;0===d;){if(e=a.indexOf("\r\n",c),-1===e)return e;!a.substring(e+2,e+4).match(/(^\r\n)/)&&a.charAt(e+2).match(/(^\s+)/)?c=e+2:d=e}return d}function c(b,c,d,e){var f,g,h,i,j=c.indexOf(":",d),k=c.substring(d,j).trim(),l=c.substring(j+1,e).trim();switch(k.toLowerCase()){case"via":case"v":b.addHeader("via",l),1===b.getHeaders("via").length?(i=b.parseHeader("Via"),i&&(b.via=i,b.via_branch=i.branch)):i=0;break;case"from":case"f":b.setHeader("from",l),i=b.parseHeader("from"),i&&(b.from=i,b.from_tag=i.getParam("tag"));break;case"to":case"t":b.setHeader("to",l),i=b.parseHeader("to"),i&&(b.to=i,b.to_tag=i.getParam("tag"));break;case"record-route":
if(i=a.Grammar.parse(l,"Record_Route"),-1===i){i=void 0;break}for(h=i.length,g=0;h>g;g++)f=i[g],b.addHeader("record-route",l.substring(f.position,f.offset)),b.headers["Record-Route"][b.getHeaders("record-route").length-1].parsed=f.parsed;break;case"call-id":case"i":b.setHeader("call-id",l),i=b.parseHeader("call-id"),i&&(b.call_id=l);break;case"contact":case"m":if(i=a.Grammar.parse(l,"Contact"),-1===i){i=void 0;break}for(h=i.length,g=0;h>g;g++)f=i[g],b.addHeader("contact",l.substring(f.position,f.offset)),b.headers.Contact[b.getHeaders("contact").length-1].parsed=f.parsed;break;case"content-length":case"l":b.setHeader("content-length",l),i=b.parseHeader("content-length");break;case"content-type":case"c":b.setHeader("content-type",l),i=b.parseHeader("content-type");break;case"cseq":b.setHeader("cseq",l),i=b.parseHeader("cseq"),i&&(b.cseq=i.value),b instanceof a.IncomingResponse&&(b.method=i.method);break;case"max-forwards":b.setHeader("max-forwards",l),i=b.parseHeader("max-forwards");break;case"www-authenticate":b.setHeader("www-authenticate",l),i=b.parseHeader("www-authenticate");break;case"proxy-authenticate":b.setHeader("proxy-authenticate",l),i=b.parseHeader("proxy-authenticate");break;case"refer-to":case"r":b.setHeader("refer-to",l),i=b.parseHeader("refer-to"),i&&(b.refer_to=i);break;default:b.setHeader(k,l),i=0}return void 0===i?{error:'error parsing header "'+k+'"'}:!0}var d;d={},d.parseMessage=function(d,e){var f,g,h,i,j,k=0,l=d.indexOf("\r\n"),m=e.getLogger("sip.parser");if(-1===l)return void m.warn("no CRLF found, not a SIP message, discarded");if(g=d.substring(0,l),j=a.Grammar.parse(g,"Request_Response"),-1===j)return void m.warn('error parsing first line of SIP message: "'+g+'"');for(j.status_code?(f=new a.IncomingResponse(e),f.status_code=j.status_code,f.reason_phrase=j.reason_phrase):(f=new a.IncomingRequest(e),f.method=j.method,f.ruri=j.uri),f.data=d,k=l+2;;){if(l=b(d,k),-2===l){i=k+2;break}if(-1===l)return void m.error("malformed message");if(j=c(f,d,k,l),j!==!0)return void m.error(j.error);k=l+2}return f.hasHeader("content-length")?(h=f.getHeader("content-length"),f.body=d.substr(i,h)):f.body=d.substring(i),f},a.Parser=d}},{}],17:[function(a,b,c){"use strict";b.exports=function(a){var b;b=function(b){var c={},d=1;this.registrar=b.configuration.registrarServer,this.expires=b.configuration.registerExpires,this.contact=b.contact.toString(),d&&(this.contact+=";reg-id="+d,this.contact+=';+sip.instance="<urn:uuid:'+b.configuration.instanceId+'>"'),this.call_id=a.Utils.createRandomToken(22),this.cseq=80,this.to_uri=b.configuration.uri,c.to_uri=this.to_uri,c.to_displayName=b.configuration.displayName,c.call_id=this.call_id,c.cseq=this.cseq,a.Utils.augment(this,a.ClientContext,[b,"REGISTER",this.registrar,{params:c}]),this.registrationTimer=null,this.registrationExpiredTimer=null,this.registered=!1,this.logger=b.getLogger("sip.registercontext")},b.prototype={register:function(b){var c,d=this;this.options=b||{},c=(this.options.extraHeaders||[]).slice(),c.push("Contact: "+this.contact+";expires="+this.expires),c.push("Allow: "+a.UA.C.ALLOWED_METHODS.toString()),this.closeHeaders=this.options.closeWithHeaders?(this.options.extraHeaders||[]).slice():[],this.receiveResponse=function(b){var c,e,f,g=b.getHeaders("contact").length;if(b.cseq===this.cseq)switch(null!==this.registrationTimer&&(a.Timers.clearTimeout(this.registrationTimer),this.registrationTimer=null),!0){case/^1[0-9]{2}$/.test(b.status_code):this.emit("progress",b);break;case/^2[0-9]{2}$/.test(b.status_code):if(this.emit("accepted",b),b.hasHeader("expires")&&(e=b.getHeader("expires")),null!==this.registrationExpiredTimer&&(a.Timers.clearTimeout(this.registrationExpiredTimer),this.registrationExpiredTimer=null),!g){this.logger.warn("no Contact header in response to REGISTER, response ignored");break}for(;g--;){if(c=b.parseHeader("contact",g),c.uri.user===this.ua.contact.uri.user){e=c.getParam("expires");break}c=null}if(!c){this.logger.warn("no Contact header pointing to us, response ignored");break}e||(e=this.expires),this.registrationTimer=a.Timers.setTimeout(function(){d.registrationTimer=null,d.register(d.options)},1e3*e-3e3),this.registrationExpiredTimer=a.Timers.setTimeout(function(){d.logger.warn("registration expired"),d.registered&&d.unregistered(null,a.C.causes.EXPIRES)},1e3*e),c.hasParam("temp-gruu")&&(this.ua.contact.temp_gruu=a.URI.parse(c.getParam("temp-gruu").replace(/"/g,""))),c.hasParam("pub-gruu")&&(this.ua.contact.pub_gruu=a.URI.parse(c.getParam("pub-gruu").replace(/"/g,""))),this.registered=!0,this.emit("registered",b||null);break;case/^423$/.test(b.status_code):b.hasHeader("min-expires")?(this.expires=b.getHeader("min-expires"),this.register(this.options)):(this.logger.warn("423 response received for REGISTER without Min-Expires"),this.registrationFailure(b,a.C.causes.SIP_FAILURE_CODE));break;default:f=a.Utils.sipErrorCause(b.status_code),this.registrationFailure(b,f)}},this.onRequestTimeout=function(){this.registrationFailure(null,a.C.causes.REQUEST_TIMEOUT)},this.onTransportError=function(){this.registrationFailure(null,a.C.causes.CONNECTION_ERROR)},this.cseq++,this.request.cseq=this.cseq,this.request.setHeader("cseq",this.cseq+" REGISTER"),this.request.extraHeaders=c,this.send()},registrationFailure:function(a,b){this.emit("failed",a||null,b||null)},onTransportClosed:function(){this.registered_before=this.registered,null!==this.registrationTimer&&(a.Timers.clearTimeout(this.registrationTimer),this.registrationTimer=null),null!==this.registrationExpiredTimer&&(a.Timers.clearTimeout(this.registrationExpiredTimer),this.registrationExpiredTimer=null),this.registered&&this.unregistered(null,a.C.causes.CONNECTION_ERROR)},onTransportConnected:function(){this.register(this.options)},close:function(){var a={all:!1,extraHeaders:this.closeHeaders};this.registered_before=this.registered,this.unregister(a)},unregister:function(b){var c;return b=b||{},this.registered||b.all?(c=(b.extraHeaders||[]).slice(),this.registered=!1,null!==this.registrationTimer&&(a.Timers.clearTimeout(this.registrationTimer),this.registrationTimer=null),b.all?(c.push("Contact: *"),c.push("Expires: 0")):c.push("Contact: "+this.contact+";expires=0"),this.receiveResponse=function(b){var c;switch(!0){case/^1[0-9]{2}$/.test(b.status_code):this.emit("progress",b);break;case/^2[0-9]{2}$/.test(b.status_code):this.emit("accepted",b),null!==this.registrationExpiredTimer&&(a.Timers.clearTimeout(this.registrationExpiredTimer),this.registrationExpiredTimer=null),this.unregistered(b);break;default:c=a.Utils.sipErrorCause(b.status_code),this.unregistered(b,c)}},this.onRequestTimeout=function(){},this.onTransportError=function(){},this.cseq++,this.request.cseq=this.cseq,this.request.setHeader("cseq",this.cseq+" REGISTER"),this.request.extraHeaders=c,void this.send()):void this.logger.warn("already unregistered")},unregistered:function(a,b){this.registered=!1,this.emit("unregistered",a||null,b||null)}},a.RegisterContext=b}},{}],18:[function(a,b,c){"use strict";b.exports=function(a){var b;b=function(b,c){this.logger=c.getLogger("sip.requestsender"),this.ua=c,this.applicant=b,this.method=b.request.method,this.request=b.request,this.credentials=null,this.challenged=!1,this.staled=!1,c.status!==a.UA.C.STATUS_USER_CLOSED||this.method===a.C.BYE&&this.method===a.C.ACK||this.onTransportError()},b.prototype={send:function(){switch(this.method){case"INVITE":this.clientTransaction=new a.Transactions.InviteClientTransaction(this,this.request,this.ua.transport);break;case"ACK":this.clientTransaction=new a.Transactions.AckClientTransaction(this,this.request,this.ua.transport);break;default:this.clientTransaction=new a.Transactions.NonInviteClientTransaction(this,this.request,this.ua.transport)}return this.clientTransaction.send(),this.clientTransaction},onRequestTimeout:function(){this.applicant.onRequestTimeout()},onTransportError:function(){this.applicant.onTransportError()},receiveResponse:function(b){var c,d,e,f=b.status_code;if(401===f||407===f){if(401===b.status_code?(d=b.parseHeader("www-authenticate"),e="authorization"):(d=b.parseHeader("proxy-authenticate"),e="proxy-authorization"),!d)return this.logger.warn(b.status_code+" with wrong or missing challenge, cannot authenticate"),void this.applicant.receiveResponse(b);if(!this.challenged||!this.staled&&d.stale===!0){if(this.credentials||(this.credentials=this.ua.configuration.authenticationFactory(this.ua)),!this.credentials.authenticate(this.request,d))return void this.applicant.receiveResponse(b);this.challenged=!0,d.stale&&(this.staled=!0),b.method===a.C.REGISTER?c=this.applicant.cseq+=1:this.request.dialog?c=this.request.dialog.local_seqnum+=1:(c=this.request.cseq+1,this.request.cseq=c),this.request.setHeader("cseq",c+" "+this.method),this.request.setHeader(e,this.credentials.toString()),this.send()}else this.applicant.receiveResponse(b)}else this.applicant.receiveResponse(b)}},a.RequestSender=b}},{}],19:[function(a,b,c){"use strict";b.exports=function(b){var c=a("../package.json"),d=c.version,e=c.title,f=Object.defineProperties({},{version:{get:function(){return d}},name:{get:function(){return e}}});return a("./Utils")(f,b),f.LoggerFactory=a("./LoggerFactory")(b.console),f.EventEmitter=a("./EventEmitter")(b.console),f.C=a("./Constants")(f.name,f.version),f.Exceptions=a("./Exceptions"),f.Timers=a("./Timers")(b.timers),f.Transport=b.Transport(f,b.WebSocket),a("./Parser")(f),a("./SIPMessage")(f),a("./URI")(f),a("./NameAddrHeader")(f),a("./Transactions")(f),a("./Dialogs")(f),a("./RequestSender")(f),a("./RegisterContext")(f),f.MediaHandler=a("./MediaHandler")(f.EventEmitter),a("./ClientContext")(f),a("./ServerContext")(f),a("./Session")(f,b),a("./Subscription")(f),f.WebRTC=a("./WebRTC")(f,b),a("./UA")(f,b),f.Hacks=a("./Hacks")(f),a("./SanityCheck")(f),f.DigestAuthentication=a("./DigestAuthentication")(f.Utils),f.Grammar=a("./Grammar")(f),f}},{"../package.json":2,"./ClientContext":3,"./Constants":4,"./Dialogs":6,"./DigestAuthentication":7,"./EventEmitter":8,"./Exceptions":9,"./Grammar":10,"./Hacks":12,"./LoggerFactory":13,"./MediaHandler":14,"./NameAddrHeader":15,"./Parser":16,"./RegisterContext":17,"./RequestSender":18,"./SIPMessage":20,"./SanityCheck":21,"./ServerContext":22,"./Session":23,"./Subscription":25,"./Timers":26,"./Transactions":27,"./UA":29,"./URI":30,"./Utils":31,"./WebRTC":32}],20:[function(a,b,c){"use strict";b.exports=function(a){function b(b){var c=b.ua.configuration.hackAllowUnregisteredOptionTags,d=[],e={};return b.method===a.C.REGISTER?d.push("path","gruu"):b.method===a.C.INVITE&&(b.ua.contact.pub_gruu||b.ua.contact.temp_gruu)&&d.push("gruu"),b.ua.configuration.rel100===a.C.supported.SUPPORTED&&d.push("100rel"),b.ua.configuration.replaces===a.C.supported.SUPPORTED&&d.push("replaces"),d.push("outbound"),d=d.concat(b.ua.configuration.extraSupported),d=d.filter(function(b){var d=a.C.OPTION_TAGS[b],f=!e[b];return e[b]=!0,(d||c)&&f}),"Supported: "+d.join(", ")+"\r\n"}var c,d,e,f;c=function(b,c,d,e,f,g){var h,i,j,k,l,m;return e=e||{},b&&c&&d?(this.logger=d.getLogger("sip.sipmessage"),this.ua=d,this.headers={},this.method=b,this.ruri=c,this.body=g,this.extraHeaders=(f||[]).slice(),this.statusCode=e.status_code,this.reasonPhrase=e.reason_phrase,e.route_set?this.setHeader("route",e.route_set):d.configuration.usePreloadedRoute&&this.setHeader("route",d.transport.server.sip_uri),this.setHeader("via",""),this.setHeader("max-forwards",a.UA.C.MAX_FORWARDS),l=e.to_uri||c,h=e.to_displayName||0===e.to_displayName?'"'+e.to_displayName+'" ':"",h+="<"+(l&&l.toRaw?l.toRaw():l)+">",h+=e.to_tag?";tag="+e.to_tag:"",this.to=new a.NameAddrHeader.parse(h),this.setHeader("to",h),m=e.from_uri||d.configuration.uri,i=e.from_displayName||0===e.from_displayName?'"'+e.from_displayName+'" ':d.configuration.displayName?'"'+d.configuration.displayName+'" ':"",i+="<"+(m&&m.toRaw?m.toRaw():m)+">;tag=",i+=e.from_tag||a.Utils.newTag(),this.from=new a.NameAddrHeader.parse(i),this.setHeader("from",i),j=e.call_id||d.configuration.sipjsId+a.Utils.createRandomToken(15),this.call_id=j,this.setHeader("call-id",j),k=e.cseq||Math.floor(1e4*Math.random()),this.cseq=k,void this.setHeader("cseq",k+" "+b)):null},c.prototype={setHeader:function(b,c){this.headers[a.Utils.headerize(b)]=c instanceof Array?c:[c]},getHeader:function(b){var c,d,e=this.extraHeaders.length,f=this.headers[a.Utils.headerize(b)];if(f){if(f[0])return f[0]}else for(c=new RegExp("^\\s*"+b+"\\s*:","i"),d=0;e>d;d++)if(f=this.extraHeaders[d],c.test(f))return f.substring(f.indexOf(":")+1).trim()},getHeaders:function(b){var c,d,e,f=this.headers[a.Utils.headerize(b)],g=[];if(f){for(d=f.length,c=0;d>c;c++)g.push(f[c]);return g}for(d=this.extraHeaders.length,e=new RegExp("^\\s*"+b+"\\s*:","i"),c=0;d>c;c++)f=this.extraHeaders[c],e.test(f)&&g.push(f.substring(f.indexOf(":")+1).trim());return g},hasHeader:function(b){var c,d,e=this.extraHeaders.length;if(this.headers[a.Utils.headerize(b)])return!0;for(c=new RegExp("^\\s*"+b+"\\s*:","i"),d=0;e>d;d++)if(c.test(this.extraHeaders[d]))return!0;return!1},toString:function(){var c,d,e,f="";f+=this.method+" "+(this.ruri.toRaw?this.ruri.toRaw():this.ruri)+" SIP/2.0\r\n";for(c in this.headers)for(d=this.headers[c].length,e=0;d>e;e++)f+=c+": "+this.headers[c][e]+"\r\n";for(d=this.extraHeaders.length,e=0;d>e;e++)f+=this.extraHeaders[e].trim()+"\r\n";return f+=b(this),f+="User-Agent: "+this.ua.configuration.userAgentString+"\r\n",this.body?"string"==typeof this.body?(d=a.Utils.str_utf8_length(this.body),f+="Content-Length: "+d+"\r\n\r\n",f+=this.body):this.body.body&&this.body.contentType?(d=a.Utils.str_utf8_length(this.body.body),f+="Content-Type: "+this.body.contentType+"\r\n",f+="Content-Length: "+d+"\r\n\r\n",f+=this.body.body):f+="Content-Length: 0\r\n\r\n":f+="Content-Length: 0\r\n\r\n",f}},d=function(){this.data=null,this.headers=null,this.method=null,this.via=null,this.via_branch=null,this.call_id=null,this.cseq=null,this.from=null,this.from_tag=null,this.to=null,this.to_tag=null,this.body=null},d.prototype={addHeader:function(b,c){var d={raw:c};b=a.Utils.headerize(b),this.headers[b]?this.headers[b].push(d):this.headers[b]=[d]},getHeader:function(b){var c=this.headers[a.Utils.headerize(b)];if(c)return c[0]?c[0].raw:void 0},getHeaders:function(b){var c,d,e=this.headers[a.Utils.headerize(b)],f=[];if(!e)return[];for(d=e.length,c=0;d>c;c++)f.push(e[c].raw);return f},hasHeader:function(b){return this.headers[a.Utils.headerize(b)]?!0:!1},parseHeader:function(b,c){var d,e,f;return b=a.Utils.headerize(b),c=c||0,this.headers[b]?c>=this.headers[b].length?void this.logger.log('not so many "'+b+'" headers present'):(d=this.headers[b][c],e=d.raw,d.parsed?d.parsed:(f=a.Grammar.parse(e,b.replace(/-/g,"_")),-1===f?(this.headers[b].splice(c,1),void this.logger.warn('error parsing "'+b+'" header field with value "'+e+'"')):(d.parsed=f,f))):void this.logger.log('header "'+b+'" not present')},s:function(a,b){return this.parseHeader(a,b)},setHeader:function(b,c){var d={raw:c};this.headers[a.Utils.headerize(b)]=[d]},toString:function(){return this.data}},e=function(a){this.logger=a.getLogger("sip.sipmessage"),this.ua=a,this.headers={},this.ruri=null,this.transport=null,this.server_transaction=null},e.prototype=new d,e.prototype.reply=function(c,d,e,f,g,h){var i,j,k,l,m,n=this.getHeader("To"),o=0,p=0;if(m=a.Utils.buildStatusLine(c,d),e=(e||[]).slice(),this.method===a.C.INVITE&&c>100&&200>=c)for(i=this.getHeaders("record-route"),k=i.length,o;k>o;o++)m+="Record-Route: "+i[o]+"\r\n";for(j=this.getHeaders("via"),k=j.length,p;k>p;p++)m+="Via: "+j[p]+"\r\n";for(!this.to_tag&&c>100?n+=";tag="+a.Utils.newTag():this.to_tag&&!this.s("to").hasParam("tag")&&(n+=";tag="+this.to_tag),m+="To: "+n+"\r\n",m+="From: "+this.getHeader("From")+"\r\n",m+="Call-ID: "+this.call_id+"\r\n",m+="CSeq: "+this.cseq+" "+this.method+"\r\n",k=e.length,l=0;k>l;l++)m+=e[l].trim()+"\r\n";return m+=b(this),m+="User-Agent: "+this.ua.configuration.userAgentString+"\r\n",f?"string"==typeof f?(k=a.Utils.str_utf8_length(f),m+="Content-Type: application/sdp\r\n",m+="Content-Length: "+k+"\r\n\r\n",m+=f):f.body&&f.contentType?(k=a.Utils.str_utf8_length(f.body),m+="Content-Type: "+f.contentType+"\r\n",m+="Content-Length: "+k+"\r\n\r\n",m+=f.body):m+="Content-Length: 0\r\n\r\n":m+="Content-Length: 0\r\n\r\n",this.server_transaction.receiveResponse(c,m).then(g,h),m},e.prototype.reply_sl=function(b,c){var d,e,f=0,g=this.getHeaders("via"),h=g.length;for(e=a.Utils.buildStatusLine(b,c),f;h>f;f++)e+="Via: "+g[f]+"\r\n";d=this.getHeader("To"),!this.to_tag&&b>100?d+=";tag="+a.Utils.newTag():this.to_tag&&!this.s("to").hasParam("tag")&&(d+=";tag="+this.to_tag),e+="To: "+d+"\r\n",e+="From: "+this.getHeader("From")+"\r\n",e+="Call-ID: "+this.call_id+"\r\n",e+="CSeq: "+this.cseq+" "+this.method+"\r\n",e+="User-Agent: "+this.ua.configuration.userAgentString+"\r\n",e+="Content-Length: 0\r\n\r\n",this.transport.send(e)},f=function(a){this.logger=a.getLogger("sip.sipmessage"),this.headers={},this.status_code=null,this.reason_phrase=null},f.prototype=new d,a.OutgoingRequest=c,a.IncomingRequest=e,a.IncomingResponse=f}},{}],21:[function(a,b,c){"use strict";b.exports=function(a){function b(b,c,d){var e,f=a.Utils.buildStatusLine(b),g=c.getHeaders("via"),h=g.length,i=0;for(i;h>i;i++)f+="Via: "+g[i]+"\r\n";e=c.getHeader("To"),c.to_tag||(e+=";tag="+a.Utils.newTag()),f+="To: "+e+"\r\n",f+="From: "+c.getHeader("From")+"\r\n",f+="Call-ID: "+c.call_id+"\r\n",f+="CSeq: "+c.cseq+" "+c.method+"\r\n",f+="\r\n",d.send(f)}function c(a,c,d){return a.ruri&&"sip"===a.ruri.scheme?void 0:(b(416,a,d),!1)}function d(a,c,d){return a.to_tag||a.call_id.substr(0,5)!==c.configuration.sipjsId?void 0:(b(482,a,d),!1)}function e(c,d,e){var f=a.Utils.str_utf8_length(c.body),g=c.getHeader("content-length");return g>f?(b(400,c,e),!1):void 0}function f(c,d,e){var f,g,h=c.from_tag,i=c.call_id,j=c.cseq;if(!c.to_tag)if(c.method===a.C.INVITE){if(f=d.transactions.ist[c.via_branch])return;for(g in d.transactions.ist)if(f=d.transactions.ist[g],f.request.from_tag===h&&f.request.call_id===i&&f.request.cseq===j)return b(482,c,e),!1}else{if(f=d.transactions.nist[c.via_branch])return;for(g in d.transactions.nist)if(f=d.transactions.nist[g],f.request.from_tag===h&&f.request.call_id===i&&f.request.cseq===j)return b(482,c,e),!1}}function g(a,b){return a.getHeaders("via").length>1?(b.getLogger("sip.sanitycheck").warn("More than one Via header field present in the response. Dropping the response"),!1):void 0}function h(a,b){var c=b.configuration.viaHost;return a.via.host!==c||void 0!==a.via.port?(b.getLogger("sip.sanitycheck").warn("Via sent-by in the response does not match UA Via host value. Dropping the response"),!1):void 0}function i(b,c){var d=a.Utils.str_utf8_length(b.body),e=b.getHeader("content-length");return e>d?(c.getLogger("sip.sanitycheck").warn("Message body length is lower than the value in Content-Length header field. Dropping the response"),!1):void 0}function j(a,b){for(var c=["from","to","call_id","cseq","via"],d=c.length;d--;)if(!a.hasHeader(c[d]))return b.getLogger("sip.sanitycheck").warn("Missing mandatory header field : "+c[d]+". Dropping the response"),!1}var k,l=[],m=[],n=[];l.push(c),l.push(d),l.push(e),l.push(f),m.push(g),m.push(h),m.push(i),n.push(j),k=function(b,c,d){var e,f;for(e=n.length;e--;)if(f=n[e](b,c,d),f===!1)return!1;if(b instanceof a.IncomingRequest){for(e=l.length;e--;)if(f=l[e](b,c,d),f===!1)return!1}else if(b instanceof a.IncomingResponse)for(e=m.length;e--;)if(f=m[e](b,c,d),f===!1)return!1;return!0},a.sanityCheck=k}},{}],22:[function(a,b,c){"use strict";b.exports=function(a){var b;b=function(b,c){this.ua=b,this.logger=b.getLogger("sip.servercontext"),this.request=c,c.method===a.C.INVITE?this.transaction=new a.Transactions.InviteServerTransaction(c,b):this.transaction=new a.Transactions.NonInviteServerTransaction(c,b),c.body&&(this.body=c.body),c.hasHeader("Content-Type")&&(this.contentType=c.getHeader("Content-Type")),this.method=c.method,this.data={},this.localIdentity=c.to,this.remoteIdentity=c.from},b.prototype=Object.create(a.EventEmitter.prototype),b.prototype.progress=function(a){return a=Object.create(a||Object.prototype),a.statusCode||(a.statusCode=180),a.minCode=100,a.maxCode=199,a.events=["progress"],this.reply(a)},b.prototype.accept=function(a){return a=Object.create(a||Object.prototype),a.statusCode||(a.statusCode=200),a.minCode=200,a.maxCode=299,a.events=["accepted"],this.reply(a)},b.prototype.reject=function(a){return a=Object.create(a||Object.prototype),a.statusCode||(a.statusCode=480),a.minCode=300,a.maxCode=699,a.events=["rejected","failed"],this.reply(a)},b.prototype.reply=function(b){b=b||{};var c,d=b.statusCode||100,e=b.minCode||100,f=b.maxCode||699,g=a.Utils.getReasonPhrase(d,b.reasonPhrase),h=b.extraHeaders||[],i=b.body,j=b.events||[];if(e>d||d>f)throw new TypeError("Invalid statusCode: "+d);return c=this.request.reply(d,g,h,i),j.forEach(function(a){this.emit(a,c,g)},this),this},b.prototype.onRequestTimeout=function(){this.emit("failed",null,a.C.causes.REQUEST_TIMEOUT)},b.prototype.onTransportError=function(){this.emit("failed",null,a.C.causes.CONNECTION_ERROR)},a.ServerContext=b}},{}],23:[function(a,b,c){"use strict";b.exports=function(b,c){var d,e,f,g=a("./Session/DTMF")(b),h={STATUS_NULL:0,STATUS_INVITE_SENT:1,STATUS_1XX_RECEIVED:2,STATUS_INVITE_RECEIVED:3,STATUS_WAITING_FOR_ANSWER:4,STATUS_ANSWERED:5,STATUS_WAITING_FOR_PRACK:6,STATUS_WAITING_FOR_ACK:7,STATUS_CANCELED:8,STATUS_TERMINATED:9,STATUS_ANSWERED_WAITING_FOR_PRACK:10,STATUS_EARLY_MEDIA:11,STATUS_CONFIRMED:12};d=function(a){this.status=h.STATUS_NULL,this.dialog=null,this.earlyDialogs={},this.mediaHandlerFactory=a||b.WebRTC.MediaHandler.defaultFactory,this.hasOffer=!1,this.hasAnswer=!1,this.timers={ackTimer:null,expiresTimer:null,invite2xxTimer:null,userNoAnswerTimer:null,rel1xxTimer:null,prackTimer:null},this.startTime=null,this.endTime=null,this.tones=null,this.local_hold=!1,this.remote_hold=!1,this.pending_actions={actions:[],length:function(){return this.actions.length},isPending:function(a){var b=0,c=this.actions.length;for(b;c>b;b++)if(this.actions[b].name===a)return!0;return!1},shift:function(){return this.actions.shift()},push:function(a){this.actions.push({name:a})},pop:function(a){var b=0,c=this.actions.length;for(b;c>b;b++)this.actions[b].name===a&&(this.actions.splice(b,1),c--,b--)}},this.early_sdp=null,this.rel100=b.C.supported.UNSUPPORTED},d.prototype={dtmf:function(a,c){var d,e=[],f=this;if(c=c||{},void 0===a)throw new TypeError("Not enough arguments");if(this.status!==h.STATUS_CONFIRMED&&this.status!==h.STATUS_WAITING_FOR_ACK)throw new b.Exceptions.InvalidStateError(this.status);if("string"!=typeof a&&"number"!=typeof a||!a.toString().match(/^[0-9A-D#*,]+$/i))throw new TypeError("Invalid tones: "+a);for(a=a.toString().split("");a.length>0;)e.push(new g(this,a.shift(),c));if(this.tones)return this.tones=this.tones.concat(e),this;var i=function(){var a,e;return f.status!==h.STATUS_TERMINATED&&f.tones&&0!==f.tones.length?(a=f.tones.shift(),","===d?e=2e3:(a.on("failed",function(){f.tones=null}),a.send(c),e=a.duration+a.interToneGap),void b.Timers.setTimeout(i,e)):(f.tones=null,this)};return this.tones=e,i(),this},bye:function(a){a=Object.create(a||Object.prototype);var c=a.statusCode;if(this.status===h.STATUS_TERMINATED)return this.logger.error("Error: Attempted to send BYE in a terminated session."),this;if(this.logger.log("terminating Session"),c&&(200>c||c>=700))throw new TypeError("Invalid statusCode: "+c);return a.receiveResponse=function(){},this.sendRequest(b.C.BYE,a).terminated()},refer:function(a,c){c=c||{};var d=(c.extraHeaders||[]).slice(),e=a instanceof b.InviteServerContext||a instanceof b.InviteClientContext,f=a;if(void 0===a)throw new TypeError("Not enough arguments");if(this.status!==h.STATUS_CONFIRMED)throw new b.Exceptions.InvalidStateError(this.status);if(e)a='"'+a.remoteIdentity.friendlyName+'" <'+a.dialog.remote_target.toString()+"?Replaces="+a.dialog.id.call_id+"%3Bto-tag%3D"+a.dialog.id.remote_tag+"%3Bfrom-tag%3D"+a.dialog.id.local_tag+">";else{try{a=b.Grammar.parse(a,"Refer_To").uri||a}catch(g){this.logger.debug(".refer() cannot parse Refer_To from",a),this.logger.debug("...falling through to normalizeTarget()")}if(a=this.ua.normalizeTarget(a),!a)throw new TypeError("Invalid target: "+f)}return d.push("Contact: "+this.contact),d.push("Allow: "+b.UA.C.ALLOWED_METHODS.toString()),d.push("Refer-To: "+a),this.sendRequest(b.C.REFER,{extraHeaders:d,body:c.body,receiveResponse:function(b){/^2[0-9]{2}$/.test(b.status_code)&&(e||a.scheme&&a.scheme.match("^sips?$"))&&this.terminate()}.bind(this)}),this},followRefer:function(a){return function(a,d){var e=d.parseHeader("refer-to"),f=e.uri;if(!f.scheme.match("^sips?$")){var g=f.toString();return void("function"==typeof c.open?c.open(g):this.logger.warn("referred to non-SIP URI but `open` isn't in the environment: "+g))}var h=[],i=f.getHeader("Replaces");void 0!==i&&h.push("Replaces: "+decodeURIComponent(i)),f.clearHeaders();var j=this.mediaHandler.getReferMedia,k=j?j.call(this.mediaHandler):this.mediaHint;b.Hacks.Chrome.getsConfusedAboutGUM(this);var l=this.ua.invite(f,{media:k,params:{to_displayName:e.friendlyName},extraHeaders:h});a.call(this,d,l),this.terminate()}.bind(this,a)},sendRequest:function(a,c){c=c||{};var d=this,e=new b.OutgoingRequest(a,this.dialog.remote_target,this.ua,{cseq:c.cseq||(this.dialog.local_seqnum+=1),call_id:this.dialog.id.call_id,from_uri:this.dialog.local_uri,from_tag:this.dialog.id.local_tag,to_uri:this.dialog.remote_uri,to_tag:this.dialog.id.remote_tag,route_set:this.dialog.route_set,statusCode:c.statusCode,reasonPhrase:c.reasonPhrase},c.extraHeaders||[],c.body);return new b.RequestSender({request:e,onRequestTimeout:function(){d.onRequestTimeout()},onTransportError:function(){d.onTransportError()},receiveResponse:c.receiveResponse||function(a){d.receiveNonInviteResponse(a)}},this.ua).send(),this.emit(a.toLowerCase(),e),this},close:function(){var a;if(this.status===h.STATUS_TERMINATED)return this;this.logger.log("closing INVITE session "+this.id),this.mediaHandler&&this.mediaHandler.close();for(a in this.timers)b.Timers.clearTimeout(this.timers[a]);this.dialog&&(this.dialog.terminate(),delete this.dialog);for(a in this.earlyDialogs)this.earlyDialogs[a].terminate(),delete this.earlyDialogs[a];return this.status=h.STATUS_TERMINATED,delete this.ua.sessions[this.id],this},createDialog:function(a,c,d){var e,f,g=a["UAS"===c?"to_tag":"from_tag"],h=a["UAS"===c?"from_tag":"to_tag"],i=a.call_id+g+h;if(f=this.earlyDialogs[i],d)return f?!0:(f=new b.Dialog(this,a,c,b.Dialog.C.STATUS_EARLY),f.error?(this.logger.error(f.error),this.failed(a,b.C.causes.INTERNAL_ERROR),!1):(this.earlyDialogs[i]=f,!0));if(f){f.update(a,c),this.dialog=f,delete this.earlyDialogs[i];for(var j in this.earlyDialogs)this.earlyDialogs[j].terminate(),delete this.earlyDialogs[j];return!0}return e=new b.Dialog(this,a,c),e.error?(this.logger.error(e.error),this.failed(a,b.C.causes.INTERNAL_ERROR),!1):(this.to_tag=a.to_tag,this.dialog=e,!0)},isReadyToReinvite:function(){return this.mediaHandler.isReady()&&!this.dialog.uac_pending_reply&&!this.dialog.uas_pending_reply},mute:function(a){var b=this.mediaHandler.mute(a);b&&this.onmute(b)},unmute:function(a){var b=this.mediaHandler.unmute(a);b&&this.onunmute(b)},hold:function(a){if(this.status!==h.STATUS_WAITING_FOR_ACK&&this.status!==h.STATUS_CONFIRMED)throw new b.Exceptions.InvalidStateError(this.status);return this.mediaHandler.hold(),this.isReadyToReinvite()?void(this.local_hold!==!0&&(this.onhold("local"),this.sendReinvite(a))):void(this.pending_actions.isPending("unhold")?this.pending_actions.pop("unhold"):this.pending_actions.isPending("hold")||this.pending_actions.push("hold"))},unhold:function(a){if(this.status!==h.STATUS_WAITING_FOR_ACK&&this.status!==h.STATUS_CONFIRMED)throw new b.Exceptions.InvalidStateError(this.status);return this.mediaHandler.unhold(),this.isReadyToReinvite()?void(this.local_hold!==!1&&(this.onunhold("local"),this.sendReinvite(a))):void(this.pending_actions.isPending("hold")?this.pending_actions.pop("hold"):this.pending_actions.isPending("unhold")||this.pending_actions.push("unhold"))},isOnHold:function(){return{local:this.local_hold,remote:this.remote_hold}},receiveReinvite:function(a){var c=this;return this.mediaHandler.hasDescription(a)?void this.mediaHandler.setDescription(a).then(this.mediaHandler.getDescription.bind(this.mediaHandler,this.mediaHint)).then(function(b){var d=["Contact: "+c.contact];a.reply(200,null,d,b,function(){c.status=h.STATUS_WAITING_FOR_ACK,c.setInvite2xxTimer(a,b),c.setACKTimer(),c.remote_hold&&!c.mediaHandler.remote_hold?c.onunhold("remote"):!c.remote_hold&&c.mediaHandler.remote_hold&&c.onhold("remote")})})["catch"](function(d){var e;d instanceof b.Exceptions.GetDescriptionError?e=500:(c.logger.error(d),e=488),a.reply(e)}):(this.logger.warn("invalid Content-Type"),void a.reply(415))},sendReinvite:function(a){a=a||{};var c,d=this,e=(a.extraHeaders||[]).slice(),f=a.eventHandlers||{};f.succeeded&&(c=f.succeeded),this.reinviteSucceeded=function(){b.Timers.clearTimeout(d.timers.ackTimer),b.Timers.clearTimeout(d.timers.invite2xxTimer),d.status=h.STATUS_CONFIRMED,c&&c.apply(this,arguments)},f.failed?this.reinviteFailed=f.failed:this.reinviteFailed=function(){},e.push("Contact: "+this.contact),e.push("Allow: "+b.UA.C.ALLOWED_METHODS.toString()),this.receiveResponse=this.receiveReinviteResponse,this.mediaHandler.getDescription(d.mediaHint).then(function(a){d.dialog.sendRequest(d,b.C.INVITE,{extraHeaders:e,body:a})},function(){d.isReadyToReinvite()&&d.onReadyToReinvite(),d.reinviteFailed()})},receiveRequest:function(a){switch(a.method){case b.C.BYE:a.reply(200),this.status===h.STATUS_CONFIRMED&&(this.emit("bye",a),this.terminated(a,b.C.causes.BYE));break;case b.C.INVITE:this.status===h.STATUS_CONFIRMED&&(this.logger.log("re-INVITE received"),this.receiveReinvite(a));break;case b.C.INFO:if(this.status===h.STATUS_CONFIRMED||this.status===h.STATUS_WAITING_FOR_ACK){if(this.onInfo)return this.onInfo(a);var c,d,e,f=a.getHeader("content-type"),i=/^(Signal\s*?=\s*?)([0-9A-D#*]{1})(\s)?.*/,j=/^(Duration\s?=\s?)([0-9]{1,4})(\s)?.*/;f&&(f.match(/^application\/dtmf-relay/i)?(a.body&&(c=a.body.split("\r\n",2),2===c.length&&(i.test(c[0])&&(d=c[0].replace(i,"$2")),j.test(c[1])&&(e=parseInt(c[1].replace(j,"$2"),10)))),new g(this,d,{duration:e}).init_incoming(a)):a.reply(415,null,["Accept: application/dtmf-relay"]))}break;case b.C.REFER:if(this.status===h.STATUS_CONFIRMED){this.logger.log("REFER received");var k,l=this.listeners("refer").length;l?(a.reply(202,"Accepted"),k="SIP/2.0 100 Trying",this.sendRequest(b.C.NOTIFY,{extraHeaders:["Event: refer","Subscription-State: terminated","Content-Type: message/sipfrag"],body:k,receiveResponse:function(){}}),this.emit("refer",a)):a.reply(603,"Declined")}break;case b.C.NOTIFY:a.reply(200,"OK"),this.emit("notify",a)}},receiveReinviteResponse:function(a){var c=this;if(this.status!==h.STATUS_TERMINATED)switch(!0){case/^1[0-9]{2}$/.test(a.status_code):break;case/^2[0-9]{2}$/.test(a.status_code):if(this.status=h.STATUS_CONFIRMED,this.sendRequest(b.C.ACK,{cseq:a.cseq}),!this.mediaHandler.hasDescription(a)){this.reinviteFailed();break}this.mediaHandler.setDescription(a).then(function(){c.reinviteSucceeded()},function(){c.reinviteFailed()});break;default:this.reinviteFailed()}},acceptAndTerminate:function(a,c,d){var e=[];return c&&e.push("Reason: "+b.Utils.getReasonHeaderValue(c,d)),(this.dialog||this.createDialog(a,"UAC"))&&(this.sendRequest(b.C.ACK,{cseq:a.cseq}),this.sendRequest(b.C.BYE,{extraHeaders:e})),this},setInvite2xxTimer:function(a,c){var d=this,e=b.Timers.T1;this.timers.invite2xxTimer=b.Timers.setTimeout(function f(){if(d.status===h.STATUS_WAITING_FOR_ACK){d.logger.log("no ACK received, attempting to retransmit OK");var g=["Contact: "+d.contact];a.reply(200,null,g,c),e=Math.min(2*e,b.Timers.T2),d.timers.invite2xxTimer=b.Timers.setTimeout(f,e)}},e)},setACKTimer:function(){
var a=this;this.timers.ackTimer=b.Timers.setTimeout(function(){a.status===h.STATUS_WAITING_FOR_ACK&&(a.logger.log("no ACK received for an extended period of time, terminating the call"),b.Timers.clearTimeout(a.timers.invite2xxTimer),a.sendRequest(b.C.BYE),a.terminated(null,b.C.causes.NO_ACK))},b.Timers.TIMER_H)},onReadyToReinvite:function(){var a=this.pending_actions.shift();a&&this[a.name]&&this[a.name]()},onTransportError:function(){this.status!==h.STATUS_CONFIRMED&&this.status!==h.STATUS_TERMINATED&&this.failed(null,b.C.causes.CONNECTION_ERROR)},onRequestTimeout:function(){this.status===h.STATUS_CONFIRMED?this.terminated(null,b.C.causes.REQUEST_TIMEOUT):this.status!==h.STATUS_TERMINATED&&(this.failed(null,b.C.causes.REQUEST_TIMEOUT),this.terminated(null,b.C.causes.REQUEST_TIMEOUT))},onDialogError:function(a){this.status===h.STATUS_CONFIRMED?this.terminated(a,b.C.causes.DIALOG_ERROR):this.status!==h.STATUS_TERMINATED&&(this.failed(a,b.C.causes.DIALOG_ERROR),this.terminated(a,b.C.causes.DIALOG_ERROR))},onhold:function(a){this["local"===a?"local_hold":"remote_hold"]=!0,this.emit("hold",{originator:a})},onunhold:function(a){this["local"===a?"local_hold":"remote_hold"]=!1,this.emit("unhold",{originator:a})},onmute:function(a){this.emit("muted",{audio:a.audio,video:a.video})},onunmute:function(a){this.emit("unmuted",{audio:a.audio,video:a.video})},failed:function(a,b){return this.status===h.STATUS_TERMINATED?this:(this.emit("failed",a||null,b||null),this)},rejected:function(a,b){return this.emit("rejected",a||null,b||null),this},canceled:function(){return this.emit("cancel"),this},accepted:function(a,c){return c=b.Utils.getReasonPhrase(a&&a.status_code,c),this.startTime=new Date,this.replacee&&(this.replacee.emit("replaced",this),this.replacee.terminate()),this.emit("accepted",a,c),this},terminated:function(a,b){return this.status===h.STATUS_TERMINATED?this:(this.endTime=new Date,this.close(),this.emit("terminated",a||null,b||null),this)},connecting:function(a){return this.emit("connecting",{request:a}),this}},d.desugar=function(a){return c.HTMLMediaElement&&a instanceof c.HTMLMediaElement&&(a={media:{constraints:{audio:!0,video:"VIDEO"===a.tagName},render:{remote:a}}}),a||{}},d.C=h,b.Session=d,e=function(a,c){function d(a,b){c.hasHeader(a)&&c.getHeader(a).toLowerCase().indexOf("100rel")>=0&&(g.rel100=b)}function e(){var a={extraHeaders:["Contact: "+g.contact]};g.rel100!==b.C.supported.REQUIRED&&g.progress(a),g.status=h.STATUS_WAITING_FOR_ANSWER,g.timers.userNoAnswerTimer=b.Timers.setTimeout(function(){c.reply(408),g.failed(c,b.C.causes.NO_ANSWER),g.terminated(c,b.C.causes.NO_ANSWER)},g.ua.configuration.noAnswerTimeout),f&&(g.timers.expiresTimer=b.Timers.setTimeout(function(){g.status===h.STATUS_WAITING_FOR_ANSWER&&(c.reply(487),g.failed(c,b.C.causes.EXPIRES),g.terminated(c,b.C.causes.EXPIRES))},f)),g.emit("invite",c)}var f,g=this,i=c.getHeader("Content-Type"),j=c.parseHeader("Content-Disposition");if(b.Utils.augment(this,b.ServerContext,[a,c]),b.Utils.augment(this,b.Session,[a.configuration.mediaHandlerFactory]),this.mediaHandler=this.mediaHandlerFactory(this,{RTCConstraints:{optional:[{DtlsSrtpKeyAgreement:"true"}]}}),!j&&!this.mediaHandler.hasDescription(c)||j&&"render"===j.type)this.renderbody=c.body,this.rendertype=i;else if(!this.mediaHandler.hasDescription(c)&&j&&"session"===j.type)return void c.reply(415);return this.status=h.STATUS_INVITE_RECEIVED,this.from_tag=c.from_tag,this.id=c.call_id+this.from_tag,this.request=c,this.contact=this.ua.contact.toString(),this.receiveNonInviteResponse=function(){},this.logger=a.getLogger("sip.inviteservercontext",this.id),this.ua.sessions[this.id]=this,c.hasHeader("expires")&&(f=1e3*c.getHeader("expires")),d("require",b.C.supported.REQUIRED),d("supported",b.C.supported.SUPPORTED),c.to_tag=b.Utils.newTag(),this.createDialog(c,"UAS",!0)?(this.mediaHandler&&this.mediaHandler.getRemoteStreams&&(this.getRemoteStreams=this.mediaHandler.getRemoteStreams.bind(this.mediaHandler),this.getLocalStreams=this.mediaHandler.getLocalStreams.bind(this.mediaHandler)),void(!this.mediaHandler.hasDescription(c)||this.renderbody?b.Timers.setTimeout(e,0):(this.hasOffer=!0,this.mediaHandler.setDescription(c).then(e,function(a){g.logger.warn("invalid description"),g.logger.warn(a),c.reply(488)})))):void c.reply(500,"Missing Contact header field")},e.prototype={reject:function(a){if(this.status===h.STATUS_TERMINATED)throw new b.Exceptions.InvalidStateError(this.status);return this.logger.log("rejecting RTCSession"),b.ServerContext.prototype.reject.call(this,a),this.terminated()},terminate:function(a){a=a||{};var c,d=(a.extraHeaders||[]).slice(),e=a.body,f=this;return this.status===h.STATUS_WAITING_FOR_ACK&&this.request.server_transaction.state!==b.Transactions.C.STATUS_TERMINATED?(c=this.dialog,this.receiveRequest=function(a){a.method===b.C.ACK&&(this.sendRequest(b.C.BYE,{extraHeaders:d,body:e}),c.terminate())},this.request.server_transaction.on("stateChanged",function(){this.state===b.Transactions.C.STATUS_TERMINATED&&this.dialog&&(this.request=new b.OutgoingRequest(b.C.BYE,this.dialog.remote_target,this.ua,{cseq:this.dialog.local_seqnum+=1,call_id:this.dialog.id.call_id,from_uri:this.dialog.local_uri,from_tag:this.dialog.id.local_tag,to_uri:this.dialog.remote_uri,to_tag:this.dialog.id.remote_tag,route_set:this.dialog.route_set},d,e),new b.RequestSender({request:this.request,onRequestTimeout:function(){f.onRequestTimeout()},onTransportError:function(){f.onTransportError()},receiveResponse:function(){}},this.ua).send(),c.terminate())}),this.emit("bye",this.request),this.terminated(),this.dialog=c,this.ua.dialogs[c.id.toString()]=c):this.status===h.STATUS_CONFIRMED?this.bye(a):this.reject(a),this},progress:function(a){function c(){g=a.statusCode||183,this.status=h.STATUS_WAITING_FOR_PRACK,j.push("Contact: "+this.contact),j.push("Require: 100rel"),j.push("RSeq: "+Math.floor(1e4*Math.random())),this.mediaHint=a.media,this.mediaHandler.getDescription(a.media).then(function(a){if(!this.isCanceled&&this.status!==h.STATUS_TERMINATED){this.early_sdp=a.body,this[this.hasOffer?"hasAnswer":"hasOffer"]=!0;var c=b.Timers.T1;this.timers.rel1xxTimer=b.Timers.setTimeout(function d(){this.request.reply(g,null,j,a),c*=2,this.timers.rel1xxTimer=b.Timers.setTimeout(d.bind(this),c)}.bind(this),c),this.timers.prackTimer=b.Timers.setTimeout(function(){this.status===h.STATUS_WAITING_FOR_PRACK&&(this.logger.log("no PRACK received, rejecting the call"),b.Timers.clearTimeout(this.timers.rel1xxTimer),this.request.reply(504),this.terminated(null,b.C.causes.NO_PRACK))}.bind(this),64*b.Timers.T1),f=this.request.reply(g,i,j,a),this.emit("progress",f,i)}}.bind(this),function(){this.request.reply(480),this.failed(null,b.C.causes.WEBRTC_ERROR),this.terminated(null,b.C.causes.WEBRTC_ERROR)}.bind(this))}function d(){f=this.request.reply(g,i,j,m),this.emit("progress",f,i)}a=a||{};var e,f,g=a.statusCode||180,i=a.reasonPhrase,j=(a.extraHeaders||[]).slice(),k=a.stunServers||null,l=a.turnServers||null,m=a.body;if(100>g||g>199)throw new TypeError("Invalid statusCode: "+g);if(this.isCanceled||this.status===h.STATUS_TERMINATED)return this;if(k||l){if(k){if(e=this.ua.getConfigurationCheck().optional.stunServers(k),!e)throw new TypeError("Invalid stunServers: "+k);this.stunServers=e}if(l){if(e=this.ua.getConfigurationCheck().optional.turnServers(l),!e)throw new TypeError("Invalid turnServers: "+l);this.turnServers=e}this.mediaHandler.updateIceServers({stunServers:this.stunServers,turnServers:this.turnServers})}return 100!==a.statusCode&&(this.rel100===b.C.supported.REQUIRED||this.rel100===b.C.supported.SUPPORTED&&a.rel100||this.rel100===b.C.supported.SUPPORTED&&this.ua.configuration.rel100===b.C.supported.REQUIRED)?c.apply(this):d.apply(this),this},accept:function(a){a=Object.create(d.desugar(a)),b.Utils.optionsOverride(a,"media","mediaConstraints",!0,this.logger,this.ua.configuration.media),this.mediaHint=a.media,this.onInfo=a.onInfo;var c,e=this,f=this.request,g=(a.extraHeaders||[]).slice(),i=a.stunServers||null,j=a.turnServers||null,k=function(a){var c,d=function(){e.status=h.STATUS_WAITING_FOR_ACK,e.setInvite2xxTimer(f,a),e.setACKTimer()},i=function(){e.failed(null,b.C.causes.CONNECTION_ERROR),e.terminated(null,b.C.causes.CONNECTION_ERROR)};e.mediaHandler.render(),g.push("Contact: "+e.contact),g.push("Allow: "+b.UA.C.ALLOWED_METHODS.toString()),e.hasOffer?e.hasAnswer=!0:e.hasOffer=!0,c=f.reply(200,null,g,a,d,i),e.status!==h.STATUS_TERMINATED&&e.accepted(c,b.Utils.getReasonPhrase(200))},l=function(){e.status!==h.STATUS_TERMINATED&&(e.request.reply(480),e.failed(null,b.C.causes.WEBRTC_ERROR),e.terminated(null,b.C.causes.WEBRTC_ERROR))};if(this.status===h.STATUS_WAITING_FOR_PRACK)return this.status=h.STATUS_ANSWERED_WAITING_FOR_PRACK,this;if(this.status===h.STATUS_WAITING_FOR_ANSWER)this.status=h.STATUS_ANSWERED;else if(this.status!==h.STATUS_EARLY_MEDIA)throw new b.Exceptions.InvalidStateError(this.status);if((i||j)&&this.status!==h.STATUS_EARLY_MEDIA&&this.status!==h.STATUS_ANSWERED_WAITING_FOR_PRACK){if(i){if(c=this.ua.getConfigurationCheck().optional.stunServers(i),!c)throw new TypeError("Invalid stunServers: "+i);this.stunServers=c}if(j){if(c=this.ua.getConfigurationCheck().optional.turnServers(j),!c)throw new TypeError("Invalid turnServers: "+j);this.turnServers=c}this.mediaHandler.updateIceServers({stunServers:this.stunServers,turnServers:this.turnServers})}return this.createDialog(f,"UAS")?(b.Timers.clearTimeout(this.timers.userNoAnswerTimer),this.status===h.STATUS_EARLY_MEDIA?k({}):this.mediaHandler.getDescription(e.mediaHint).then(k,l),this):(f.reply(500,"Missing Contact header field"),this)},receiveRequest:function(a){function c(){var c;b.Timers.clearTimeout(this.timers.ackTimer),b.Timers.clearTimeout(this.timers.invite2xxTimer),this.status=h.STATUS_CONFIRMED,this.unmute(),c=a.getHeader("Content-Type"),this.mediaHandler.hasDescription(a)||(this.renderbody=a.body,this.rendertype=c),this.emit("confirmed",a)}switch(a.method){case b.C.CANCEL:(this.status===h.STATUS_WAITING_FOR_ANSWER||this.status===h.STATUS_WAITING_FOR_PRACK||this.status===h.STATUS_ANSWERED_WAITING_FOR_PRACK||this.status===h.STATUS_EARLY_MEDIA||this.status===h.STATUS_ANSWERED)&&(this.status=h.STATUS_CANCELED,this.request.reply(487),this.canceled(a),this.rejected(a,b.C.causes.CANCELED),this.failed(a,b.C.causes.CANCELED),this.terminated(a,b.C.causes.CANCELED));break;case b.C.ACK:this.status===h.STATUS_WAITING_FOR_ACK&&(this.hasAnswer?c.apply(this):this.mediaHandler.hasDescription(a)?(this.hasAnswer=!0,this.mediaHandler.setDescription(a).then(c.bind(this),function(c){this.logger.warn(c),this.terminate({statusCode:"488",reasonPhrase:"Bad Media Description"}),this.failed(a,b.C.causes.BAD_MEDIA_DESCRIPTION),this.terminated(a,b.C.causes.BAD_MEDIA_DESCRIPTION)}.bind(this))):this.early_sdp?c.apply(this):(this.failed(a,b.C.causes.BAD_MEDIA_DESCRIPTION),this.terminated(a,b.C.causes.BAD_MEDIA_DESCRIPTION)));break;case b.C.PRACK:this.status===h.STATUS_WAITING_FOR_PRACK||this.status===h.STATUS_ANSWERED_WAITING_FOR_PRACK?this.hasAnswer?(b.Timers.clearTimeout(this.timers.rel1xxTimer),b.Timers.clearTimeout(this.timers.prackTimer),a.reply(200),this.status===h.STATUS_ANSWERED_WAITING_FOR_PRACK&&(this.status=h.STATUS_EARLY_MEDIA,this.accept()),this.status=h.STATUS_EARLY_MEDIA,this.mute()):this.mediaHandler.hasDescription(a)?(this.hasAnswer=!0,this.mediaHandler.setDescription(a).then(function(){b.Timers.clearTimeout(this.timers.rel1xxTimer),b.Timers.clearTimeout(this.timers.prackTimer),a.reply(200),this.status===h.STATUS_ANSWERED_WAITING_FOR_PRACK&&(this.status=h.STATUS_EARLY_MEDIA,this.accept()),this.status=h.STATUS_EARLY_MEDIA,this.mute()}.bind(this),function(c){this.logger.warn(c),this.terminate({statusCode:"488",reasonPhrase:"Bad Media Description"}),this.failed(a,b.C.causes.BAD_MEDIA_DESCRIPTION),this.terminated(a,b.C.causes.BAD_MEDIA_DESCRIPTION)}.bind(this))):(this.terminate({statusCode:"488",reasonPhrase:"Bad Media Description"}),this.failed(a,b.C.causes.BAD_MEDIA_DESCRIPTION),this.terminated(a,b.C.causes.BAD_MEDIA_DESCRIPTION)):this.status===h.STATUS_EARLY_MEDIA&&a.reply(200);break;default:d.prototype.receiveRequest.apply(this,[a])}},onTransportError:function(){this.status!==h.STATUS_CONFIRMED&&this.status!==h.STATUS_TERMINATED&&this.failed(null,b.C.causes.CONNECTION_ERROR)},onRequestTimeout:function(){this.status===h.STATUS_CONFIRMED?this.terminated(null,b.C.causes.REQUEST_TIMEOUT):this.status!==h.STATUS_TERMINATED&&(this.failed(null,b.C.causes.REQUEST_TIMEOUT),this.terminated(null,b.C.causes.REQUEST_TIMEOUT))}},b.InviteServerContext=e,f=function(a,c,e){e=Object.create(d.desugar(e)),e.params=Object.create(e.params||Object.prototype);var f,g=(e.extraHeaders||[]).slice(),i=e.stunServers||null,j=e.turnServers||null,k=e.mediaHandlerFactory||a.configuration.mediaHandlerFactory,l=k.isSupported;if(l&&!l())throw new b.Exceptions.NotSupportedError("Media not supported");if(this.RTCConstraints=e.RTCConstraints||{},this.inviteWithoutSdp=e.inviteWithoutSdp||!1,this.anonymous=e.anonymous||!1,this.renderbody=e.renderbody||null,this.rendertype=e.rendertype||"text/plain",e.params.from_tag=this.from_tag,this.contact=a.contact.toString({anonymous:this.anonymous,outbound:this.anonymous?!a.contact.temp_gruu:!a.contact.pub_gruu}),this.anonymous&&(e.params.from_displayName="Anonymous",e.params.from_uri="sip:<EMAIL>",g.push("P-Preferred-Identity: "+a.configuration.uri.toString()),g.push("Privacy: id")),g.push("Contact: "+this.contact),g.push("Allow: "+b.UA.C.ALLOWED_METHODS.toString()),this.inviteWithoutSdp&&this.renderbody&&(g.push("Content-Type: "+this.rendertype),g.push("Content-Disposition: render;handling=optional")),a.configuration.rel100===b.C.supported.REQUIRED&&g.push("Require: 100rel"),a.configuration.replaces===b.C.supported.REQUIRED&&g.push("Require: replaces"),e.extraHeaders=g,b.Utils.augment(this,b.ClientContext,[a,b.C.INVITE,c,e]),b.Utils.augment(this,b.Session,[k]),this.status!==h.STATUS_NULL)throw new b.Exceptions.InvalidStateError(this.status);if(this.from_tag=b.Utils.newTag(),this.isCanceled=!1,this.received_100=!1,this.method=b.C.INVITE,this.receiveNonInviteResponse=this.receiveResponse,this.receiveResponse=this.receiveInviteResponse,this.logger=a.getLogger("sip.inviteclientcontext"),i){if(f=this.ua.getConfigurationCheck().optional.stunServers(i),!f)throw new TypeError("Invalid stunServers: "+i);this.stunServers=f}if(j){if(f=this.ua.getConfigurationCheck().optional.turnServers(j),!f)throw new TypeError("Invalid turnServers: "+j);this.turnServers=f}a.applicants[this]=this,this.id=this.request.call_id+this.from_tag,this.mediaHandler=this.mediaHandlerFactory(this,{RTCConstraints:this.RTCConstraints,stunServers:this.stunServers,turnServers:this.turnServers}),this.mediaHandler&&this.mediaHandler.getRemoteStreams&&(this.getRemoteStreams=this.mediaHandler.getRemoteStreams.bind(this.mediaHandler),this.getLocalStreams=this.mediaHandler.getLocalStreams.bind(this.mediaHandler)),b.Utils.optionsOverride(e,"media","mediaConstraints",!0,this.logger,this.ua.configuration.media),this.mediaHint=e.media,this.onInfo=e.onInfo},f.prototype={invite:function(){var a=this;return this.ua.sessions[this.id]=this,this.inviteWithoutSdp?(this.request.body=a.renderbody,this.status=h.STATUS_INVITE_SENT,this.send()):this.mediaHandler.getDescription(a.mediaHint).then(function(b){a.isCanceled||a.status===h.STATUS_TERMINATED||(a.hasOffer=!0,a.request.body=b,a.status=h.STATUS_INVITE_SENT,a.send())},function(){a.status!==h.STATUS_TERMINATED&&(a.failed(null,b.C.causes.WEBRTC_ERROR),a.terminated(null,b.C.causes.WEBRTC_ERROR))}),this},receiveInviteResponse:function(a){var c,d=this,e=a.call_id+a.from_tag+a.to_tag,f=[],g={};if(this.status!==h.STATUS_TERMINATED&&a.method===b.C.INVITE){if(this.dialog&&a.status_code>=200&&a.status_code<=299){if(e!==this.dialog.id.toString()){if(!this.createDialog(a,"UAC",!0))return;return this.earlyDialogs[e].sendRequest(this,b.C.ACK,{body:b.Utils.generateFakeSDP(a.body)}),this.earlyDialogs[e].sendRequest(this,b.C.BYE),void(this.status!==h.STATUS_CONFIRMED&&(this.failed(a,b.C.causes.WEBRTC_ERROR),this.terminated(a,b.C.causes.WEBRTC_ERROR)))}if(this.status===h.STATUS_CONFIRMED)return void this.sendRequest(b.C.ACK,{cseq:a.cseq});if(!this.hasAnswer)return}if(this.dialog&&a.status_code<200){if(-1!==this.dialog.pracked.indexOf(a.getHeader("rseq"))||this.dialog.pracked[this.dialog.pracked.length-1]>=a.getHeader("rseq")&&this.dialog.pracked.length>0)return;if(!this.earlyDialogs[e]&&!this.createDialog(a,"UAC",!0))return;if(-1!==this.earlyDialogs[e].pracked.indexOf(a.getHeader("rseq"))||this.earlyDialogs[e].pracked[this.earlyDialogs[e].pracked.length-1]>=a.getHeader("rseq")&&this.earlyDialogs[e].pracked.length>0)return;return f.push("RAck: "+a.getHeader("rseq")+" "+a.getHeader("cseq")),this.earlyDialogs[e].pracked.push(a.getHeader("rseq")),void this.earlyDialogs[e].sendRequest(this,b.C.PRACK,{extraHeaders:f,body:b.Utils.generateFakeSDP(a.body)})}if(this.isCanceled)return void(a.status_code>=100&&a.status_code<200?(this.request.cancel(this.cancelReason,f),this.canceled(null)):a.status_code>=200&&a.status_code<299?(this.acceptAndTerminate(a),this.emit("bye",this.request)):a.status_code>=300&&(c=b.C.REASON_PHRASE[a.status_code]||b.C.causes.CANCELED,this.rejected(a,c),this.failed(a,c),this.terminated(a,c)));switch(!0){case/^100$/.test(a.status_code):this.received_100=!0,this.emit("progress",a);break;case/^1[0-9]{2}$/.test(a.status_code):if(!a.to_tag){this.logger.warn("1xx response received without to tag");break}if(a.hasHeader("contact")&&!this.createDialog(a,"UAC",!0))break;if(this.status=h.STATUS_1XX_RECEIVED,a.hasHeader("require")&&-1!==a.getHeader("require").indexOf("100rel")){if(this.dialog||!this.earlyDialogs[e])break;if(-1!==this.earlyDialogs[e].pracked.indexOf(a.getHeader("rseq"))||this.earlyDialogs[e].pracked[this.earlyDialogs[e].pracked.length-1]>=a.getHeader("rseq")&&this.earlyDialogs[e].pracked.length>0)return;if(this.mediaHandler.hasDescription(a))if(this.hasOffer){if(!this.createDialog(a,"UAC"))break;this.hasAnswer=!0,this.dialog.pracked.push(a.getHeader("rseq")),this.mediaHandler.setDescription(a).then(function(){f.push("RAck: "+a.getHeader("rseq")+" "+a.getHeader("cseq")),d.sendRequest(b.C.PRACK,{extraHeaders:f,receiveResponse:function(){}}),d.status=h.STATUS_EARLY_MEDIA,d.mute(),d.emit("progress",a)},function(c){d.logger.warn(c),d.acceptAndTerminate(a,488,"Not Acceptable Here"),d.failed(a,b.C.causes.BAD_MEDIA_DESCRIPTION)})}else{var i=this.earlyDialogs[e],j=i.mediaHandler;i.pracked.push(a.getHeader("rseq")),j.setDescription(a).then(j.getDescription.bind(j,d.mediaHint)).then(function(c){f.push("RAck: "+a.getHeader("rseq")+" "+a.getHeader("cseq")),i.sendRequest(d,b.C.PRACK,{extraHeaders:f,body:c}),d.status=h.STATUS_EARLY_MEDIA,d.emit("progress",a)})["catch"](function(c){if(c instanceof b.Exceptions.GetDescriptionError){if(i.pracked.push(a.getHeader("rseq")),d.status===h.STATUS_TERMINATED)return;d.failed(null,b.C.causes.WEBRTC_ERROR),d.terminated(null,b.C.causes.WEBRTC_ERROR)}else i.pracked.splice(i.pracked.indexOf(a.getHeader("rseq")),1),d.logger.warn("invalid description"),d.logger.warn(c)})}else f.push("RAck: "+a.getHeader("rseq")+" "+a.getHeader("cseq")),this.earlyDialogs[e].pracked.push(a.getHeader("rseq")),this.earlyDialogs[e].sendRequest(this,b.C.PRACK,{extraHeaders:f}),this.emit("progress",a)}else this.emit("progress",a);break;case/^2[0-9]{2}$/.test(a.status_code):var k=this.request.cseq+" "+this.request.method;if(k!==a.getHeader("cseq"))break;if(this.status===h.STATUS_EARLY_MEDIA&&this.dialog){this.status=h.STATUS_CONFIRMED,this.unmute(),g={},this.renderbody&&(f.push("Content-Type: "+this.rendertype),g.extraHeaders=f,g.body=this.renderbody),g.cseq=a.cseq,this.sendRequest(b.C.ACK,g),this.accepted(a);break}if(this.dialog)break;if(this.hasOffer)if(this.hasAnswer)this.renderbody&&(f.push("Content-Type: "+d.rendertype),g.extraHeaders=f,g.body=this.renderbody),this.sendRequest(b.C.ACK,g);else{if(!this.mediaHandler.hasDescription(a)){this.acceptAndTerminate(a,400,"Missing session description"),this.failed(a,b.C.causes.BAD_MEDIA_DESCRIPTION);break}if(!this.createDialog(a,"UAC"))break;this.hasAnswer=!0,this.mediaHandler.setDescription(a).then(function(){var c={};d.status=h.STATUS_CONFIRMED,d.unmute(),d.renderbody&&(f.push("Content-Type: "+d.rendertype),c.extraHeaders=f,c.body=d.renderbody),c.cseq=a.cseq,d.sendRequest(b.C.ACK,c),d.accepted(a)},function(c){d.logger.warn(c),d.acceptAndTerminate(a,488,"Not Acceptable Here"),d.failed(a,b.C.causes.BAD_MEDIA_DESCRIPTION)})}else if(this.earlyDialogs[e]&&this.earlyDialogs[e].mediaHandler.localMedia){if(this.hasOffer=!0,this.hasAnswer=!0,this.mediaHandler=this.earlyDialogs[e].mediaHandler,!this.createDialog(a,"UAC"))break;this.status=h.STATUS_CONFIRMED,this.sendRequest(b.C.ACK,{cseq:a.cseq}),this.unmute(),this.accepted(a)}else{if(!this.mediaHandler.hasDescription(a)){this.acceptAndTerminate(a,400,"Missing session description"),this.failed(a,b.C.causes.BAD_MEDIA_DESCRIPTION);break}if(!this.createDialog(a,"UAC"))break;this.hasOffer=!0,this.mediaHandler.setDescription(a).then(this.mediaHandler.getDescription.bind(this.mediaHandler,this.mediaHint)).then(function(c){d.isCanceled||d.status===h.STATUS_TERMINATED||(d.status=h.STATUS_CONFIRMED,d.hasAnswer=!0,d.unmute(),d.sendRequest(b.C.ACK,{body:c,cseq:a.cseq}),d.accepted(a))})["catch"](function(c){c instanceof b.Exceptions.GetDescriptionError?d.logger.warn("there was a problem"):(d.logger.warn("invalid description"),d.logger.warn(c),d.acceptAndTerminate(a,488,"Invalid session description"),d.failed(a,b.C.causes.BAD_MEDIA_DESCRIPTION))})}break;default:c=b.Utils.sipErrorCause(a.status_code),this.rejected(a,c),this.failed(a,c),this.terminated(a,c)}}},cancel:function(a){if(a=a||{},a.extraHeaders=(a.extraHeaders||[]).slice(),this.status===h.STATUS_TERMINATED||this.status===h.STATUS_CONFIRMED)throw new b.Exceptions.InvalidStateError(this.status);this.logger.log("canceling RTCSession");var c=b.Utils.getCancelReason(a.status_code,a.reason_phrase);return this.status===h.STATUS_NULL||this.status===h.STATUS_INVITE_SENT&&!this.received_100?(this.isCanceled=!0,this.cancelReason=c):(this.status===h.STATUS_INVITE_SENT||this.status===h.STATUS_1XX_RECEIVED||this.status===h.STATUS_EARLY_MEDIA)&&this.request.cancel(c,a.extraHeaders),this.canceled()},terminate:function(a){return this.status===h.STATUS_TERMINATED?this:(this.status===h.STATUS_WAITING_FOR_ACK||this.status===h.STATUS_CONFIRMED?this.bye(a):this.cancel(a),this)},receiveRequest:function(a){return a.method===b.C.CANCEL,a.method===b.C.ACK&&this.status===h.STATUS_WAITING_FOR_ACK&&(b.Timers.clearTimeout(this.timers.ackTimer),b.Timers.clearTimeout(this.timers.invite2xxTimer),this.status=h.STATUS_CONFIRMED,this.unmute(),this.accepted()),d.prototype.receiveRequest.apply(this,[a])},onTransportError:function(){this.status!==h.STATUS_CONFIRMED&&this.status!==h.STATUS_TERMINATED&&this.failed(null,b.C.causes.CONNECTION_ERROR)},onRequestTimeout:function(){this.status===h.STATUS_CONFIRMED?this.terminated(null,b.C.causes.REQUEST_TIMEOUT):this.status!==h.STATUS_TERMINATED&&(this.failed(null,b.C.causes.REQUEST_TIMEOUT),this.terminated(null,b.C.causes.REQUEST_TIMEOUT))}},b.InviteClientContext=f}},{"./Session/DTMF":24}],24:[function(a,b,c){"use strict";b.exports=function(a){var b,c={MIN_DURATION:70,MAX_DURATION:6e3,DEFAULT_DURATION:100,MIN_INTER_TONE_GAP:50,DEFAULT_INTER_TONE_GAP:500};return b=function(c,d,e){var f,g;if(void 0===d)throw new TypeError("Not enough arguments");if(this.logger=c.ua.getLogger("sip.invitecontext.dtmf",c.id),this.owner=c,this.direction=null,e=e||{},f=e.duration||null,g=e.interToneGap||null,"string"==typeof d)d=d.toUpperCase();else{if("number"!=typeof d)throw new TypeError("Invalid tone: "+d);d=d.toString()}if(!d.match(/^[0-9A-D#*]$/))throw new TypeError("Invalid tone: "+d);if(this.tone=d,f&&!a.Utils.isDecimal(f))throw new TypeError("Invalid tone duration: "+f);if(f?f<b.C.MIN_DURATION?(this.logger.warn('"duration" value is lower than the minimum allowed, setting it to '+b.C.MIN_DURATION+" milliseconds"),f=b.C.MIN_DURATION):f>b.C.MAX_DURATION?(this.logger.warn('"duration" value is greater than the maximum allowed, setting it to '+b.C.MAX_DURATION+" milliseconds"),f=b.C.MAX_DURATION):f=Math.abs(f):f=b.C.DEFAULT_DURATION,this.duration=f,g&&!a.Utils.isDecimal(g))throw new TypeError("Invalid interToneGap: "+g);g?g<b.C.MIN_INTER_TONE_GAP?(this.logger.warn('"interToneGap" value is lower than the minimum allowed, setting it to '+b.C.MIN_INTER_TONE_GAP+" milliseconds"),g=b.C.MIN_INTER_TONE_GAP):g=Math.abs(g):g=b.C.DEFAULT_INTER_TONE_GAP,this.interToneGap=g},b.prototype=Object.create(a.EventEmitter.prototype),b.prototype.send=function(b){var c,d={};if(this.direction="outgoing",this.owner.status!==a.Session.C.STATUS_CONFIRMED&&this.owner.status!==a.Session.C.STATUS_WAITING_FOR_ACK)throw new a.Exceptions.InvalidStateError(this.owner.status);b=b||{},c=b.extraHeaders?b.extraHeaders.slice():[],d.contentType="application/dtmf-relay",d.body="Signal= "+this.tone+"\r\n",d.body+="Duration= "+this.duration,this.request=this.owner.dialog.sendRequest(this,a.C.INFO,{extraHeaders:c,body:d}),this.owner.emit("dtmf",this.request,this)},b.prototype.receiveResponse=function(b){var c;switch(!0){case/^1[0-9]{2}$/.test(b.status_code):break;case/^2[0-9]{2}$/.test(b.status_code):this.emit("succeeded",{originator:"remote",response:b});break;default:c=a.Utils.sipErrorCause(b.status_code),this.emit("failed",b,c)}},b.prototype.onRequestTimeout=function(){this.emit("failed",null,a.C.causes.REQUEST_TIMEOUT),this.owner.onRequestTimeout()},b.prototype.onTransportError=function(){this.emit("failed",null,a.C.causes.CONNECTION_ERROR),this.owner.onTransportError()},b.prototype.onDialogError=function(b){this.emit("failed",b,a.C.causes.DIALOG_ERROR),this.owner.onDialogError(b)},b.prototype.init_incoming=function(a){this.direction="incoming",this.request=a,a.reply(200),this.tone&&this.duration?this.owner.emit("dtmf",a,this):this.logger.warn("invalid INFO DTMF received, discarded")},b.C=c,b}},{}],25:[function(a,b,c){"use strict";b.exports=function(a){a.Subscription=function(b,c,d,e){if(e=Object.create(e||Object.prototype),this.extraHeaders=e.extraHeaders=(e.extraHeaders||[]).slice(),this.id=null,this.state="init",!d)throw new TypeError("Event necessary to create a subscription.");this.event=d,"number"!=typeof e.expires?(b.logger.warn("expires must be a number. Using default of 3600."),this.expires=3600):this.expires=e.expires,e.extraHeaders.push("Event: "+this.event),e.extraHeaders.push("Expires: "+this.expires),e.body&&(this.body=e.body),this.contact=b.contact.toString(),e.extraHeaders.push("Contact: "+this.contact),e.extraHeaders.push("Allow: "+a.UA.C.ALLOWED_METHODS.toString()),a.Utils.augment(this,a.ClientContext,[b,a.C.SUBSCRIBE,c,e]),this.logger=b.getLogger("sip.subscription"),this.dialog=null,this.timers={N:null,sub_duration:null},this.errorCodes=[404,405,410,416,480,481,482,483,484,485,489,501,604]},a.Subscription.prototype={subscribe:function(){var b=this;return"active"===this.state?(this.refresh(),this):"notify_wait"===this.state?this:(a.Timers.clearTimeout(this.timers.sub_duration),a.Timers.clearTimeout(this.timers.N),this.timers.N=a.Timers.setTimeout(b.timer_fire.bind(b),a.Timers.TIMER_N),this.ua.earlySubscriptions[this.request.call_id+this.request.from.parameters.tag+this.event]=this,this.send(),this.state="notify_wait",this)},refresh:function(){"terminated"!==this.state&&"pending"!==this.state&&"notify_wait"!==this.state&&this.dialog.sendRequest(this,a.C.SUBSCRIBE,{extraHeaders:this.extraHeaders,body:this.body})},receiveResponse:function(b){var c,d=this,e=a.Utils.getReasonPhrase(b.status_code);"notify_wait"===this.state&&b.status_code>=300||"notify_wait"!==this.state&&-1!==this.errorCodes.indexOf(b.status_code)?this.failed(b,null):/^2[0-9]{2}$/.test(b.status_code)?(this.emit("accepted",b,e),c=b.getHeader("Expires"),c&&c<=this.expires?(this.expires=c,this.timers.sub_duration=a.Timers.setTimeout(d.refresh.bind(d),900*c)):c?(this.logger.warn("Expires header in a 200-class response to SUBSCRIBE with a higher value than the one in the request"),this.failed(b,a.C.INVALID_EXPIRES_HEADER)):(this.logger.warn("Expires header missing in a 200-class response to SUBSCRIBE"),this.failed(b,a.C.EXPIRES_HEADER_MISSING))):b.statusCode>300&&(this.emit("failed",b,e),this.emit("rejected",b,e))},unsubscribe:function(){var b=[],c=this;this.state="terminated",b.push("Event: "+this.event),b.push("Expires: 0"),b.push("Contact: "+this.contact),b.push("Allow: "+a.UA.C.ALLOWED_METHODS.toString()),this.receiveResponse=function(){},this.dialog.sendRequest(this,this.method,{extraHeaders:b,body:this.body}),a.Timers.clearTimeout(this.timers.sub_duration),a.Timers.clearTimeout(this.timers.N),this.timers.N=a.Timers.setTimeout(c.timer_fire.bind(c),a.Timers.TIMER_N)},timer_fire:function(){"terminated"===this.state?(this.terminateDialog(),a.Timers.clearTimeout(this.timers.N),a.Timers.clearTimeout(this.timers.sub_duration),delete this.ua.subscriptions[this.id]):"notify_wait"===this.state||"pending"===this.state?this.close():this.refresh()},close:function(){"notify_wait"===this.state?(this.state="terminated",a.Timers.clearTimeout(this.timers.N),a.Timers.clearTimeout(this.timers.sub_duration),this.receiveResponse=function(){},delete this.ua.earlySubscriptions[this.request.call_id+this.request.from.parameters.tag+this.event]):"terminated"!==this.state&&this.unsubscribe()},createConfirmedDialog:function(b,c){var d;return this.terminateDialog(),d=new a.Dialog(this,b,c),d.invite_seqnum=this.request.cseq,d.local_seqnum=this.request.cseq,d.error?!1:(this.dialog=d,!0)},terminateDialog:function(){this.dialog&&(delete this.ua.subscriptions[this.id],this.dialog.terminate(),delete this.dialog)},receiveRequest:function(b){function c(){d.expires&&(a.Timers.clearTimeout(e.timers.sub_duration),d.expires=Math.min(e.expires,Math.max(d.expires,0)),e.timers.sub_duration=a.Timers.setTimeout(e.refresh.bind(e),900*d.expires))}var d,e=this;if(!this.matchEvent(b))return void b.reply(489);if(this.dialog||this.createConfirmedDialog(b,"UAS")&&(this.id=this.dialog.id.toString(),delete this.ua.earlySubscriptions[this.request.call_id+this.request.from.parameters.tag+this.event],this.ua.subscriptions[this.id]=this),d=b.parseHeader("Subscription-State"),b.reply(200,a.C.REASON_200),a.Timers.clearTimeout(this.timers.N),this.emit("notify",{request:b}),"terminated"===this.state)return void("terminated"===d.state&&(this.terminateDialog(),a.Timers.clearTimeout(this.timers.N),a.Timers.clearTimeout(this.timers.sub_duration),delete this.ua.subscriptions[this.id]));switch(d.state){case"active":this.state="active",c();break;case"pending":"notify_wait"===this.state&&c(),this.state="pending";break;case"terminated":if(a.Timers.clearTimeout(this.timers.sub_duration),d.reason)switch(this.logger.log("terminating subscription with reason "+d.reason),d.reason){case"deactivated":case"timeout":return void this.subscribe();case"probation":case"giveup":return void(d.params&&d.params["retry-after"]?this.timers.sub_duration=a.Timers.setTimeout(e.subscribe.bind(e),d.params["retry-after"]):this.subscribe());case"rejected":case"noresource":case"invariant":}this.close()}},failed:function(a,b){return this.close(),this.emit("failed",a,b),this.emit("rejected",a,b),this},onDialogError:function(b){this.failed(b,a.C.causes.DIALOG_ERROR)},matchEvent:function(a){var b;return a.hasHeader("Event")?a.hasHeader("Subscription-State")?(b=a.parseHeader("event").event,this.event!==b?(this.logger.warn("event match failed"),a.reply(481,"Event Match Failed"),!1):!0):(this.logger.warn("missing Subscription-State header"),!1):(this.logger.warn("missing Event header"),!1)}}}},{}],26:[function(a,b,c){"use strict";var d=500,e=4e3,f=5e3;b.exports=function(a){var b={T1:d,T2:e,T4:f,TIMER_B:64*d,TIMER_D:0*d,TIMER_F:64*d,TIMER_H:64*d,TIMER_I:0*d,TIMER_J:0*d,TIMER_K:0*f,TIMER_L:64*d,
TIMER_M:64*d,TIMER_N:64*d,PROVISIONAL_RESPONSE_INTERVAL:6e4};return["setTimeout","clearTimeout","setInterval","clearInterval"].forEach(function(c){b[c]=function(){return a[c].apply(a,arguments)}}),b}},{}],27:[function(a,b,c){"use strict";b.exports=function(a){function b(a,b,c){var d;return d="SIP/2.0/"+(a.ua.configuration.hackViaTcp?"TCP":b.server.scheme),d+=" "+a.ua.configuration.viaHost+";branch="+c,a.ua.configuration.forceRport&&(d+=";rport"),d}var c={STATUS_TRYING:1,STATUS_PROCEEDING:2,STATUS_CALLING:3,STATUS_ACCEPTED:4,STATUS_COMPLETED:5,STATUS_TERMINATED:6,STATUS_CONFIRMED:7,NON_INVITE_CLIENT:"nict",NON_INVITE_SERVER:"nist",INVITE_CLIENT:"ict",INVITE_SERVER:"ist"},d=function(a,d,e){var f;this.type=c.NON_INVITE_CLIENT,this.transport=e,this.id="z9hG4bK"+Math.floor(1e7*Math.random()),this.request_sender=a,this.request=d,this.logger=a.ua.getLogger("sip.transaction.nict",this.id),f=b(a,e,this.id),this.request.setHeader("via",f),this.request_sender.ua.newTransaction(this)};d.prototype=Object.create(a.EventEmitter.prototype),d.prototype.stateChanged=function(a){this.state=a,this.emit("stateChanged")},d.prototype.send=function(){var b=this;this.stateChanged(c.STATUS_TRYING),this.F=a.Timers.setTimeout(b.timer_F.bind(b),a.Timers.TIMER_F),this.transport.send(this.request)||this.onTransportError()},d.prototype.onTransportError=function(){this.logger.log("transport error occurred, deleting non-INVITE client transaction "+this.id),a.Timers.clearTimeout(this.F),a.Timers.clearTimeout(this.K),this.stateChanged(c.STATUS_TERMINATED),this.request_sender.ua.destroyTransaction(this),this.request_sender.onTransportError()},d.prototype.timer_F=function(){this.logger.log("Timer F expired for non-INVITE client transaction "+this.id),this.stateChanged(c.STATUS_TERMINATED),this.request_sender.ua.destroyTransaction(this),this.request_sender.onRequestTimeout()},d.prototype.timer_K=function(){this.stateChanged(c.STATUS_TERMINATED),this.request_sender.ua.destroyTransaction(this)},d.prototype.receiveResponse=function(b){var d=this,e=b.status_code;if(200>e)switch(this.state){case c.STATUS_TRYING:case c.STATUS_PROCEEDING:this.stateChanged(c.STATUS_PROCEEDING),this.request_sender.receiveResponse(b)}else switch(this.state){case c.STATUS_TRYING:case c.STATUS_PROCEEDING:this.stateChanged(c.STATUS_COMPLETED),a.Timers.clearTimeout(this.F),408===e?this.request_sender.onRequestTimeout():this.request_sender.receiveResponse(b),this.K=a.Timers.setTimeout(d.timer_K.bind(d),a.Timers.TIMER_K);break;case c.STATUS_COMPLETED:}};var e=function(a,d,e){var f,g=this;this.type=c.INVITE_CLIENT,this.transport=e,this.id="z9hG4bK"+Math.floor(1e7*Math.random()),this.request_sender=a,this.request=d,this.logger=a.ua.getLogger("sip.transaction.ict",this.id),f=b(a,e,this.id),this.request.setHeader("via",f),this.request_sender.ua.newTransaction(this),this.request.cancel=function(a,b){b=(b||[]).slice();for(var c=b.length,d=null,e=0;c>e;e++)d=(d||"")+b[e].trim()+"\r\n";g.cancel_request(g,a,d)}};e.prototype=Object.create(a.EventEmitter.prototype),e.prototype.stateChanged=function(a){this.state=a,this.emit("stateChanged")},e.prototype.send=function(){var b=this;this.stateChanged(c.STATUS_CALLING),this.B=a.Timers.setTimeout(b.timer_B.bind(b),a.Timers.TIMER_B),this.transport.send(this.request)||this.onTransportError()},e.prototype.onTransportError=function(){this.logger.log("transport error occurred, deleting INVITE client transaction "+this.id),a.Timers.clearTimeout(this.B),a.Timers.clearTimeout(this.D),a.Timers.clearTimeout(this.M),this.stateChanged(c.STATUS_TERMINATED),this.request_sender.ua.destroyTransaction(this),this.state!==c.STATUS_ACCEPTED&&this.request_sender.onTransportError()},e.prototype.timer_M=function(){this.logger.log("Timer M expired for INVITE client transaction "+this.id),this.state===c.STATUS_ACCEPTED&&(a.Timers.clearTimeout(this.B),this.stateChanged(c.STATUS_TERMINATED),this.request_sender.ua.destroyTransaction(this))},e.prototype.timer_B=function(){this.logger.log("Timer B expired for INVITE client transaction "+this.id),this.state===c.STATUS_CALLING&&(this.stateChanged(c.STATUS_TERMINATED),this.request_sender.ua.destroyTransaction(this),this.request_sender.onRequestTimeout())},e.prototype.timer_D=function(){this.logger.log("Timer D expired for INVITE client transaction "+this.id),a.Timers.clearTimeout(this.B),this.stateChanged(c.STATUS_TERMINATED),this.request_sender.ua.destroyTransaction(this)},e.prototype.sendACK=function(b){var c=this;this.ack="ACK "+this.request.ruri+" SIP/2.0\r\n",this.ack+="Via: "+this.request.headers.Via.toString()+"\r\n",this.request.headers.Route&&(this.ack+="Route: "+this.request.headers.Route.toString()+"\r\n"),this.ack+="To: "+b.getHeader("to")+"\r\n",this.ack+="From: "+this.request.headers.From.toString()+"\r\n",this.ack+="Call-ID: "+this.request.headers["Call-ID"].toString()+"\r\n",this.ack+="Content-Length: 0\r\n",this.ack+="CSeq: "+this.request.headers.CSeq.toString().split(" ")[0],this.ack+=" ACK\r\n\r\n",this.D=a.Timers.setTimeout(c.timer_D.bind(c),a.Timers.TIMER_D),this.transport.send(this.ack)},e.prototype.cancel_request=function(b,d,e){var f=b.request;this.cancel=a.C.CANCEL+" "+f.ruri+" SIP/2.0\r\n",this.cancel+="Via: "+f.headers.Via.toString()+"\r\n",this.request.headers.Route&&(this.cancel+="Route: "+f.headers.Route.toString()+"\r\n"),this.cancel+="To: "+f.headers.To.toString()+"\r\n",this.cancel+="From: "+f.headers.From.toString()+"\r\n",this.cancel+="Call-ID: "+f.headers["Call-ID"].toString()+"\r\n",this.cancel+="CSeq: "+f.headers.CSeq.toString().split(" ")[0]+" CANCEL\r\n",d&&(this.cancel+="Reason: "+d+"\r\n"),e&&(this.cancel+=e),this.cancel+="Content-Length: 0\r\n\r\n",this.state===c.STATUS_PROCEEDING&&this.transport.send(this.cancel)},e.prototype.receiveResponse=function(b){var d=this,e=b.status_code;if(e>=100&&199>=e)switch(this.state){case c.STATUS_CALLING:this.stateChanged(c.STATUS_PROCEEDING),this.request_sender.receiveResponse(b),this.cancel&&this.transport.send(this.cancel);break;case c.STATUS_PROCEEDING:this.request_sender.receiveResponse(b)}else if(e>=200&&299>=e)switch(this.state){case c.STATUS_CALLING:case c.STATUS_PROCEEDING:this.stateChanged(c.STATUS_ACCEPTED),this.M=a.Timers.setTimeout(d.timer_M.bind(d),a.Timers.TIMER_M),this.request_sender.receiveResponse(b);break;case c.STATUS_ACCEPTED:this.request_sender.receiveResponse(b)}else if(e>=300&&699>=e)switch(this.state){case c.STATUS_CALLING:case c.STATUS_PROCEEDING:this.stateChanged(c.STATUS_COMPLETED),this.sendACK(b),this.request_sender.receiveResponse(b);break;case c.STATUS_COMPLETED:this.sendACK(b)}};var f=function(a,c,d){var e;this.transport=d,this.id="z9hG4bK"+Math.floor(1e7*Math.random()),this.request_sender=a,this.request=c,this.logger=a.ua.getLogger("sip.transaction.nict",this.id),e=b(a,d,this.id),this.request.setHeader("via",e)};f.prototype=Object.create(a.EventEmitter.prototype),f.prototype.send=function(){this.transport.send(this.request)||this.onTransportError()},f.prototype.onTransportError=function(){this.logger.log("transport error occurred, for an ACK client transaction "+this.id),this.request_sender.onTransportError()};var g=function(a,b){this.type=c.NON_INVITE_SERVER,this.id=a.via_branch,this.request=a,this.transport=a.transport,this.ua=b,this.last_response="",a.server_transaction=this,this.logger=b.getLogger("sip.transaction.nist",this.id),this.state=c.STATUS_TRYING,b.newTransaction(this)};g.prototype=Object.create(a.EventEmitter.prototype),g.prototype.stateChanged=function(a){this.state=a,this.emit("stateChanged")},g.prototype.timer_J=function(){this.logger.log("Timer J expired for non-INVITE server transaction "+this.id),this.stateChanged(c.STATUS_TERMINATED),this.ua.destroyTransaction(this)},g.prototype.onTransportError=function(){this.transportError||(this.transportError=!0,this.logger.log("transport error occurred, deleting non-INVITE server transaction "+this.id),a.Timers.clearTimeout(this.J),this.stateChanged(c.STATUS_TERMINATED),this.ua.destroyTransaction(this))},g.prototype.receiveResponse=function(b,d){var e=this,f=a.Utils.defer();if(100===b)switch(this.state){case c.STATUS_TRYING:this.stateChanged(c.STATUS_PROCEEDING),this.transport.send(d)||this.onTransportError();break;case c.STATUS_PROCEEDING:this.last_response=d,this.transport.send(d)?f.resolve():(this.onTransportError(),f.reject())}else if(b>=200&&699>=b)switch(this.state){case c.STATUS_TRYING:case c.STATUS_PROCEEDING:this.stateChanged(c.STATUS_COMPLETED),this.last_response=d,this.J=a.Timers.setTimeout(e.timer_J.bind(e),a.Timers.TIMER_J),this.transport.send(d)?f.resolve():(this.onTransportError(),f.reject());break;case c.STATUS_COMPLETED:}return f.promise};var h=function(a,b){this.type=c.INVITE_SERVER,this.id=a.via_branch,this.request=a,this.transport=a.transport,this.ua=b,this.last_response="",a.server_transaction=this,this.logger=b.getLogger("sip.transaction.ist",this.id),this.state=c.STATUS_PROCEEDING,b.newTransaction(this),this.resendProvisionalTimer=null,a.reply(100)};h.prototype=Object.create(a.EventEmitter.prototype),h.prototype.stateChanged=function(a){this.state=a,this.emit("stateChanged")},h.prototype.timer_H=function(){this.logger.log("Timer H expired for INVITE server transaction "+this.id),this.state===c.STATUS_COMPLETED&&this.logger.warn("transactions","ACK for INVITE server transaction was never received, call will be terminated"),this.stateChanged(c.STATUS_TERMINATED),this.ua.destroyTransaction(this)},h.prototype.timer_I=function(){this.stateChanged(c.STATUS_TERMINATED),this.ua.destroyTransaction(this)},h.prototype.timer_L=function(){this.logger.log("Timer L expired for INVITE server transaction "+this.id),this.state===c.STATUS_ACCEPTED&&(this.stateChanged(c.STATUS_TERMINATED),this.ua.destroyTransaction(this))},h.prototype.onTransportError=function(){this.transportError||(this.transportError=!0,this.logger.log("transport error occurred, deleting INVITE server transaction "+this.id),null!==this.resendProvisionalTimer&&(a.Timers.clearInterval(this.resendProvisionalTimer),this.resendProvisionalTimer=null),a.Timers.clearTimeout(this.L),a.Timers.clearTimeout(this.H),a.Timers.clearTimeout(this.I),this.stateChanged(c.STATUS_TERMINATED),this.ua.destroyTransaction(this))},h.prototype.resend_provisional=function(){this.transport.send(this.last_response)||this.onTransportError()},h.prototype.receiveResponse=function(b,d){var e=this,f=a.Utils.defer();if(b>=100&&199>=b)switch(this.state){case c.STATUS_PROCEEDING:this.transport.send(d)||this.onTransportError(),this.last_response=d}if(b>100&&199>=b&&this.state===c.STATUS_PROCEEDING)null===this.resendProvisionalTimer&&(this.resendProvisionalTimer=a.Timers.setInterval(e.resend_provisional.bind(e),a.Timers.PROVISIONAL_RESPONSE_INTERVAL));else if(b>=200&&299>=b)switch(this.state){case c.STATUS_PROCEEDING:this.stateChanged(c.STATUS_ACCEPTED),this.last_response=d,this.L=a.Timers.setTimeout(e.timer_L.bind(e),a.Timers.TIMER_L),null!==this.resendProvisionalTimer&&(a.Timers.clearInterval(this.resendProvisionalTimer),this.resendProvisionalTimer=null);case c.STATUS_ACCEPTED:this.transport.send(d)?f.resolve():(this.onTransportError(),f.reject())}else if(b>=300&&699>=b)switch(this.state){case c.STATUS_PROCEEDING:null!==this.resendProvisionalTimer&&(a.Timers.clearInterval(this.resendProvisionalTimer),this.resendProvisionalTimer=null),this.transport.send(d)?(this.stateChanged(c.STATUS_COMPLETED),this.H=a.Timers.setTimeout(e.timer_H.bind(e),a.Timers.TIMER_H),f.resolve()):(this.onTransportError(),f.reject())}return f.promise};var i=function(b,d){var e;switch(d.method){case a.C.INVITE:if(e=b.transactions.ist[d.via_branch]){switch(e.state){case c.STATUS_PROCEEDING:e.transport.send(e.last_response);break;case c.STATUS_ACCEPTED:}return!0}break;case a.C.ACK:if(e=b.transactions.ist[d.via_branch],!e)return!1;if(e.state===c.STATUS_ACCEPTED)return!1;if(e.state===c.STATUS_COMPLETED)return e.stateChanged(c.STATUS_CONFIRMED),e.I=a.Timers.setTimeout(e.timer_I.bind(e),a.Timers.TIMER_I),!0;break;case a.C.CANCEL:return e=b.transactions.ist[d.via_branch],e?(d.reply_sl(200),e.state===c.STATUS_PROCEEDING?!1:!0):(d.reply_sl(481),!0);default:if(e=b.transactions.nist[d.via_branch]){switch(e.state){case c.STATUS_TRYING:break;case c.STATUS_PROCEEDING:case c.STATUS_COMPLETED:e.transport.send(e.last_response)}return!0}}};a.Transactions={C:c,checkTransaction:i,NonInviteClientTransaction:d,InviteClientTransaction:e,AckClientTransaction:f,NonInviteServerTransaction:g,InviteServerTransaction:h}}},{}],28:[function(a,b,c){"use strict";b.exports=function(a,b){function c(a){var b=.8*a;return 1e3*(Math.random()*(a-b)+b)}var d,e={STATUS_READY:0,STATUS_DISCONNECTED:1,STATUS_ERROR:2};return d=function(a,b){this.logger=a.getLogger("sip.transport"),this.ua=a,this.ws=null,this.server=b,this.reconnection_attempts=0,this.closed=!1,this.connected=!1,this.reconnectTimer=null,this.lastTransportError={},this.keepAliveInterval=a.configuration.keepAliveInterval,this.keepAliveTimeout=null,this.keepAliveTimer=null,this.ua.transport=this,this.connect()},d.prototype={send:function(a){var c=a.toString();return this.ws&&this.ws.readyState===b.OPEN?(this.ua.configuration.traceSip===!0&&this.logger.log("sending WebSocket message:\n\n"+c+"\n"),this.ws.send(c),!0):(this.logger.warn("unable to send message, WebSocket is not open"),!1)},sendKeepAlive:function(){return this.keepAliveTimeout?void 0:(this.keepAliveTimeout=a.Timers.setTimeout(function(){this.ua.emit("keepAliveTimeout")}.bind(this),1e4),this.send("\r\n\r\n"))},startSendingKeepAlives:function(){this.keepAliveInterval&&!this.keepAliveTimer&&(this.keepAliveTimer=a.Timers.setTimeout(function(){this.sendKeepAlive(),this.keepAliveTimer=null,this.startSendingKeepAlives()}.bind(this),c(this.keepAliveInterval)))},stopSendingKeepAlives:function(){a.Timers.clearTimeout(this.keepAliveTimer),a.Timers.clearTimeout(this.keepAliveTimeout),this.keepAliveTimer=null,this.keepAliveTimeout=null},disconnect:function(){this.ws&&(a.Timers.clearTimeout(this.reconnectTimer),this.stopSendingKeepAlives(),this.closed=!0,this.logger.log("closing WebSocket "+this.server.ws_uri),this.ws.close(),this.ws=null),null!==this.reconnectTimer&&(a.Timers.clearTimeout(this.reconnectTimer),this.reconnectTimer=null,this.ua.emit("disconnected",{transport:this,code:this.lastTransportError.code,reason:this.lastTransportError.reason}))},connect:function(){var a=this;if(this.ws&&(this.ws.readyState===b.OPEN||this.ws.readyState===b.CONNECTING))return this.logger.log("WebSocket "+this.server.ws_uri+" is already connected"),!1;this.ws&&(this.ws.close(),this.ws=null),this.logger.log("connecting to WebSocket "+this.server.ws_uri),this.ua.onTransportConnecting(this,0===this.reconnection_attempts?1:this.reconnection_attempts);try{this.ws=new b(this.server.ws_uri,"sip")}catch(c){this.logger.warn("error connecting to WebSocket "+this.server.ws_uri+": "+c)}this.ws.binaryType="arraybuffer",this.ws.onopen=function(){a.onOpen()},this.ws.onclose=function(b){a.onClose(b),this.onopen=null,this.onclose=null,this.onmessage=null,this.onerror=null},this.ws.onmessage=function(b){a.onMessage(b)},this.ws.onerror=function(b){a.onError(b)}},onOpen:function(){this.connected=!0,this.logger.log("WebSocket "+this.server.ws_uri+" connected"),null!==this.reconnectTimer&&(a.Timers.clearTimeout(this.reconnectTimer),this.reconnectTimer=null),this.reconnection_attempts=0,this.closed=!1,this.ua.onTransportConnected(this),this.startSendingKeepAlives()},onClose:function(a){var b=this.connected;this.lastTransportError.code=a.code,this.lastTransportError.reason=a.reason,this.stopSendingKeepAlives(),this.reconnection_attempts>0?(this.logger.log("Reconnection attempt "+this.reconnection_attempts+" failed (code: "+a.code+(a.reason?"| reason: "+a.reason:"")+")"),this.reconnect()):(this.connected=!1,this.logger.log("WebSocket disconnected (code: "+a.code+(a.reason?"| reason: "+a.reason:"")+")"),a.wasClean===!1&&this.logger.warn("WebSocket abrupt disconnection"),b===!0?(this.ua.onTransportClosed(this),this.closed?this.ua.emit("disconnected",{transport:this,code:this.lastTransportError.code,reason:this.lastTransportError.reason}):this.reconnect()):this.ua.onTransportError(this))},onMessage:function(b){var c,d,e=b.data;if("\r\n"===e)return a.Timers.clearTimeout(this.keepAliveTimeout),this.keepAliveTimeout=null,void(this.ua.configuration.traceSip===!0&&this.logger.log("received WebSocket message with CRLF Keep Alive response"));if("string"!=typeof e){try{e=String.fromCharCode.apply(null,new Uint8Array(e))}catch(f){return void this.logger.warn("received WebSocket binary message failed to be converted into string, message discarded")}this.ua.configuration.traceSip===!0&&this.logger.log("received WebSocket binary message:\n\n"+e+"\n")}else this.ua.configuration.traceSip===!0&&this.logger.log("received WebSocket text message:\n\n"+e+"\n");if(c=a.Parser.parseMessage(e,this.ua),c&&!(this.ua.status===a.UA.C.STATUS_USER_CLOSED&&c instanceof a.IncomingRequest)&&a.sanityCheck(c,this.ua,this))if(c instanceof a.IncomingRequest)c.transport=this,this.ua.receiveRequest(c);else if(c instanceof a.IncomingResponse)switch(c.method){case a.C.INVITE:d=this.ua.transactions.ict[c.via_branch],d&&d.receiveResponse(c);break;case a.C.ACK:break;default:d=this.ua.transactions.nict[c.via_branch],d&&d.receiveResponse(c)}},onError:function(a){this.logger.warn("WebSocket connection error: "+JSON.stringify(a))},reconnect:function(){var b=this;this.reconnection_attempts+=1,this.reconnection_attempts>this.ua.configuration.wsServerMaxReconnection?(this.logger.warn("maximum reconnection attempts for WebSocket "+this.server.ws_uri),this.ua.onTransportError(this)):1===this.reconnection_attempts?(this.logger.log("Connection to WebSocket "+this.server.ws_uri+" severed, attempting first reconnect"),b.connect()):(this.logger.log("trying to reconnect to WebSocket "+this.server.ws_uri+" (reconnection attempt "+this.reconnection_attempts+")"),this.reconnectTimer=a.Timers.setTimeout(function(){b.connect(),b.reconnectTimer=null},1e3*this.ua.configuration.wsServerReconnectionTimeout))}},d.C=e,d}},{}],29:[function(a,b,c){(function(a){"use strict";b.exports=function(b,c){function d(a){return a instanceof Function?(a.initialize||(a.initialize=function(){return b.Utils.Promise.resolve()}),a):void 0}var e,f={STATUS_INIT:0,STATUS_STARTING:1,STATUS_READY:2,STATUS_USER_CLOSED:3,STATUS_NOT_READY:4,CONFIGURATION_ERROR:1,NETWORK_ERROR:2,ALLOWED_METHODS:["ACK","CANCEL","INVITE","MESSAGE","BYE","OPTIONS","INFO","NOTIFY","REFER"],ACCEPTED_BODY_TYPES:["application/sdp","application/dtmf-relay"],MAX_FORWARDS:70,TAG_LENGTH:10};e=function(a){function c(a){return d.emit.bind(d,a)}var d=this;f.ACCEPTED_BODY_TYPES=f.ACCEPTED_BODY_TYPES.toString(),this.log=new b.LoggerFactory,this.logger=this.getLogger("sip.ua"),this.cache={credentials:{}},this.configuration={},this.dialogs={},this.applicants={},this.data={},this.sessions={},this.subscriptions={},this.earlySubscriptions={},this.transport=null,this.contact=null,this.status=f.STATUS_INIT,this.error=null,this.transactions={nist:{},nict:{},ist:{},ict:{}},this.transportRecoverAttempts=0,this.transportRecoveryTimer=null,Object.defineProperties(this,{transactionsCount:{get:function(){var a,b=["nist","nict","ist","ict"],c=0;for(a in b)c+=Object.keys(this.transactions[b[a]]).length;return c}},nictTransactionsCount:{get:function(){return Object.keys(this.transactions.nict).length}},nistTransactionsCount:{get:function(){return Object.keys(this.transactions.nist).length}},ictTransactionsCount:{get:function(){return Object.keys(this.transactions.ict).length}},istTransactionsCount:{get:function(){return Object.keys(this.transactions.ist).length}}}),void 0===a?a={}:("string"==typeof a||a instanceof String)&&(a={uri:a}),a.log&&(a.log.hasOwnProperty("builtinEnabled")&&(this.log.builtinEnabled=a.log.builtinEnabled),a.log.hasOwnProperty("level")&&(this.log.level=a.log.level),a.log.hasOwnProperty("connector")&&(this.log.connector=a.log.connector));try{this.loadConfig(a)}catch(e){throw this.status=f.STATUS_NOT_READY,this.error=f.CONFIGURATION_ERROR,e}this.registerContext=new b.RegisterContext(this),this.registerContext.on("failed",c("registrationFailed")),this.registerContext.on("registered",c("registered")),this.registerContext.on("unregistered",c("unregistered")),this.configuration.autostart&&this.start()},e.prototype=Object.create(b.EventEmitter.prototype),e.prototype.register=function(a){return this.configuration.register=!0,this.registerContext.register(a),this},e.prototype.unregister=function(a){this.configuration.register=!1;var b=this.registerContext;return this.afterConnected(b.unregister.bind(b,a)),this},e.prototype.isRegistered=function(){return this.registerContext.registered},e.prototype.isConnected=function(){return this.transport?this.transport.connected:!1},e.prototype.afterConnected=function(a){this.isConnected()?a():this.once("connected",a)},e.prototype.invite=function(a,c){var d=new b.InviteClientContext(this,a,c);return this.afterConnected(d.invite.bind(d)),this.emit("inviteSent",d),d},e.prototype.subscribe=function(a,c,d){var e=new b.Subscription(this,a,c,d);return this.afterConnected(e.subscribe.bind(e)),e},e.prototype.message=function(a,c,d){if(void 0===c)throw new TypeError("Not enough arguments");return d=Object.create(d||Object.prototype),d.contentType||(d.contentType="text/plain"),d.body=c,this.request(b.C.MESSAGE,a,d)},e.prototype.request=function(a,c,d){var e=new b.ClientContext(this,a,c,d);return this.afterConnected(e.send.bind(e)),e},e.prototype.stop=function(){function d(){0===i.nistTransactionsCount&&0===i.nictTransactionsCount&&(i.removeListener("transactionDestroyed",d),i.transport.disconnect())}var e,g,h,i=this;if(this.logger.log("user requested closure..."),this.status===f.STATUS_USER_CLOSED)return this.logger.warn("UA already closed"),this;b.Timers.clearTimeout(this.transportRecoveryTimer),this.logger.log("closing registerContext"),this.registerContext.close();for(e in this.sessions)this.logger.log("closing session "+e),this.sessions[e].terminate();for(g in this.subscriptions)this.logger.log("unsubscribing from subscription "+g),this.subscriptions[g].close();for(g in this.earlySubscriptions)this.logger.log("unsubscribing from early subscription "+g),this.earlySubscriptions[g].close();for(h in this.applicants)this.applicants[h].close();return this.status=f.STATUS_USER_CLOSED,0===this.nistTransactionsCount&&0===this.nictTransactionsCount?this.transport.disconnect():this.on("transactionDestroyed",d),"function"==typeof c.removeEventListener&&(a.chrome&&a.chrome.app&&a.chrome.app.runtime||c.removeEventListener("unload",this.environListener)),this},e.prototype.start=function(){var d;return this.logger.log("user requested startup..."),this.status===f.STATUS_INIT?(d=this.getNextWsServer(),this.status=f.STATUS_STARTING,new b.Transport(this,d)):this.status===f.STATUS_USER_CLOSED?(this.logger.log("resuming"),this.status=f.STATUS_READY,this.transport.connect()):this.status===f.STATUS_STARTING?this.logger.log("UA is in STARTING status, not opening new connection"):this.status===f.STATUS_READY?this.logger.log("UA is in READY status, not resuming"):this.logger.error("Connection is down. Auto-Recovery system is trying to connect"),this.configuration.autostop&&"function"==typeof c.addEventListener&&(a.chrome&&a.chrome.app&&a.chrome.app.runtime||(this.environListener=this.stop.bind(this),c.addEventListener("unload",this.environListener))),this},e.prototype.normalizeTarget=function(a){return b.Utils.normalizeTarget(a,this.configuration.hostportParams)},e.prototype.saveCredentials=function(a){return this.cache.credentials[a.realm]=this.cache.credentials[a.realm]||{},this.cache.credentials[a.realm][a.uri]=a,this},e.prototype.getCredentials=function(a){var b,c;return b=a.ruri.host,this.cache.credentials[b]&&this.cache.credentials[b][a.ruri]&&(c=this.cache.credentials[b][a.ruri],c.method=a.method),c},e.prototype.getLogger=function(a,b){return this.log.getLogger(a,b)},e.prototype.onTransportClosed=function(a){var c,d,e,f=["nict","ict","nist","ist"];for(a.server.status=b.Transport.C.STATUS_DISCONNECTED,this.logger.log("connection state set to "+b.Transport.C.STATUS_DISCONNECTED),e=f.length,c=0;e>c;c++)for(d in this.transactions[f[c]])this.transactions[f[c]][d].onTransportError();this.contact.pub_gruu||this.closeSessionsOnTransportError()},e.prototype.onTransportError=function(a){var c;this.logger.log("transport "+a.server.ws_uri+" failed | connection state set to "+b.Transport.C.STATUS_ERROR),a.server.status=b.Transport.C.STATUS_ERROR,this.emit("disconnected",{transport:a}),this.status!==f.STATUS_USER_CLOSED&&(c=this.getNextWsServer(),c?new b.Transport(this,c):(this.closeSessionsOnTransportError(),this.error&&this.error===f.NETWORK_ERROR||(this.status=f.STATUS_NOT_READY,this.error=f.NETWORK_ERROR),this.recoverTransport()))},e.prototype.onTransportConnected=function(a){this.transport=a,this.transportRecoverAttempts=0,a.server.status=b.Transport.C.STATUS_READY,this.logger.log("connection state set to "+b.Transport.C.STATUS_READY),this.status!==f.STATUS_USER_CLOSED&&(this.status=f.STATUS_READY,this.error=null,this.configuration.register&&this.configuration.authenticationFactory.initialize().then(function(){this.registerContext.onTransportConnected()}.bind(this)),this.emit("connected",{transport:a}))},e.prototype.onTransportConnecting=function(a,b){this.emit("connecting",{transport:a,attempts:b})},e.prototype.newTransaction=function(a){this.transactions[a.type][a.id]=a,this.emit("newTransaction",{transaction:a})},e.prototype.destroyTransaction=function(a){delete this.transactions[a.type][a.id],this.emit("transactionDestroyed",{transaction:a})},e.prototype.receiveRequest=function(a){function c(b){return b&&b.user===a.ruri.user}var d,e,g,h,i,j,k,l=a.method,m=this;if(!(c(this.configuration.uri)||c(this.contact.uri)||c(this.contact.pub_gruu)||c(this.contact.temp_gruu)))return this.logger.warn("Request-URI does not point to us"),void(a.method!==b.C.ACK&&a.reply_sl(404));if(a.ruri.scheme===b.C.SIPS)return void a.reply_sl(416);if(!b.Transactions.checkTransaction(this,a))if(l===b.C.OPTIONS?(new b.Transactions.NonInviteServerTransaction(a,this),a.reply(200,null,["Allow: "+b.UA.C.ALLOWED_METHODS.toString(),"Accept: "+f.ACCEPTED_BODY_TYPES])):l===b.C.MESSAGE?(g=new b.ServerContext(this,a),g.body=a.body,g.content_type=a.getHeader("Content-Type")||"text/plain",a.reply(200,null),this.emit("message",g)):l!==b.C.INVITE&&l!==b.C.ACK&&(i=new b.ServerContext(this,a)),a.to_tag)d=this.findDialog(a),d?(l===b.C.INVITE&&new b.Transactions.InviteServerTransaction(a,this),d.receiveRequest(a)):l===b.C.NOTIFY?(e=this.findSession(a),h=this.findEarlySubscription(a),e?e.receiveRequest(a):h?h.receiveRequest(a):(this.logger.warn("received NOTIFY request for a non existent session or subscription"),a.reply(481,"Subscription does not exist"))):l!==b.C.ACK&&a.reply(481);else switch(l){case b.C.INVITE:if(j=this.configuration.replaces!==b.C.supported.UNSUPPORTED&&a.parseHeader("replaces")){if(k=this.dialogs[j.call_id+j.replaces_to_tag+j.replaces_from_tag],!k)return void a.reply_sl(481,null);if(k.owner.status===b.Session.C.STATUS_TERMINATED)return void a.reply_sl(603,null);if(k.state===b.Dialog.C.STATUS_CONFIRMED&&j.early_only)return void a.reply_sl(486,null)}var n=this.configuration.mediaHandlerFactory.isSupported;!n||n()?(e=new b.InviteServerContext(this,a),e.replacee=k&&k.owner,e.on("invite",function(){m.emit("invite",this)})):(this.logger.warn("INVITE received but WebRTC is not supported"),a.reply(488));break;case b.C.BYE:a.reply(481);break;case b.C.CANCEL:e=this.findSession(a),e?e.receiveRequest(a):this.logger.warn("received CANCEL request for a non existent session");break;case b.C.ACK:break;case b.C.NOTIFY:this.configuration.allowLegacyNotifications&&this.listeners("notify").length>0?(a.reply(200,null),m.emit("notify",{request:a})):a.reply(481,"Subscription does not exist");break;default:a.reply(405)}},e.prototype.findSession=function(a){return this.sessions[a.call_id+a.from_tag]||this.sessions[a.call_id+a.to_tag]||null},e.prototype.findDialog=function(a){return this.dialogs[a.call_id+a.from_tag+a.to_tag]||this.dialogs[a.call_id+a.to_tag+a.from_tag]||null},e.prototype.findEarlySubscription=function(a){return this.earlySubscriptions[a.call_id+a.to_tag+a.getHeader("event")]||null},e.prototype.getNextWsServer=function(){var a,c,d,e=[];for(c=this.configuration.wsServers.length,a=0;c>a;a++)d=this.configuration.wsServers[a],d.status!==b.Transport.C.STATUS_ERROR&&(0===e.length?e.push(d):d.weight>e[0].weight?e=[d]:d.weight===e[0].weight&&e.push(d));return a=Math.floor(Math.random()*e.length),e[a]},e.prototype.closeSessionsOnTransportError=function(){var a;for(a in this.sessions)this.sessions[a].onTransportError();this.registerContext.onTransportClosed()},e.prototype.recoverTransport=function(a){var c,d,e,f,g,h;for(a=a||this,g=a.transportRecoverAttempts,d=a.configuration.wsServers.length,c=0;d>c;c++)a.configuration.wsServers[c].status=0;h=a.getNextWsServer(),e=Math.floor(Math.random()*Math.pow(2,g)+1),f=e*a.configuration.connectionRecoveryMinInterval,f>a.configuration.connectionRecoveryMaxInterval&&(this.logger.log("time for next connection attempt exceeds connectionRecoveryMaxInterval, resetting counter"),f=a.configuration.connectionRecoveryMinInterval,g=0),this.logger.log("next connection attempt in "+f+" seconds"),this.transportRecoveryTimer=b.Timers.setTimeout(function(){a.transportRecoverAttempts=g+1,new b.Transport(a,h)},1e3*f)},e.prototype.loadConfig=function(a){function c(b,c){var d=b.replace(/([a-z][A-Z])/g,function(a){return a[0]+"_"+a[1].toLowerCase()});if(b!==d){var e=a.hasOwnProperty(b);a.hasOwnProperty(d)&&(c.warn(d+" is deprecated, please use "+b),e&&c.warn(b+" overriding "+d)),a[b]=e?a[b]:a[d]}}var e,f,g,h,i,j={viaHost:b.Utils.createRandomToken(12)+".invalid",uri:new b.URI("sip","anonymous."+b.Utils.createRandomToken(6),"anonymous.invalid",null,null),wsServers:[{scheme:"WSS",sip_uri:"<sip:edge.sip.onsip.com;transport=ws;lr>",status:0,weight:0,ws_uri:"wss://edge.sip.onsip.com"}],custom:{},displayName:"",password:null,registerExpires:600,register:!0,registrarServer:null,wsServerMaxReconnection:3,wsServerReconnectionTimeout:4,connectionRecoveryMinInterval:2,connectionRecoveryMaxInterval:30,keepAliveInterval:0,extraSupported:[],usePreloadedRoute:!1,userAgentString:b.C.USER_AGENT,iceCheckingTimeout:5e3,noAnswerTimeout:60,stunServers:["stun:stun.l.google.com:19302"],turnServers:[],traceSip:!1,hackViaTcp:!1,hackIpInContact:!1,hackWssInTransport:!1,hackAllowUnregisteredOptionTags:!1,hackCleanJitsiSdpImageattr:!1,hackStripTcp:!1,contactTransport:"ws",forceRport:!1,autostart:!0,autostop:!0,rel100:b.C.supported.UNSUPPORTED,replaces:b.C.supported.UNSUPPORTED,mediaHandlerFactory:b.WebRTC.MediaHandler.defaultFactory,authenticationFactory:d(function(a){return new b.DigestAuthentication(a)}),allowLegacyNotifications:!1},k=this.getConfigurationCheck();for(e in k.mandatory){if(c(e,this.logger),!a.hasOwnProperty(e))throw new b.Exceptions.ConfigurationError(e);if(f=a[e],g=k.mandatory[e](f),void 0===g)throw new b.Exceptions.ConfigurationError(e,f);j[e]=g}b.Utils.optionsOverride(a,"rel100","reliable",!0,this.logger,b.C.supported.UNSUPPORTED);var l=["stunServers","turnServers"];for(e in k.optional)if(c(e,this.logger),a.hasOwnProperty(e)){if(f=a[e],f instanceof Array&&0===f.length&&l.indexOf(e)<0)continue;if(null===f||""===f||void 0===f)continue;if("number"==typeof f&&isNaN(f))continue;if(g=k.optional[e](f),void 0===g)throw new b.Exceptions.ConfigurationError(e,f);j[e]=g}if(j.connectionRecoveryMaxInterval<j.connectionRecoveryMinInterval)throw new b.Exceptions.ConfigurationError("connectionRecoveryMaxInterval",j.connectionRecoveryMaxInterval);0===j.displayName&&(j.displayName="0"),j.instanceId||(j.instanceId=b.Utils.newUUID()),j.sipjsId=b.Utils.createRandomToken(5),h=j.uri.clone(),h.user=null,j.hostportParams=h.toRaw().replace(/^sip:/i,""),j.authorizationUser||(j.authorizationUser=j.uri.user),
j.registrarServer||(i=j.uri.clone(),i.user=null,j.registrarServer=i),j.noAnswerTimeout=1e3*j.noAnswerTimeout,j.hackIpInContact&&("boolean"==typeof j.hackIpInContact?j.viaHost=b.Utils.getRandomTestNetIP():"string"==typeof j.hackIpInContact&&(j.viaHost=j.hackIpInContact)),j.hackWssInTransport&&(j.contactTransport="wss"),this.contact={pub_gruu:null,temp_gruu:null,uri:new b.URI("sip",b.Utils.createRandomToken(8),j.viaHost,null,{transport:j.contactTransport}),toString:function(a){a=a||{};var b=a.anonymous||null,c=a.outbound||null,d="<";return d+=b?(this.temp_gruu||"sip:<EMAIL>;transport="+j.contactTransport).toString():(this.pub_gruu||this.uri).toString(),c&&(d+=";ob"),d+=">"}},b.Utils.optionsOverride(j,"media","mediaConstraints",!0,this.logger);var m={};for(e in j)m[e]={value:j[e],writable:"register"===e||"custom"===e,configurable:!1};Object.defineProperties(this.configuration,m),this.logger.log("configuration parameters after validation:");for(e in j)switch(e){case"uri":case"registrarServer":case"mediaHandlerFactory":this.logger.log("\xb7 "+e+": "+j[e]);break;case"password":this.logger.log("\xb7 "+e+": NOT SHOWN");break;default:this.logger.log("\xb7 "+e+": "+JSON.stringify(j[e]))}},e.prototype.getConfigurationCheck=function(){return{mandatory:{},optional:{uri:function(a){var c;return/^sip:/i.test(a)||(a=b.C.SIP+":"+a),c=b.URI.parse(a),c&&c.user?c:void 0},wsServers:function(a){var c,d,e;if("string"==typeof a)a=[{ws_uri:a}];else{if(!(a instanceof Array))return;for(d=a.length,c=0;d>c;c++)"string"==typeof a[c]&&(a[c]={ws_uri:a[c]})}if(0===a.length)return!1;for(d=a.length,c=0;d>c;c++){if(!a[c].ws_uri)return;if(a[c].weight&&!Number(a[c].weight))return;if(e=b.Grammar.parse(a[c].ws_uri,"absoluteURI"),-1===e)return;if(["wss","ws","udp"].indexOf(e.scheme)<0)return;a[c].sip_uri="<sip:"+e.host+(e.port?":"+e.port:"")+";transport="+e.scheme.replace(/^wss$/i,"ws")+";lr>",a[c].weight||(a[c].weight=0),a[c].status=0,a[c].scheme=e.scheme.toUpperCase()}return a},authorizationUser:function(a){return-1===b.Grammar.parse('"'+a+'"',"quoted_string")?void 0:a},connectionRecoveryMaxInterval:function(a){var c;return b.Utils.isDecimal(a)&&(c=Number(a),c>0)?c:void 0},connectionRecoveryMinInterval:function(a){var c;return b.Utils.isDecimal(a)&&(c=Number(a),c>0)?c:void 0},displayName:function(a){return-1===b.Grammar.parse('"'+a+'"',"displayName")?void 0:a},hackViaTcp:function(a){return"boolean"==typeof a?a:void 0},hackIpInContact:function(a){return"boolean"==typeof a?a:"string"==typeof a&&-1!==b.Grammar.parse(a,"host")?a:void 0},iceCheckingTimeout:function(a){return b.Utils.isDecimal(a)?Math.max(500,a):void 0},hackWssInTransport:function(a){return"boolean"==typeof a?a:void 0},hackAllowUnregisteredOptionTags:function(a){return"boolean"==typeof a?a:void 0},hackCleanJitsiSdpImageattr:function(a){return"boolean"==typeof a?a:void 0},hackStripTcp:function(a){return"boolean"==typeof a?a:void 0},contactTransport:function(a){return"string"==typeof a?a:void 0},forceRport:function(a){return"boolean"==typeof a?a:void 0},instanceId:function(a){return"string"==typeof a?(/^uuid:/i.test(a)&&(a=a.substr(5)),-1===b.Grammar.parse(a,"uuid")?void 0:a):void 0},keepAliveInterval:function(a){var c;return b.Utils.isDecimal(a)&&(c=Number(a),c>0)?c:void 0},extraSupported:function(a){var b,c;if(a instanceof Array){for(c=a.length,b=0;c>b;b++)if("string"!=typeof a[b])return;return a}},noAnswerTimeout:function(a){var c;return b.Utils.isDecimal(a)&&(c=Number(a),c>0)?c:void 0},password:function(a){return String(a)},rel100:function(a){return a===b.C.supported.REQUIRED?b.C.supported.REQUIRED:a===b.C.supported.SUPPORTED?b.C.supported.SUPPORTED:b.C.supported.UNSUPPORTED},replaces:function(a){return a===b.C.supported.REQUIRED?b.C.supported.REQUIRED:a===b.C.supported.SUPPORTED?b.C.supported.SUPPORTED:b.C.supported.UNSUPPORTED},register:function(a){return"boolean"==typeof a?a:void 0},registerExpires:function(a){var c;return b.Utils.isDecimal(a)&&(c=Number(a),c>0)?c:void 0},registrarServer:function(a){var c;if("string"==typeof a)return/^sip:/i.test(a)||(a=b.C.SIP+":"+a),c=b.URI.parse(a),c?c.user?void 0:c:void 0},stunServers:function(a){var c,d,e;if("string"==typeof a)a=[a];else if(!(a instanceof Array))return;for(d=a.length,c=0;d>c;c++){if(e=a[c],/^stuns?:/.test(e)||(e="stun:"+e),-1===b.Grammar.parse(e,"stun_URI"))return;a[c]=e}return a},traceSip:function(a){return"boolean"==typeof a?a:void 0},turnServers:function(a){var c,d,e,f,g,h;for(a instanceof Array||(a=[a]),e=a.length,c=0;e>c;c++){if(f=a[c],f.server&&(f.urls=[f.server]),!f.urls)return;for(f.urls instanceof Array?g=f.urls.length:(f.urls=[f.urls],g=1),d=0;g>d;d++)if(h=f.urls[d],/^turns?:/.test(h)||(h="turn:"+h),-1===b.Grammar.parse(h,"turn_URI"))return}return a},rtcpMuxPolicy:function(a){return"string"==typeof a?a:void 0},userAgentString:function(a){return"string"==typeof a?a:void 0},usePreloadedRoute:function(a){return"boolean"==typeof a?a:void 0},wsServerMaxReconnection:function(a){var c;return b.Utils.isDecimal(a)&&(c=Number(a),c>0)?c:void 0},wsServerReconnectionTimeout:function(a){var c;return b.Utils.isDecimal(a)&&(c=Number(a),c>0)?c:void 0},autostart:function(a){return"boolean"==typeof a?a:void 0},autostop:function(a){return"boolean"==typeof a?a:void 0},mediaHandlerFactory:function(a){if(a instanceof Function){var c=function(){function c(a){var c=d[a];if(c.length>1){var e="getDescription"===a;d[a]=b.Utils.promisify(d,a,e)}}var d=a.apply(this,arguments);return c("getDescription"),c("setDescription"),d};return c.isSupported=a.isSupported,c}},authenticationFactory:d,allowLegacyNotifications:function(a){return"boolean"==typeof a?a:void 0},custom:function(a){return"object"==typeof a?a:void 0}}}},e.C=f,b.UA=e}}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],30:[function(a,b,c){"use strict";b.exports=function(a){var b;b=function(b,c,d,e,f,g){var h,i,j,k;if(!d)throw new TypeError('missing or invalid "host" parameter');b=b||a.C.SIP,this.parameters={},this.headers={};for(h in f)this.setParam(h,f[h]);for(i in g)this.setHeader(i,g[i]);j={scheme:b,user:c,host:d,port:e},k={scheme:b.toLowerCase(),user:c,host:d.toLowerCase(),port:e},Object.defineProperties(this,{_normal:{get:function(){return k}},_raw:{get:function(){return j}},scheme:{get:function(){return k.scheme},set:function(a){j.scheme=a,k.scheme=a.toLowerCase()}},user:{get:function(){return k.user},set:function(a){k.user=j.user=a}},host:{get:function(){return k.host},set:function(a){j.host=a,k.host=a.toLowerCase()}},aor:{get:function(){return k.user+"@"+k.host}},port:{get:function(){return k.port},set:function(a){k.port=j.port=0===a?a:parseInt(a,10)||null}}})},b.prototype={setParam:function(a,b){a&&(this.parameters[a.toLowerCase()]="undefined"==typeof b||null===b?null:b.toString().toLowerCase())},getParam:function(a){return a?this.parameters[a.toLowerCase()]:void 0},hasParam:function(a){return a?this.parameters.hasOwnProperty(a.toLowerCase())&&!0||!1:void 0},deleteParam:function(a){var b;return a=a.toLowerCase(),this.parameters.hasOwnProperty(a)?(b=this.parameters[a],delete this.parameters[a],b):void 0},clearParams:function(){this.parameters={}},setHeader:function(b,c){this.headers[a.Utils.headerize(b)]=c instanceof Array?c:[c]},getHeader:function(b){return b?this.headers[a.Utils.headerize(b)]:void 0},hasHeader:function(b){return b?this.headers.hasOwnProperty(a.Utils.headerize(b))&&!0||!1:void 0},deleteHeader:function(b){var c;return b=a.Utils.headerize(b),this.headers.hasOwnProperty(b)?(c=this.headers[b],delete this.headers[b],c):void 0},clearHeaders:function(){this.headers={}},clone:function(){return new b(this._raw.scheme,this._raw.user,this._raw.host,this._raw.port,JSON.parse(JSON.stringify(this.parameters)),JSON.parse(JSON.stringify(this.headers)))},toRaw:function(){return this._toString(this._raw)},toString:function(){return this._toString(this._normal)},_toString:function(b){var c,d,e,f,g=[];f=b.scheme+":",b.scheme.toLowerCase().match("^sips?$")||(f+="//"),b.user&&(f+=a.Utils.escapeUser(b.user)+"@"),f+=b.host,(b.port||0===b.port)&&(f+=":"+b.port);for(d in this.parameters)f+=";"+d,null!==this.parameters[d]&&(f+="="+this.parameters[d]);for(c in this.headers)for(e in this.headers[c])g.push(c+"="+this.headers[c][e]);return g.length>0&&(f+="?"+g.join("&")),f}},b.parse=function(b){return b=a.Grammar.parse(b,"SIP_URI"),-1!==b?b:void 0},a.URI=b}},{}],31:[function(a,b,c){"use strict";b.exports=function(a,b){var c;c={Promise:b.Promise,defer:function(){var a={};return a.promise=new c.Promise(function(b,c){a.resolve=b,a.reject=c}),a},promisify:function(a,b,d){var e=a[b];return function(b,f,g){return new c.Promise(function(c,f){var g=[b,c,f];d&&(g=[c,f,b]),e.apply(a,g)}).then(f,g)}},augment:function(a,b,c,d){var e,f;f=b.prototype;for(e in f)(d||void 0===a[e])&&(a[e]=f[e]);b.apply(a,c)},optionsOverride:function(a,b,c,d,e,f){d&&a[c]&&e.warn(c+" is deprecated, please use "+b+" instead"),a[b]&&a[c]&&e.warn(b+" overriding "+c),a[b]=a[b]||a[c]||f},str_utf8_length:function(a){return encodeURIComponent(a).replace(/%[A-F\d]{2}/g,"U").length},generateFakeSDP:function(a){if(a){var b=a.indexOf("o="),c=a.indexOf("\r\n",b);return"v=0\r\n"+a.slice(b,c)+"\r\ns=-\r\nt=0 0\r\nc=IN IP4 0.0.0.0"}},isFunction:function(a){return void 0!==a?"[object Function]"===Object.prototype.toString.call(a):!1},isDecimal:function(a){return!isNaN(a)&&parseFloat(a)===parseInt(a,10)},createRandomToken:function(a,b){var c,d,e="";for(b=b||32,c=0;a>c;c++)d=Math.random()*b|0,e+=d.toString(b);return e},newTag:function(){return a.Utils.createRandomToken(a.UA.C.TAG_LENGTH)},newUUID:function(){var a="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(a){var b=16*Math.random()|0,c="x"===a?b:3&b|8;return c.toString(16)});return a},hostType:function(b){return b?(b=a.Grammar.parse(b,"host"),-1!==b?b.host_type:void 0):void 0},normalizeTarget:function(b,c){var d,e,f,g;if(b){if(b instanceof a.URI)return b;if("string"==typeof b){switch(e=b.split("@"),e.length){case 1:if(!c)return;f=b,g=c;break;case 2:f=e[0],g=e[1];break;default:f=e.slice(0,e.length-1).join("@"),g=e[e.length-1]}return f=f.replace(/^(sips?|tel):/i,""),/^[\-\.\(\)]*\+?[0-9\-\.\(\)]+$/.test(f)&&(f=f.replace(/[\-\.\(\)]/g,"")),b=a.C.SIP+":"+a.Utils.escapeUser(f)+"@"+g,(d=a.URI.parse(b))?d:void 0}}else;},escapeUser:function(a){return encodeURIComponent(decodeURIComponent(a)).replace(/%3A/gi,":").replace(/%2B/gi,"+").replace(/%3F/gi,"?").replace(/%2F/gi,"/")},headerize:function(a){var b,c={"Call-Id":"Call-ID",Cseq:"CSeq","Min-Se":"Min-SE",Rack:"RAck",Rseq:"RSeq","Www-Authenticate":"WWW-Authenticate"},d=a.toLowerCase().replace(/_/g,"-").split("-"),e="",f=d.length;for(b=0;f>b;b++)0!==b&&(e+="-"),e+=d[b].charAt(0).toUpperCase()+d[b].substring(1);return c[e]&&(e=c[e]),e},sipErrorCause:function(b){var c;for(c in a.C.SIP_ERROR_CAUSES)if(-1!==a.C.SIP_ERROR_CAUSES[c].indexOf(b))return a.C.causes[c];return a.C.causes.SIP_FAILURE_CODE},getReasonPhrase:function(b,c){return c||a.C.REASON_PHRASE[b]||""},getReasonHeaderValue:function(b,c){return c=a.Utils.getReasonPhrase(b,c),"SIP ;cause="+b+' ;text="'+c+'"'},getCancelReason:function(b,c){if(b&&200>b||b>699)throw new TypeError("Invalid status_code: "+b);return b?a.Utils.getReasonHeaderValue(b,c):void 0},buildStatusLine:function(a,b){if(a=a||null,b=b||null,!a||100>a||a>699)throw new TypeError("Invalid status_code: "+a);if(b&&"string"!=typeof b&&!(b instanceof String))throw new TypeError("Invalid reason_phrase: "+b);return b=c.getReasonPhrase(a,b),"SIP/2.0 "+a+" "+b+"\r\n"},getRandomTestNetIP:function(){function a(a,b){return Math.floor(Math.random()*(b-a+1)+a)}return"192.0.2."+a(1,254)},calculateMD5:function(a){function b(a,b){return a<<b|a>>>32-b}function c(a,b){var c,d,e,f,g;return e=2147483648&a,f=2147483648&b,c=1073741824&a,d=1073741824&b,g=(1073741823&a)+(1073741823&b),c&d?2147483648^g^e^f:c|d?1073741824&g?3221225472^g^e^f:1073741824^g^e^f:g^e^f}function d(a,b,c){return a&b|~a&c}function e(a,b,c){return a&c|b&~c}function f(a,b,c){return a^b^c}function g(a,b,c){return b^(a|~c)}function h(a,e,f,g,h,i,j){return a=c(a,c(c(d(e,f,g),h),j)),c(b(a,i),e)}function i(a,d,f,g,h,i,j){return a=c(a,c(c(e(d,f,g),h),j)),c(b(a,i),d)}function j(a,d,e,g,h,i,j){return a=c(a,c(c(f(d,e,g),h),j)),c(b(a,i),d)}function k(a,d,e,f,h,i,j){return a=c(a,c(c(g(d,e,f),h),j)),c(b(a,i),d)}function l(a){for(var b,c=a.length,d=c+8,e=(d-d%64)/64,f=16*(e+1),g=Array(f-1),h=0,i=0;c>i;)b=(i-i%4)/4,h=i%4*8,g[b]=g[b]|a.charCodeAt(i)<<h,i++;return b=(i-i%4)/4,h=i%4*8,g[b]=g[b]|128<<h,g[f-2]=c<<3,g[f-1]=c>>>29,g}function m(a){var b,c,d="",e="";for(c=0;3>=c;c++)b=a>>>8*c&255,e="0"+b.toString(16),d+=e.substr(e.length-2,2);return d}function n(a){a=a.replace(/\r\n/g,"\n");for(var b="",c=0;c<a.length;c++){var d=a.charCodeAt(c);128>d?b+=String.fromCharCode(d):d>127&&2048>d?(b+=String.fromCharCode(d>>6|192),b+=String.fromCharCode(63&d|128)):(b+=String.fromCharCode(d>>12|224),b+=String.fromCharCode(d>>6&63|128),b+=String.fromCharCode(63&d|128))}return b}var o,p,q,r,s,t,u,v,w,x=[],y=7,z=12,A=17,B=22,C=5,D=9,E=14,F=20,G=4,H=11,I=16,J=23,K=6,L=10,M=15,N=21;for(a=n(a),x=l(a),t=1732584193,u=4023233417,v=2562383102,w=271733878,o=0;o<x.length;o+=16)p=t,q=u,r=v,s=w,t=h(t,u,v,w,x[o+0],y,3614090360),w=h(w,t,u,v,x[o+1],z,3905402710),v=h(v,w,t,u,x[o+2],A,606105819),u=h(u,v,w,t,x[o+3],B,3250441966),t=h(t,u,v,w,x[o+4],y,4118548399),w=h(w,t,u,v,x[o+5],z,1200080426),v=h(v,w,t,u,x[o+6],A,2821735955),u=h(u,v,w,t,x[o+7],B,4249261313),t=h(t,u,v,w,x[o+8],y,1770035416),w=h(w,t,u,v,x[o+9],z,2336552879),v=h(v,w,t,u,x[o+10],A,4294925233),u=h(u,v,w,t,x[o+11],B,2304563134),t=h(t,u,v,w,x[o+12],y,1804603682),w=h(w,t,u,v,x[o+13],z,4254626195),v=h(v,w,t,u,x[o+14],A,2792965006),u=h(u,v,w,t,x[o+15],B,1236535329),t=i(t,u,v,w,x[o+1],C,4129170786),w=i(w,t,u,v,x[o+6],D,3225465664),v=i(v,w,t,u,x[o+11],E,643717713),u=i(u,v,w,t,x[o+0],F,3921069994),t=i(t,u,v,w,x[o+5],C,3593408605),w=i(w,t,u,v,x[o+10],D,38016083),v=i(v,w,t,u,x[o+15],E,3634488961),u=i(u,v,w,t,x[o+4],F,3889429448),t=i(t,u,v,w,x[o+9],C,568446438),w=i(w,t,u,v,x[o+14],D,3275163606),v=i(v,w,t,u,x[o+3],E,4107603335),u=i(u,v,w,t,x[o+8],F,1163531501),t=i(t,u,v,w,x[o+13],C,2850285829),w=i(w,t,u,v,x[o+2],D,4243563512),v=i(v,w,t,u,x[o+7],E,1735328473),u=i(u,v,w,t,x[o+12],F,2368359562),t=j(t,u,v,w,x[o+5],G,4294588738),w=j(w,t,u,v,x[o+8],H,2272392833),v=j(v,w,t,u,x[o+11],I,1839030562),u=j(u,v,w,t,x[o+14],J,4259657740),t=j(t,u,v,w,x[o+1],G,2763975236),w=j(w,t,u,v,x[o+4],H,1272893353),v=j(v,w,t,u,x[o+7],I,4139469664),u=j(u,v,w,t,x[o+10],J,3200236656),t=j(t,u,v,w,x[o+13],G,681279174),w=j(w,t,u,v,x[o+0],H,3936430074),v=j(v,w,t,u,x[o+3],I,3572445317),u=j(u,v,w,t,x[o+6],J,76029189),t=j(t,u,v,w,x[o+9],G,3654602809),w=j(w,t,u,v,x[o+12],H,3873151461),v=j(v,w,t,u,x[o+15],I,530742520),u=j(u,v,w,t,x[o+2],J,3299628645),t=k(t,u,v,w,x[o+0],K,4096336452),w=k(w,t,u,v,x[o+7],L,1126891415),v=k(v,w,t,u,x[o+14],M,2878612391),u=k(u,v,w,t,x[o+5],N,4237533241),t=k(t,u,v,w,x[o+12],K,1700485571),w=k(w,t,u,v,x[o+3],L,2399980690),v=k(v,w,t,u,x[o+10],M,4293915773),u=k(u,v,w,t,x[o+1],N,2240044497),t=k(t,u,v,w,x[o+8],K,1873313359),w=k(w,t,u,v,x[o+15],L,4264355552),v=k(v,w,t,u,x[o+6],M,2734768916),u=k(u,v,w,t,x[o+13],N,1309151649),t=k(t,u,v,w,x[o+4],K,4149444226),w=k(w,t,u,v,x[o+11],L,3174756917),v=k(v,w,t,u,x[o+2],M,718787259),u=k(u,v,w,t,x[o+9],N,3951481745),t=c(t,p),u=c(u,q),v=c(v,r),w=c(w,s);var O=m(t)+m(u)+m(v)+m(w);return O.toLowerCase()}},a.Utils=c}},{}],32:[function(a,b,c){"use strict";b.exports=function(b,c){var d;d={},d.MediaHandler=a("./WebRTC/MediaHandler")(b),d.MediaStreamManager=a("./WebRTC/MediaStreamManager")(b,c);var e;return d.isSupported=function(){return void 0!==e?e:(d.MediaStream=c.MediaStream,d.getUserMedia=c.getUserMedia,d.RTCPeerConnection=c.RTCPeerConnection,d.RTCSessionDescription=c.RTCSessionDescription,d.RTCPeerConnection&&d.RTCSessionDescription?(d.getUserMedia&&(d.getUserMedia=b.Utils.promisify(c,"getUserMedia")),e=!0):e=!1,e)},d}},{"./WebRTC/MediaHandler":33,"./WebRTC/MediaStreamManager":34}],33:[function(a,b,c){"use strict";b.exports=function(a){var b=function(b,c){function d(a,b){a.mediaStreamManager.on&&a.mediaStreamManager.on(b,function(){a.emit.apply(a,[b].concat(Array.prototype.slice.call(arguments)))})}c=c||{},this.logger=b.ua.getLogger("sip.invitecontext.mediahandler",b.id),this.session=b,this.localMedia=null,this.ready=!0,this.mediaStreamManager=c.mediaStreamManager||new a.WebRTC.MediaStreamManager(this.logger),this.audioMuted=!1,this.videoMuted=!1,this.local_hold=!1,this.remote_hold=!1;var e=this.prepareIceServers(c.stunServers,c.turnServers);this.RTCConstraints=c.RTCConstraints||{},this.initPeerConnection(e),d(this,"userMediaRequest"),d(this,"userMedia"),d(this,"userMediaFailed")};return b.defaultFactory=function(a,c){return new b(a,c)},b.defaultFactory.isSupported=function(){return a.WebRTC.isSupported()},b.prototype=Object.create(a.MediaHandler.prototype,{isReady:{writable:!0,value:function(){return this.ready}},close:{writable:!0,value:function(){this.logger.log("closing PeerConnection"),this._remoteStreams=[],this.peerConnection&&"closed"!==this.peerConnection.signalingState&&(this.peerConnection.close(),this.localMedia&&this.mediaStreamManager.release(this.localMedia))}},getDescription:{writable:!0,value:function(b){var c=this,d=c.mediaStreamManager.acquire;d.length>1&&(d=a.Utils.promisify(this.mediaStreamManager,"acquire",!0)),b=b||{},b.dataChannel===!0&&(b.dataChannel={}),this.mediaHint=b;var e;return c.localMedia?(c.logger.log("already have local media"),e=a.Utils.Promise.resolve(c.localMedia)):(c.logger.log("acquiring local media"),e=d.call(c.mediaStreamManager,b).then(function(a){return c.logger.log("acquired local media streams"),c.localMedia=a,c.session.connecting(),a},function(a){throw c.logger.error("unable to acquire streams"),c.logger.error(a),c.session.connecting(),a}).then(this.addStreams.bind(this))),e.then(function(){return c.hasOffer("remote")?c.peerConnection.ondatachannel=function(a){c.dataChannel=a.channel,c.emit("dataChannel",c.dataChannel)}:b.dataChannel&&c.peerConnection.createDataChannel&&(c.dataChannel=c.peerConnection.createDataChannel("sipjs",b.dataChannel),c.emit("dataChannel",c.dataChannel)),c.render(),c.createOfferOrAnswer(c.RTCConstraints)}).then(function(b){return b=a.Hacks.Firefox.hasMissingCLineInSDP(b),c.local_hold&&(/a=(sendrecv|sendonly|recvonly|inactive)/.test(b)?(b=b.replace(/a=sendrecv\r\n/g,"a=sendonly\r\n"),b=b.replace(/a=recvonly\r\n/g,"a=inactive\r\n")):b=b.replace(/(m=[^\r]*\r\n)/g,"$1a=sendonly\r\n")),{body:b,contentType:"application/sdp"}})}},hasDescription:{writeable:!0,value:function(a){return"application/sdp"===a.getHeader("Content-Type")&&!!a.body}},setDescription:{writable:!0,value:function(b){var c=this,d=b.body;this.remote_hold=/a=(sendonly|inactive)/.test(d),d=a.Hacks.Firefox.cannotHandleExtraWhitespace(d),d=a.Hacks.AllBrowsers.maskDtls(d);var e={type:this.hasOffer("local")?"answer":"offer",sdp:d};this.emit("setDescription",e);var f=new a.WebRTC.RTCSessionDescription(e);return a.Utils.promisify(this.peerConnection,"setRemoteDescription")(f)["catch"](function(a){throw c.emit("peerConnection-setRemoteDescriptionFailed",a),a})}},getReferMedia:{writable:!0,value:function(){function a(a,b){return b[a]().length>0}function b(b){return this.getLocalStreams().some(a.bind(null,b))&&this.getRemoteStreams().some(a.bind(null,b))}return{constraints:{audio:b.call(this,"getAudioTracks"),video:b.call(this,"getVideoTracks")}}}},updateIceServers:{writeable:!0,value:function(a){var b=this.prepareIceServers(a.stunServers,a.turnServers);this.RTCConstraints=a.RTCConstraints||this.RTCConstraints,this.initPeerConnection(b)}},isMuted:{writable:!0,value:function(){return{audio:this.audioMuted,video:this.videoMuted}}},mute:{writable:!0,value:function(a){if(0!==this.getLocalStreams().length){a=a||{audio:this.getLocalStreams()[0].getAudioTracks().length>0,video:this.getLocalStreams()[0].getVideoTracks().length>0};var b=!1,c=!1;return a.audio&&!this.audioMuted&&(b=!0,this.audioMuted=!0,this.toggleMuteAudio(!0)),a.video&&!this.videoMuted&&(c=!0,this.videoMuted=!0,this.toggleMuteVideo(!0)),b||c?{audio:b,video:c}:void 0}}},unmute:{writable:!0,value:function(a){if(0!==this.getLocalStreams().length){a=a||{audio:this.getLocalStreams()[0].getAudioTracks().length>0,video:this.getLocalStreams()[0].getVideoTracks().length>0};var b=!1,c=!1;return a.audio&&this.audioMuted&&(b=!0,this.audioMuted=!1,this.toggleMuteAudio(!1)),a.video&&this.videoMuted&&(c=!0,this.videoMuted=!1,this.toggleMuteVideo(!1)),b||c?{audio:b,video:c}:void 0}}},hold:{writable:!0,value:function(){this.local_hold=!0,this.toggleMuteAudio(!0),this.toggleMuteVideo(!0)}},unhold:{writable:!0,value:function(){this.local_hold=!1,this.audioMuted||this.toggleMuteAudio(!1),this.videoMuted||this.toggleMuteVideo(!1)}},getLocalStreams:{writable:!0,value:function(){var a=this.peerConnection;return a&&"closed"===a.signalingState?(this.logger.warn("peerConnection is closed, getLocalStreams returning []"),[]):a.getLocalStreams&&a.getLocalStreams()||a.localStreams||[]}},getRemoteStreams:{writable:!0,value:function(){var a=this.peerConnection;return a&&"closed"===a.signalingState?(this.logger.warn("peerConnection is closed, getRemoteStreams returning this._remoteStreams"),this._remoteStreams):a.getRemoteStreams&&a.getRemoteStreams()||a.remoteStreams||[]}},render:{writable:!0,value:function(b){if(b=b||this.mediaHint&&this.mediaHint.render,!b)return!1;var c={local:"getLocalStreams",remote:"getRemoteStreams"};Object.keys(c).forEach(function(d){var e=c[d],f=this[e]();a.WebRTC.MediaStreamManager.render(f,b[d])}.bind(this))}},hasOffer:{writable:!0,value:function(a){var b="have-"+a+"-offer";return this.peerConnection.signalingState===b}},prepareIceServers:{writable:!0,value:function(a,b){var c=[],d=this.session.ua.configuration;return a=a||d.stunServers,b=b||d.turnServers,[].concat(a).forEach(function(a){c.push({urls:a})}),[].concat(b).forEach(function(a){var b={urls:a.urls};a.username&&(b.username=a.username),a.password&&(b.credential=a.password),c.push(b)}),c}},initPeerConnection:{writable:!0,value:function(b){var c=this,d=this.session.ua.configuration;this.onIceCompleted=a.Utils.defer(),this.onIceCompleted.promise.then(function(b){c.emit("iceGatheringComplete",b),c.iceCheckingTimer&&(a.Timers.clearTimeout(c.iceCheckingTimer),c.iceCheckingTimer=null)}),this.peerConnection&&this.peerConnection.close();var e={iceServers:b};d.rtcpMuxPolicy&&(e.rtcpMuxPolicy=d.rtcpMuxPolicy),this.peerConnection=new a.WebRTC.RTCPeerConnection(e),this._remoteStreams=[],this.peerConnection.onaddstream=function(a){c.logger.log("stream added: "+a.stream.id),c._remoteStreams.push(a.stream),c.render(),c.emit("addStream",a)},this.peerConnection.onremovestream=function(a){c.logger.log("stream removed: "+a.stream.id)},this.startIceCheckingTimer=function(){c.iceCheckingTimer||(c.iceCheckingTimer=a.Timers.setTimeout(function(){c.logger.log("RTCIceChecking Timeout Triggered after "+d.iceCheckingTimeout+" milliseconds"),c.onIceCompleted.resolve(this)}.bind(this.peerConnection),d.iceCheckingTimeout))},this.peerConnection.onicecandidate=function(a){c.emit("iceCandidate",a),a.candidate?(c.logger.log("ICE candidate received: "+(null===a.candidate.candidate?null:a.candidate.candidate.trim())),c.startIceCheckingTimer()):c.onIceCompleted.resolve(this)},this.peerConnection.onicegatheringstatechange=function(){c.logger.log("RTCIceGatheringState changed: "+this.iceGatheringState),"gathering"===this.iceGatheringState&&c.emit("iceGathering",this),"complete"===this.iceGatheringState&&c.onIceCompleted.resolve(this)},this.peerConnection.oniceconnectionstatechange=function(){var a;switch("checking"===this.iceConnectionState&&c.startIceCheckingTimer(),this.iceConnectionState){case"new":a="iceConnection";break;case"checking":a="iceConnectionChecking";break;case"connected":a="iceConnectionConnected";break;case"completed":a="iceConnectionCompleted";break;case"failed":a="iceConnectionFailed";break;case"disconnected":a="iceConnectionDisconnected";break;case"closed":a="iceConnectionClosed";break;default:return void c.logger.warn("Unknown iceConnection state:",this.iceConnectionState)}c.emit(a,this)},this.peerConnection.onstatechange=function(){c.logger.log('PeerConnection state changed to "'+this.readyState+'"')}}},createOfferOrAnswer:{writable:!0,value:function(b){var c,d=this,e=d.peerConnection;return d.ready=!1,c=d.hasOffer("remote")?"createAnswer":"createOffer",a.Utils.promisify(e,c,!0)(b)["catch"](function(a){throw d.emit("peerConnection-"+c+"Failed",a),a}).then(a.Utils.promisify(e,"setLocalDescription"))["catch"](function(a){throw d.emit("peerConnection-selLocalDescriptionFailed",a),a}).then(function(){var b=a.Utils.defer();return"complete"!==e.iceGatheringState||"connected"!==e.iceConnectionState&&"completed"!==e.iceConnectionState?d.onIceCompleted.promise.then(b.resolve):b.resolve(),b.promise}).then(function(){var b=e.localDescription.sdp;b=a.Hacks.Chrome.needsExplicitlyInactiveSDP(b),b=a.Hacks.AllBrowsers.unmaskDtls(b);var f={type:"createOffer"===c?"offer":"answer",sdp:b};return d.emit("getDescription",f),d.session.ua.configuration.hackStripTcp&&(f.sdp=f.sdp.replace(/^a=candidate:\d+ \d+ tcp .*?\r\n/gim,"")),d.ready=!0,f.sdp})["catch"](function(b){throw d.logger.error(b),d.ready=!0,new a.Exceptions.GetDescriptionError(b)})}},addStreams:{writable:!0,value:function(b){try{b=[].concat(b),b.forEach(function(a){this.peerConnection.addStream(a)},this)}catch(c){return this.logger.error("error adding stream"),this.logger.error(c),a.Utils.Promise.reject(c)}return a.Utils.Promise.resolve()}},toggleMuteHelper:{writable:!0,value:function(a,b){this.getLocalStreams().forEach(function(c){c[a]().forEach(function(a){a.enabled=!b})})}},toggleMuteAudio:{writable:!0,value:function(a){this.toggleMuteHelper("getAudioTracks",a)}},toggleMuteVideo:{writable:!0,value:function(a){this.toggleMuteHelper("getVideoTracks",a)}}}),b}},{}],34:[function(a,b,c){"use strict";b.exports=function(a,b){var c=function(b,c){if(!a.WebRTC.isSupported())throw new a.Exceptions.NotSupportedError("Media not supported");this.mediaHint=c||{constraints:{audio:!0,video:!0}},this.acquisitions={}};return c.streamId=function(a){return a.getAudioTracks().concat(a.getVideoTracks()).map(function(a){return a.id}).join("")},c.render=function(c,d){function e(a,b){a.srcObject=b}function f(b){var c=100;b.ensurePlayingIntervalId=a.Timers.setInterval(function(){b.paused&&b.srcObject?b.play():a.Timers.clearInterval(b.ensurePlayingIntervalId)},c)}function g(a,c,d){var g=a[d%a.length];"function"==typeof g&&(g=g()),(b.attachMediaStream||e)(g,c),f(g)}if(!d)return!1;if(Array.isArray(d)&&!d.length)throw new TypeError("elements must not be empty");d=[].concat(d),[].concat(c).forEach(g.bind(null,d))},c.prototype=Object.create(a.EventEmitter.prototype,{acquire:{writable:!0,value:function(b){b=Object.keys(b||{}).length?b:this.mediaHint;var d=function(b,d){return d=[].concat(d),d.forEach(function(a){var d=c.streamId(a);this.acquisitions[d]=!!b},this),a.Utils.Promise.resolve(d)}.bind(this);if(b.stream)return d(!0,b.stream);var e=b.constraints||this.mediaHint&&this.mediaHint.constraints||{audio:!0,video:!0},f=a.Utils.defer();return a.Timers.setTimeout(function(){this.emit("userMediaRequest",e);var b=function(a,b){var c=Array.prototype.slice.call(arguments,2),d=[a].concat(c);return this.emit.apply(this,d),b.apply(null,c)}.bind(this);e.audio||e.video?f.resolve(a.WebRTC.getUserMedia(e).then(b.bind(this,"userMedia",d.bind(null,!1)),b.bind(this,"userMediaFailed",function(a){throw a}))):f.resolve([])}.bind(this),0),f.promise}},release:{writable:!0,value:function(a){a=[].concat(a),a.forEach(function(a){var b=c.streamId(a);this.acquisitions[b]===!1&&a.getTracks().forEach(function(a){a.stop()}),delete this.acquisitions[b]},this)}}}),c}},{}],35:[function(a,b,c){(function(c){"use strict";function d(a,b){if(null!=a){var c=b.charAt(0).toUpperCase()+b.slice(1),d=[b,"webkit"+c,"moz"+c];for(var e in d){var f=a[d[e]];if(f)return f.bind(a)}}}var e=c.window||c;b.exports={WebSocket:e.WebSocket,Transport:a("./Transport"),open:e.open,Promise:e.Promise,timers:e,console:e.console||{debug:function(){},log:function(){},warn:function(){},error:function(){}},MediaStream:d(e,"MediaStream"),getUserMedia:d(e.navigator,"getUserMedia"),RTCPeerConnection:d(e,"RTCPeerConnection"),RTCSessionDescription:d(e,"RTCSessionDescription"),addEventListener:d(e,"addEventListener"),removeEventListener:d(e,"removeEventListener"),HTMLMediaElement:e.HTMLMediaElement,attachMediaStream:e.attachMediaStream,createObjectURL:e.URL&&e.URL.createObjectURL,revokeObjectURL:e.URL&&e.URL.revokeObjectURL}}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./Transport":28}],36:[function(a,b,c){"use strict";b.exports=a("./SIP")(a("./environment"))},{"./SIP":19,"./environment":35}]},{},[36])(36)});