r.op = req.query.op
r.page_name = req.query.page_name || req.query.id
console.log('r.op', r.op)
if (r.op == 'rm') {
    gr(
        'api_account_page_rm',
        [req.oid, req.gid, req.uid, req.IP, r.page_name],
        function (row) {
            // tr.page_update(row.id, '');
            return res.json({success: true, resutl: row})
        },
        true
    )
} else if (r.op == 'save') {
    gr(
        'api_account_page_save',
        [req.oid, req.gid, req.uid, req.IP, req.body.page_name, req.body.page_content, req.query.mode],
        function (row) {
            // tr.page_update(row.id, req.body.page_content);
            return res.json({success: true, resutl: row})
        },
        true
    )
} else {
    gr(
        'api_account_page_by_id',
        [req.oid, req.gid, req.uid, req.IP, r.page_name],
        function (row) {
            r.page = row
            r.mode = req.query.mode || row.page_mode
            if (!r.mode) r.mode = 'visual'
            return tr.display_adm(req, res, automake_name, r)
        },
        true
    )
}
