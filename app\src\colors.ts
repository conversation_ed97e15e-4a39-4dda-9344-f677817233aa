import {Logger} from 'tslog'
const logger = new Logger({type: 'pretty'})
import colors from 'colors'
import util from 'util'
const to_string = (args: any[]) => {
    return args
        .map(arg => {
            if (typeof arg === 'object') return JSON.stringify(arg, null, 2)
            return arg
        })
        .join(' ')
}
const at_ignores = [
    'err',
    'DB.GR',
    'DB.GA',
    'DB.GO',
    'DB.sql',
    'PromisePool.execute',
    'node:internal',
    'node_modules',
    'colors.ts',
    'at()',
    'green',
    'yellow',
    'red',
    'blue',
    'magenta',
    'cyan',
    'gray',
    'white',
    'fd_call',
    'call',
    'gr',
    'ga',
    'bg_blue',
    'bg_green',
    'bg_yellow',
    'bg_red',
    'bg_magenta',
    'bg_gray',
    'bg_cyan',
    'sql',
    'display',
    'logger'
]
export const at = (_tracing: boolean = true) => {
    if (!_tracing) return ''
    const stack = new Error().stack
    if (!stack) return ''
    const stack_lines = stack.split('\n')
    for (let i = 0; i < stack_lines.length; i++) {
        const line = stack_lines[i].trim()
        if (!at_ignores.some(ignore => line.includes(ignore))) {
            const match = line.match(/at\s+(.+)\s+\((.+):(\d+):(\d+)\)/)
            if (!match) continue
            const [, , file_path, line_number] = match
            let file = file_path.replace(new RegExp(`^/`), '')
            return colors.gray(`${file}:${line_number} `)
        }
    }
    return ''
}
const log = (bg: colors.Color, ...args: any[]) => {
    try {
        const str = `${at()} ${bg(to_string(args))}`
        if (bg === colors.bgRed || bg === colors.red) logger.error(str)
        else if (bg === colors.bgGreen || bg === colors.green) logger.info(str)
        else if (bg === colors.bgYellow || bg === colors.yellow) logger.warn(str)
        else if (bg === colors.bgBlue || bg === colors.blue) logger.info(str)
        else if (bg === colors.bgMagenta || bg === colors.magenta) logger.debug(str)
        else if (bg === colors.bgCyan || bg === colors.cyan) logger.info(str)
        else logger.info(str)
    } catch (error) {
        logger.error(error)
        process.exit(0)
    }
}
const blue = (...args: any[]) => log(colors.blue, ...args)
const green = (...args: any[]) => log(colors.green, ...args)
const yellow = (...args: any[]) => log(colors.yellow, ...args)
const red = (...args: any[]) => log(colors.red, ...args)
const magenta = (...args: any[]) => log(colors.magenta, ...args)
const gray = (...args: any[]) => log(colors.gray, ...args)
const cyan = (...args: any[]) => log(colors.bgCyan, ...args)
const info = (...args: any[]) => log(colors.blue, ...args)
const success = (...args: any[]) => log(colors.green, ...args)
const warning = (...args: any[]) => log(colors.yellow, ...args)
const error = (...args: any[]) => log(colors.red, ...args)
const debug = (...args: any[]) => log(colors.magenta, ...args)
const bg_blue = (...args: any[]) => log(colors.bgBlue, ...args)
const bg_green = (...args: any[]) => log(colors.bgGreen, ...args)
const bg_yellow = (...args: any[]) => log(colors.bgYellow, ...args)
const bg_red = (...args: any[]) => log(colors.bgRed, ...args)
const bg_magenta = (...args: any[]) => log(colors.bgMagenta, ...args)
const bg_gray = (...args: any[]) => log(colors.bgBlack.white, ...args)
const bg_cyan = (...args: any[]) => log(colors.bgCyan, ...args)
const die = (...args: any[]) => {
    log(colors.bgRed.yellow, ...args)
    process.exit(0)
}
const funny = (...args: any) => log(colors.bold.bgWhite.yellow, ...args)
const inspect = (...args: any) =>
    log(
        colors.blue,
        util.inspect(args, {
            colors: true,
            depth: 10,
            compact: true,
            sorted: true
        })
    )
const dump = inspect
const trace = inspect
let mark_index = 0
const mark_time_start = new Date()
const mark = (...args: any[]) => {
    const time = new Date()
    const time_diff = time.getTime() - mark_time_start.getTime()
    const secs = Math.floor(time_diff / 1000)
    log(colors.bgRed.red, `--MARK-- [${mark_index++}] ${secs}s`, ...args)
}
const box = (...args: any) => {
    const {str, title, bg, box_width} = args
    if (!str) return
    const horizontal_line = '─'.repeat(box_width - 2)
    const padded_title = ` ${title} `.padEnd(box_width - 2, '─')
    const lines = str.split('\n')
    const wrapped_content = lines.flatMap((line: string) => {
        const initial_spaces = line.match(/^\s*/)?.[0] || ''
        const trimmed_line = line.trim()
        const max_line_width = box_width - 4 - initial_spaces.length
        return trimmed_line.length <= max_line_width
            ? [initial_spaces + trimmed_line]
            : trimmed_line.match(new RegExp(`.{1,${max_line_width}}(\\s|$)`, 'g'))?.map((segment: string) => initial_spaces + segment.trimEnd()) || []
    })
    let res = `┌${padded_title}┐\n`
    res += wrapped_content.map((line: string) => `│ ${line.padEnd(box_width - 4)} │`).join('\n')
    res += `\n└${horizontal_line}┘`
    logger.info(bg(res))
}
const white = (...args: any[]) => log(colors.white, ...args)
export {
    red,
    green,
    yellow,
    gray,
    blue,
    magenta,
    cyan,
    error,
    success,
    warning,
    info,
    log,
    debug,
    trace,
    dump,
    inspect,
    funny,
    die,
    mark,
    box,
    bg_blue,
    bg_green,
    bg_yellow,
    bg_red,
    bg_magenta,
    bg_gray,
    bg_cyan,
    white
}
