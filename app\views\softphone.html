<!doctype html>
<html lang="en" data-bs-theme="light">
    <head>
        <meta charset="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
        <title>INOVA WebPhone</title>
        <script
            src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"
            integrity="sha512-v2CJ7UaYy4JwqLDIrZUI/4hqeoQieOmAZNXBeQyjo21dadnwR+8ZaIJVT8EE2iyI61OV8e6M8PP2/4hpQINQ/g=="
            crossorigin="anonymous"
            referrerpolicy="no-referrer"></script>
        <script
            src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js"
            integrity="sha384-I7E8VVD/ismYTF4hNIPjVp/Zjvgyol6VFvRkX/vR+Vc4jQkC+hVqc2pM8ODewa9r"
            crossorigin="anonymous"></script>
        <script
            src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.7/js/bootstrap.min.js"
            integrity="sha512-zKeerWHHuP3ar7kX2WKBSENzb+GJytFSBL6HrR2nPSR1kOX1qjm+oHooQtbDpDBSITgyl7QXZApvDfDWvKjkUw=="
            crossorigin="anonymous"
            referrerpolicy="no-referrer"></script>
        <link
            rel="stylesheet"
            href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.7/css/bootstrap.min.css"
            integrity="sha512-fw7f+TcMjTb7bpbLJZlP8g2Y4XcCyFZW8uy8HsRZsH/SwbMw0plKHFHr99DN3l04VsYNwvzicUX/6qurvIxbxw=="
            crossorigin="anonymous"
            referrerpolicy="no-referrer" />
        <link
            rel="stylesheet"
            href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css"
            integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg=="
            crossorigin="anonymous"
            referrerpolicy="no-referrer" />
        <link
            rel="stylesheet"
            href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"
            integrity="sha512-c42qTSw/wPZ3/5LBzD+Bw5f7bSF2oxou6wEb+I/lqeaKV5FDIfMvvRp772y4jcJLKuGUOpbJMdg/BTl50fJYAw=="
            crossorigin="anonymous"
            referrerpolicy="no-referrer" />
        <script
            src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.30.1/moment-with-locales.min.js"
            integrity="sha512-4F1cxYdMiAW98oomSLaygEwmCnIP38pb4Kx70yQYqRwLVCs3DbRumfBq82T08g/4LJ/smbFGFpmeFlQgoDccgg=="
            crossorigin="anonymous"
            referrerpolicy="no-referrer"></script>
        <script
            src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"
            integrity="sha512-z4OUqw38qNLpn1libAN9BsoDx6nbNFio5lA6CuTp9NlK83b89hgyCVq+N5FdBJptINztxn1Z3SaKSKUS5UP60Q=="
            crossorigin="anonymous"
            referrerpolicy="no-referrer"></script>
        <script src="/phone/scripts/sip-0.21.2.min.js"></script>

        <style>
            :root {
                --softphone-primary: var(--bs-primary);
                --softphone-secondary: var(--bs-secondary);
                --softphone-success: var(--bs-success);
                --softphone-danger: var(--bs-danger);
                --softphone-warning: var(--bs-warning);
                --softphone-info: var(--bs-info);
                --softphone-background: var(--bs-body-bg);
                --softphone-text: var(--bs-body-color);
                --softphone-border: var(--bs-border-color);
            }

            .softphone-container {
                width: 320px;
                background: var(--softphone-background);
                border: 1px solid var(--softphone-border);
                border-radius: 0.75rem;
                box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
                overflow: hidden;
                transition: all 0.3s ease;
            }

            .softphone-header {
                background: linear-gradient(135deg, var(--softphone-primary), var(--softphone-info));
                color: white;
                padding: 1rem;
                position: relative;
            }

            .softphone-status {
                background: rgba(255, 255, 255, 0.1);
                border-radius: 0.5rem;
                padding: 0.5rem;
                margin-top: 0.5rem;
                backdrop-filter: blur(10px);
            }

            .dialpad {
                padding: 1rem;
                background: var(--softphone-background);
            }

            .dialpad-button {
                width: 70px;
                height: 70px;
                border-radius: 50%;
                border: 2px solid var(--softphone-border);
                background: var(--softphone-background);
                color: var(--softphone-text);
                font-size: 1.5rem;
                font-weight: 600;
                margin: 0.25rem;
                transition: all 0.2s ease;
                cursor: pointer;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
            }

            .dialpad-button:hover {
                background: var(--softphone-primary);
                color: white;
                transform: translateY(-2px);
                box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15);
            }

            .dialpad-button:active {
                transform: translateY(0);
            }

            .dialpad-button small {
                font-size: 0.6rem;
                font-weight: 400;
                opacity: 0.7;
            }

            .call-controls {
                padding: 1rem;
                border-top: 1px solid var(--softphone-border);
            }

            .btn-call {
                background: var(--softphone-success);
                border: none;
                color: white;
                border-radius: 50px;
                padding: 0.75rem 2rem;
                font-weight: 600;
                transition: all 0.3s ease;
            }

            .btn-call:hover {
                background: var(--softphone-success);
                transform: translateY(-2px);
                box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            }

            .btn-hangup {
                background: var(--softphone-danger);
                border: none;
                color: white;
                border-radius: 50px;
                padding: 0.75rem 2rem;
                font-weight: 600;
                transition: all 0.3s ease;
            }

            .btn-hangup:hover {
                background: var(--softphone-danger);
                transform: translateY(-2px);
                box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            }

            .call-log {
                max-height: 300px;
                overflow-y: auto;
                border-top: 1px solid var(--softphone-border);
            }

            .call-log-item {
                padding: 0.75rem 1rem;
                border-bottom: 1px solid rgba(var(--bs-border-color-rgb), 0.5);
                transition: background-color 0.2s ease;
            }

            .call-log-item:hover {
                background: rgba(var(--bs-primary-rgb), 0.1);
            }

            .theme-toggle {
                position: absolute;
                top: 1rem;
                right: 1rem;
                background: rgba(255, 255, 255, 0.2);
                border: 1px solid rgba(255, 255, 255, 0.3);
                color: white;
                border-radius: 50%;
                width: 40px;
                height: 40px;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                transition: all 0.3s ease;
            }

            .theme-toggle:hover {
                background: rgba(255, 255, 255, 0.3);
                transform: rotate(180deg);
            }

            .number-display {
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.3);
                color: white;
                border-radius: 0.5rem;
                padding: 0.75rem;
                font-size: 1.25rem;
                text-align: center;
                backdrop-filter: blur(10px);
            }

            .number-display::placeholder {
                color: rgba(255, 255, 255, 0.7);
            }

            .volume-control {
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 50%;
                width: 40px;
                height: 40px;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                transition: all 0.3s ease;
            }

            .volume-control:hover {
                background: rgba(255, 255, 255, 0.3);
            }

            .status-indicator {
                width: 12px;
                height: 12px;
                border-radius: 50%;
                display: inline-block;
                margin-right: 0.5rem;
                animation: pulse 2s infinite;
            }

            .status-connected {
                background: var(--softphone-success);
            }

            .status-disconnected {
                background: var(--softphone-danger);
            }

            .status-connecting {
                background: var(--softphone-warning);
            }

            @keyframes pulse {
                0% {
                    opacity: 1;
                }
                50% {
                    opacity: 0.5;
                }
                100% {
                    opacity: 1;
                }
            }

            .fade-in {
                animation: fadeIn 0.5s ease-in;
            }

            @keyframes fadeIn {
                from {
                    opacity: 0;
                    transform: translateY(20px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            [data-bs-theme='dark'] {
                --softphone-background: var(--bs-dark);
                --softphone-text: var(--bs-light);
            }

            .toast-container {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 1050;
            }
        </style>
    </head>
    <body>
        <div class="container-fluid d-flex justify-content-center align-items-center min-vh-100 p-3">
            <div class="softphone-container animate__animated animate__fadeIn">
                <div class="softphone-header">
                    <button class="theme-toggle" id="themeToggle" title="Alternar tema">
                        <i class="fas fa-sun"></i>
                    </button>

                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <h5 class="mb-0">
                                <i class="fas fa-phone me-2"></i>
                                INOVA WebPhone
                            </h5>
                            <small class="opacity-75">Ramal: <%=ramal.numero%></small>
                        </div>
                    </div>

                    <div class="softphone-status mt-3">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <span class="status-indicator status-disconnected" id="statusIndicator"></span>
                                <span id="connectionStatus">Desconectado</span>
                            </div>
                            <div class="volume-control" id="volumeControl" title="Controle de volume">
                                <i class="fas fa-volume-up"></i>
                            </div>
                        </div>
                        <div class="mt-2">
                            <div id="callStatus" class="text-center">Pronto para ligação</div>
                        </div>
                    </div>
                </div>

                <div class="p-3 border-bottom">
                    <input type="text" id="numberInput" class="form-control form-control-lg text-center" placeholder="Digite o número..." autocomplete="off" />
                </div>

                <div class="dialpad">
                    <div class="row g-2 justify-content-center">
                        <div class="col-4 text-center">
                            <button class="dialpad-button" data-digit="1">
                                1
                                <small>&nbsp;</small>
                            </button>
                        </div>
                        <div class="col-4 text-center">
                            <button class="dialpad-button" data-digit="2">
                                2
                                <small>ABC</small>
                            </button>
                        </div>
                        <div class="col-4 text-center">
                            <button class="dialpad-button" data-digit="3">
                                3
                                <small>DEF</small>
                            </button>
                        </div>
                        <div class="col-4 text-center">
                            <button class="dialpad-button" data-digit="4">
                                4
                                <small>GHI</small>
                            </button>
                        </div>
                        <div class="col-4 text-center">
                            <button class="dialpad-button" data-digit="5">
                                5
                                <small>JKL</small>
                            </button>
                        </div>
                        <div class="col-4 text-center">
                            <button class="dialpad-button" data-digit="6">
                                6
                                <small>MNO</small>
                            </button>
                        </div>
                        <div class="col-4 text-center">
                            <button class="dialpad-button" data-digit="7">
                                7
                                <small>PQRS</small>
                            </button>
                        </div>
                        <div class="col-4 text-center">
                            <button class="dialpad-button" data-digit="8">
                                8
                                <small>TUV</small>
                            </button>
                        </div>
                        <div class="col-4 text-center">
                            <button class="dialpad-button" data-digit="9">
                                9
                                <small>WXYZ</small>
                            </button>
                        </div>
                        <div class="col-4 text-center">
                            <button class="dialpad-button" data-digit="*">
                                *
                                <small>&nbsp;</small>
                            </button>
                        </div>
                        <div class="col-4 text-center">
                            <button class="dialpad-button" data-digit="0">
                                0
                                <small>+</small>
                            </button>
                        </div>
                        <div class="col-4 text-center">
                            <button class="dialpad-button" data-digit="#">
                                #
                                <small>&nbsp;</small>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="call-controls">
                    <div class="d-grid gap-2">
                        <button class="btn btn-call" id="callButton">
                            <i class="fas fa-phone me-2"></i>
                            Chamar
                        </button>
                        <button class="btn btn-hangup d-none" id="hangupButton">
                            <i class="fas fa-phone-slash me-2"></i>
                            Desligar
                        </button>
                    </div>

                    <div class="row mt-3 g-2">
                        <div class="col">
                            <button class="btn btn-outline-primary btn-sm w-100" id="holdButton" disabled>
                                <i class="fas fa-pause me-1"></i>
                                Hold
                            </button>
                        </div>
                        <div class="col">
                            <button class="btn btn-outline-warning btn-sm w-100" id="muteButton" disabled>
                                <i class="fas fa-microphone me-1"></i>
                                Mudo
                            </button>
                        </div>
                        <div class="col">
                            <button class="btn btn-outline-info btn-sm w-100" id="transferButton" disabled>
                                <i class="fas fa-exchange-alt me-1"></i>
                                Trans.
                            </button>
                        </div>
                    </div>
                </div>

                <div class="call-log" id="callLog">
                    <div class="text-center p-4 text-muted">
                        <i class="fas fa-phone fa-2x mb-2"></i>
                        <p class="mb-0">Nenhuma ligação recente</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="toast-container position-fixed top-0 end-0 p-3"></div>

        <div class="modal fade" id="volumeModal" tabindex="-1">
            <div class="modal-dialog modal-sm">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-volume-up me-2"></i>
                            Volume
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="volumeSlider" class="form-label">Nível do Volume</label>
                            <input type="range" class="form-range" id="volumeSlider" min="0" max="100" value="100" />
                        </div>
                        <div class="text-center">
                            <span id="volumeValue">100%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <audio id="ringtone" src="/phone/sounds/incoming.mp3" loop></audio>
        <audio id="ringbacktone" src="/phone/sounds/outgoing.mp3" loop></audio>
        <audio id="dtmfTone" src="/phone/sounds/dtmf.mp3"></audio>
        <audio id="remoteAudio"></audio>
        <script>
            function toast(message, type = 'danger', timeout = 5000) {
                const toastContainer = $('.toast-container')
                if (toastContainer.length === 0) {
                    $('body').append('<div class="toast-container position-fixed top-0 end-0 p-3"></div>')
                }
                const toastId = 'toast-' + Date.now()
                const toast = $(`
                    <div class="toast align-items-center text-bg-${type} border-0" role="alert" id="${toastId}">
                        <div class="d-flex">
                            <div class="toast-body">${message}</div>
                            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                        </div>
                    </div>
                `)

                $('.toast-container').append(toast)
                const bsToast = new bootstrap.Toast(toast[0], {autohide: timeout > 0, delay: timeout})
                bsToast.show()
                toast.on('hidden.bs.toast', () => toast.remove())
                return bsToast
            }

            function warning(message, timeout = 5000) {
                return toast(message, 'warning', timeout)
            }
            function error(message, timeout = 5000) {
                return toast(message, 'danger', timeout)
            }
            function success(message, timeout = 5000) {
                return toast(message, 'success', timeout)
            }
            function info(message, timeout = 5000) {
                return toast(message, 'info', timeout)
            }
            function debug(message, timeout = 5000) {
                return toast(message, 'secondary', timeout)
            }
            class Softphone {
                simpleUser = null
                websocket = null
                currentCall = null
                isConnected = false
                isRegistered = false
                callHistory = []
                audioElements = {}
                userConfig = {}
                init(numer, senha, endpoint, nome, port) {
                    userConfig = {
                        user: numero,
                        password: senha,
                        realm: endpoint,
                        displayName: nome,
                        wsServer: 'wss://' + endpoint + ':' + port + '/ws'
                    }
                    console.log(`Inicializando Softphone ${numero}@${endpoint}:${port}`)
                    this.initializeAudioElements()
                    this.setupEventListeners()
                    this.initializeTheme()
                    this.connectWebSocket(endpoint, port)
                    this.initializeSipClient()
                }
                connectWebSocket(host, port) {
                    const wsUrl = 'wss://' + host + ':' + port + '/'
                    console.log('Connecting to WebSocket:', wsUrl)
                    this.websocket = new WebSocket(wsUrl)
                    this.websocket.onopen = () => {
                        console.log('WebSocket connected')
                    }
                    this.websocket.onmessage = event => {
                        try {
                            const data = JSON.parse(event.data)
                            this.handleWebSocketMessage(data)
                        } catch (e) {
                            console.error('WebSocket message parse error:', e)
                        }
                    }
                    this.websocket.onclose = () => {
                        console.log('WebSocket disconnected')
                        setTimeout(() => this.connectWebSocket(), 3000)
                    }
                    this.websocket.onerror = error => {
                        console.error('WebSocket error:', error)
                        this.showToast('Erro de conexão WebSocket', 'danger')
                    }
                }
                initializeAudioElements() {
                    this.audioElements = {
                        ringtone: $('#ringtone')[0],
                        ringbacktone: $('#ringbacktone')[0],
                        dtmfTone: $('#dtmfTone')[0],
                        remoteAudio: $('#remoteAudio')[0]
                    }
                }
                setupEventListeners() {
                    $('#themeToggle').on('click', () => this.toggleTheme())
                    $('.dialpad-button').on('click', e => {
                        const digit = $(e.currentTarget).data('digit')
                        this.addDigit(digit)
                    })
                    $('#callButton').on('click', () => this.makeCall())
                    $('#hangupButton').on('click', () => this.hangupCall())
                    $('#holdButton').on('click', () => this.toggleHold())
                    $('#muteButton').on('click', () => this.toggleMute())
                    $('#transferButton').on('click', () => this.transferCall())
                    $('#volumeControl').on('click', () => this.showVolumeModal())
                    $('#volumeSlider').on('input', e => this.updateVolume(e.target.value))
                    $('#numberInput').on('keypress', e => {
                        if (e.which === 13) this.makeCall()
                    })
                }
                initializeTheme() {
                    const savedTheme = localStorage.getItem('softphone-theme') || 'light'
                    this.setTheme(savedTheme)
                }
                toggleTheme() {
                    const currentTheme = $('html').attr('data-bs-theme')
                    const newTheme = currentTheme === 'light' ? 'dark' : 'light'
                    this.setTheme(newTheme)
                    anime({
                        targets: '#themeToggle',
                        rotate: '1turn',
                        duration: 500,
                        easing: 'easeInOutQuad'
                    })
                }
                setTheme(theme) {
                    $('html').attr('data-bs-theme', theme)
                    localStorage.setItem('softphone-theme', theme)

                    const icon = theme === 'light' ? 'fa-moon' : 'fa-sun'
                    $('#themeToggle i').removeClass('fa-sun fa-moon').addClass(icon)
                }

                handleWebSocketMessage(data) {
                    if (data && data[0] === 1 && data[1] === 11) {
                        return
                    }

                    try {
                        if (window.parent) {
                            const app = window.parent.document.getElementById('app')
                            if (app) {
                                const fctl = app.contentWindow[data.event_id]
                                if (fctl) fctl(data.data)
                            }
                        }
                    } catch (e) {
                        console.log('WebSocket event handling error:', e)
                    }
                }

                async initializeSipClient() {
                    try {
                        const delegate = {
                            onCallReceived: () => {
                                console.log('Incoming call received')
                                this.handleIncomingCall()
                            },
                            onCallAnswered: () => {
                                console.log('Call answered')
                                this.handleCallAnswered()
                            },
                            onCallHangup: () => {
                                console.log('Call hangup')
                                this.handleCallHangup()
                            },
                            onCallHold: held => {
                                console.log('Call hold status:', held)
                                this.handleCallHold(held)
                            }
                        }

                        const options = {
                            aor: `sip:${this.userConfig.user}@${this.userConfig.realm}`,
                            delegate: delegate,
                            media: {
                                constraints: {audio: true, video: false},
                                remote: {audio: this.audioElements.remoteAudio}
                            },
                            userAgentOptions: {
                                displayName: this.userConfig.displayName,
                                logLevel: 'warn'
                            }
                        }
                        console.log('Creating SimpleUser with options:', options)
                        this.simpleUser = new SIP.Web.SimpleUser(this.userConfig.wsServer, options)
                        await this.connectSipClient()
                    } catch (error) {
                        console.error('SIP initialization error:', error)
                        this.showToast('Erro ao inicializar SIP: ' + error.message, 'danger')
                        this.updateConnectionStatus('error', 'Erro de inicialização')
                    }
                }
                async connectSipClient() {
                    try {
                        this.updateConnectionStatus('connecting', 'Conectando...')

                        await this.simpleUser.connect()
                        console.log('SIP connected')

                        await this.simpleUser.register()
                        console.log('SIP registered')

                        this.isConnected = true
                        this.isRegistered = true
                        this.updateConnectionStatus('connected', 'Online')
                        this.showToast('Conectado com sucesso!', 'success')
                    } catch (error) {
                        console.error('SIP connection error:', error)
                        this.showToast('Erro de conexão SIP: ' + error.message, 'danger')
                        this.updateConnectionStatus('error', 'Erro de conexão')
                    }
                }

                updateConnectionStatus(status, message) {
                    const indicator = $('#statusIndicator')
                    const statusText = $('#connectionStatus')

                    indicator.removeClass('status-connected status-disconnected status-connecting')

                    switch (status) {
                        case 'connected':
                            indicator.addClass('status-connected')
                            break
                        case 'connecting':
                            indicator.addClass('status-connecting')
                            break
                        case 'error':
                        case 'disconnected':
                            indicator.addClass('status-disconnected')
                            break
                    }

                    statusText.text(message)
                }

                addDigit(digit) {
                    const input = $('#numberInput')
                    input.val(input.val() + digit)
                    this.playDTMF()

                    if (this.currentCall) {
                        try {
                            this.simpleUser.sendDTMF(digit)
                        } catch (e) {
                            console.error('DTMF send error:', e)
                        }
                    }

                    const button = $(`.dialpad-button[data-digit="${digit}"]`)
                    anime({
                        targets: button[0],
                        scale: [1, 0.95, 1],
                        duration: 150,
                        easing: 'easeOutQuad'
                    })
                }

                async makeCall() {
                    const number = $('#numberInput').val().trim()
                    if (!number) {
                        this.showToast('Digite um número para chamar', 'warning')
                        return
                    }

                    if (!this.isRegistered) {
                        this.showToast('SIP não está registrado', 'danger')
                        return
                    }

                    try {
                        $('#callStatus').text('Chamando...')
                        this.playRingbackTone()
                        this.switchToCallMode()

                        const target = number.includes('@') ? number : `sip:${number}@${this.userConfig.realm}`
                        console.log('Making call to:', target)

                        await this.simpleUser.call(target)
                        this.currentCall = target

                        this.addToCallHistory(number, 'outgoing', 'calling')
                    } catch (error) {
                        console.error('Call error:', error)
                        this.showToast('Erro ao fazer ligação: ' + error.message, 'danger')
                        this.stopRingbackTone()
                        this.switchToIdleMode()
                        $('#callStatus').text('Pronto para ligação')
                    }
                }

                async hangupCall() {
                    try {
                        if (this.simpleUser && this.currentCall) {
                            await this.simpleUser.hangup()
                        }
                        this.handleCallHangup()
                    } catch (error) {
                        console.error('Hangup error:', error)
                        this.handleCallHangup()
                    }
                }

                async toggleHold() {
                    if (!this.currentCall) return

                    try {
                        const isOnHold = $('#holdButton').hasClass('btn-warning')

                        if (isOnHold) {
                            await this.simpleUser.unhold()
                            $('#holdButton').removeClass('btn-warning').addClass('btn-outline-primary')
                            $('#holdButton i').removeClass('fa-play').addClass('fa-pause')
                            $('#callStatus').text('Chamada ativa')
                        } else {
                            await this.simpleUser.hold()
                            $('#holdButton').removeClass('btn-outline-primary').addClass('btn-warning')
                            $('#holdButton i').removeClass('fa-pause').addClass('fa-play')
                            $('#callStatus').text('Chamada em espera')
                        }
                    } catch (error) {
                        console.error('Hold error:', error)
                        this.showToast('Erro ao colocar chamada em espera', 'danger')
                    }
                }

                async toggleMute() {
                    if (!this.currentCall) return

                    try {
                        const isMuted = $('#muteButton').hasClass('btn-warning')

                        if (isMuted) {
                            await this.simpleUser.unmute()
                            $('#muteButton').removeClass('btn-warning').addClass('btn-outline-warning')
                            $('#muteButton i').removeClass('fa-microphone-slash').addClass('fa-microphone')
                        } else {
                            await this.simpleUser.mute()
                            $('#muteButton').removeClass('btn-outline-warning').addClass('btn-warning')
                            $('#muteButton i').removeClass('fa-microphone').addClass('fa-microphone-slash')
                        }
                    } catch (error) {
                        console.error('Mute error:', error)
                        this.showToast('Erro ao alterar status do microfone', 'danger')
                    }
                }

                transferCall() {
                    if (!this.currentCall) return

                    const target = prompt('Digite o número para transferir:')
                    if (target && target.trim()) {
                        this.showToast('Transferência não implementada na versão SimpleUser', 'info')
                    }
                }

                handleIncomingCall() {
                    this.stopRingbackTone()
                    this.playRingtone()
                    $('#callStatus').text('Chamada recebida')
                    this.switchToCallMode()
                    this.showIncomingCallToast()
                }

                handleCallAnswered() {
                    this.stopRingtone()
                    this.stopRingbackTone()
                    $('#callStatus').text('Chamada ativa')
                    this.enableCallControls()
                }

                handleCallHangup() {
                    this.stopRingtone()
                    this.stopRingbackTone()
                    this.currentCall = null
                    $('#callStatus').text('Pronto para ligação')
                    this.switchToIdleMode()
                    this.disableCallControls()
                }

                handleCallHold(held) {
                    if (held) {
                        $('#callStatus').text('Chamada em espera')
                        $('#holdButton').removeClass('btn-outline-primary').addClass('btn-warning')
                    } else {
                        $('#callStatus').text('Chamada ativa')
                        $('#holdButton').removeClass('btn-warning').addClass('btn-outline-primary')
                    }
                }

                switchToCallMode() {
                    $('#callButton').addClass('d-none')
                    $('#hangupButton').removeClass('d-none')
                    $('#numberInput').prop('disabled', true)
                }

                switchToIdleMode() {
                    $('#callButton').removeClass('d-none')
                    $('#hangupButton').addClass('d-none')
                    $('#numberInput').prop('disabled', false)
                    $('#numberInput').val('')
                }

                enableCallControls() {
                    $('#holdButton, #muteButton, #transferButton').prop('disabled', false)
                }

                disableCallControls() {
                    $('#holdButton, #muteButton, #transferButton').prop('disabled', true)
                    $('#holdButton').removeClass('btn-warning').addClass('btn-outline-primary')
                    $('#muteButton').removeClass('btn-warning').addClass('btn-outline-warning')
                    $('#holdButton i').removeClass('fa-play').addClass('fa-pause')
                    $('#muteButton i').removeClass('fa-microphone-slash').addClass('fa-microphone')
                }

                playRingtone() {
                    try {
                        this.audioElements.ringtone.play()
                    } catch (e) {
                        console.error('Ringtone play error:', e)
                    }
                }

                stopRingtone() {
                    try {
                        this.audioElements.ringtone.pause()
                        this.audioElements.ringtone.currentTime = 0
                    } catch (e) {
                        console.error('Ringtone stop error:', e)
                    }
                }

                playRingbackTone() {
                    try {
                        this.audioElements.ringbacktone.play()
                    } catch (e) {
                        console.error('Ringback tone play error:', e)
                    }
                }

                stopRingbackTone() {
                    try {
                        this.audioElements.ringbacktone.pause()
                        this.audioElements.ringbacktone.currentTime = 0
                    } catch (e) {
                        console.error('Ringback tone stop error:', e)
                    }
                }

                playDTMF() {
                    try {
                        this.audioElements.dtmfTone.currentTime = 0
                        this.audioElements.dtmfTone.play()
                    } catch (e) {
                        console.error('DTMF play error:', e)
                    }
                }

                showVolumeModal() {
                    const modal = new bootstrap.Modal($('#volumeModal')[0])
                    modal.show()
                }

                updateVolume(value) {
                    $('#volumeValue').text(value + '%')
                    const volume = value / 100

                    Object.values(this.audioElements).forEach(audio => {
                        if (audio) audio.volume = volume
                    })

                    const icon = $('#volumeControl i')
                    icon.removeClass('fa-volume-off fa-volume-down fa-volume-up')

                    if (value == 0) {
                        icon.addClass('fa-volume-off')
                    } else if (value < 50) {
                        icon.addClass('fa-volume-down')
                    } else {
                        icon.addClass('fa-volume-up')
                    }
                }

                addToCallHistory(number, direction, status) {
                    const call = {
                        number: number,
                        direction: direction,
                        status: status,
                        timestamp: new Date(),
                        duration: 0
                    }

                    this.callHistory.unshift(call)
                    this.updateCallLog()
                }

                updateCallLog() {
                    const logContainer = $('#callLog')

                    if (this.callHistory.length === 0) {
                        logContainer.html(`
                            <div class="text-center p-4 text-muted">
                                <i class="fas fa-phone fa-2x mb-2"></i>
                                <p class="mb-0">Nenhuma ligação recente</p>
                            </div>
                        `)
                        return
                    }

                    let html = ''
                    this.callHistory.slice(0, 10).forEach(call => {
                        const icon = call.direction === 'incoming' ? 'fa-arrow-left' : 'fa-arrow-right'
                        const time = moment(call.timestamp).format('HH:mm')

                        html += `
                            <div class="call-log-item">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="fas ${icon} me-2"></i>
                                        <strong>${call.number}</strong>
                                    </div>
                                    <small class="text-muted">${time}</small>
                                </div>
                                <div class="mt-1">
                                    <small class="text-muted">${call.status}</small>
                                </div>
                            </div>
                        `
                    })

                    logContainer.html(html)
                }

                showIncomingCallToast() {
                    this.showToast('Chamada recebida! Clique em Atender', 'info', 0, [
                        {
                            text: 'Atender',
                            class: 'btn-success',
                            action: () => this.simpleUser.answer()
                        },
                        {
                            text: 'Rejeitar',
                            class: 'btn-danger',
                            action: () => this.simpleUser.decline()
                        }
                    ])
                }
                showToast(message, type = 'info', timeout = 5000, buttons = []) {
                    const toastId = 'toast-' + Date.now()
                    const toast = $(`
                        <div class="toast align-items-center text-bg-${type} border-0" role="alert" id="${toastId}">
                            <div class="d-flex">
                                <div class="toast-body">
                                    ${message}
                                    <div class="mt-2" id="${toastId}-buttons"></div>
                                </div>
                                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                            </div>
                        </div>
                    `)
                    $('.toast-container').append(toast)
                    buttons.forEach(btn => {
                        const button = $(`<button type="button" class="btn btn-sm ${btn.class} me-2">${btn.text}</button>`)
                        button.on('click', () => {
                            btn.action()
                            toast.find('.btn-close').click()
                        })
                        $(`#${toastId}-buttons`).append(button)
                    })
                    const bsToast = new bootstrap.Toast(toast[0], {
                        autohide: timeout > 0,
                        delay: timeout
                    })
                    bsToast.show()
                    toast.on('hidden.bs.toast', () => toast.remove())
                }
            }
            window.pickup = function (channel) {
                if (softphone.websocket) {
                    const message = {
                        event_id: 'broadcast',
                        data: {
                            event_id: 'pbxip',
                            data: {
                                cmd: 'pickup',
                                exten: softphone.userConfig.user,
                                channel: channel
                            }
                        }
                    }
                    softphone.websocket.send(JSON.stringify(message))
                }
            }
            window.server_hangup = function (channel) {
                if (softphone.websocket) {
                    const message = {
                        event_id: 'broadcast',
                        data: {
                            event_id: 'pbxip',
                            data: {
                                cmd: 'hangup',
                                channel: channel
                            }
                        }
                    }
                    softphone.websocket.send(JSON.stringify(message))
                }
            }
            window.dial = function (number) {
                $('#numberInput').val(number)
                softphone.makeCall()
            }
            window.hangup = function () {
                softphone.hangupCall()
            }
            let softphone = new Softphone()
            $(document).ready(function () {
                softphone.init()
            })
        </script>
    </body>
</html>
