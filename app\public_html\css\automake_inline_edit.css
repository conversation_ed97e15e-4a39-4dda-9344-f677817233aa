/* Automake Inline Editing Styles */

.editable-field {
    position: relative;
    transition: background-color 0.2s ease;
}

.editable-field:hover {
    background-color: rgba(0, 123, 255, 0.1) !important;
    border: 1px dashed #007bff;
}

.editable-field:hover::after {
    content: 'Duplo clique para editar';
    position: absolute;
    top: -25px;
    left: 50%;
    transform: translateX(-50%);
    background-color: #333;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    white-space: nowrap;
    z-index: 1000;
    pointer-events: none;
    opacity: 0.9;
}

/* Modal styles for non-Bootstrap environments */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1050;
    display: none;
}

.modal.show {
    display: block;
}

.modal-dialog {
    position: relative;
    width: auto;
    margin: 10px;
    max-width: 500px;
    margin: 30px auto;
}

.modal-content {
    background-color: #fff;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 6px;
    box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);
    outline: 0;
}

.modal-header {
    padding: 15px;
    border-bottom: 1px solid #e5e5e5;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-title {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
}

.modal-body {
    padding: 15px;
}

.modal-footer {
    padding: 15px;
    text-align: right;
    border-top: 1px solid #e5e5e5;
}

.close {
    background: none;
    border: none;
    font-size: 21px;
    font-weight: bold;
    line-height: 1;
    color: #000;
    opacity: 0.5;
    cursor: pointer;
}

.close:hover {
    opacity: 0.75;
}

/* Form styles */
.form-group {
    margin-bottom: 15px;
}

.form-control {
    display: block;
    width: 100%;
    padding: 6px 12px;
    font-size: 14px;
    line-height: 1.42857143;
    color: #555;
    background-color: #fff;
    background-image: none;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    transition:
        border-color ease-in-out 0.15s,
        box-shadow ease-in-out 0.15s;
}

.form-control:focus {
    border-color: #66afe9;
    outline: 0;
    box-shadow:
        inset 0 1px 1px rgba(0, 0, 0, 0.075),
        0 0 8px rgba(102, 175, 233, 0.6);
}

/* Button styles */
.btn {
    display: inline-block;
    padding: 6px 12px;
    margin-bottom: 0;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.42857143;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    cursor: pointer;
    border: 1px solid transparent;
    border-radius: 4px;
    text-decoration: none;
}

.btn-primary {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
}

.btn-primary:hover {
    color: #fff;
    background-color: #0056b3;
    border-color: #004085;
}

.btn-primary:disabled {
    opacity: 0.65;
    cursor: not-allowed;
}

.btn-secondary {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d;
}

.btn-secondary:hover {
    color: #fff;
    background-color: #545b62;
    border-color: #4e555b;
}

/* Text utilities */
.text-danger {
    color: #dc3545;
}

.text-muted {
    color: #6c757d;
}

.form-text {
    display: block;
    margin-top: 5px;
    font-size: 12px;
}

/* Loading state */
.btn:disabled {
    opacity: 0.65;
    cursor: not-allowed;
}

/* Responsive */
@media (max-width: 576px) {
    .modal-dialog {
        margin: 10px;
        max-width: none;
    }

    .editable-field:hover::after {
        display: none;
    }
}
