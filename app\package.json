{"name": "webapp", "version": "0.0.197", "description": "webapp", "main": "index.ts", "scripts": {"start": "ts-node src/index.ts", "lint": "eslint .", "prettier": "prettier", "format": "prettier --no-color --write '**/*.{js,jsx,ts,tsx,css,html,ejs}'", "livereload": "livereload -p 31729 -x node_modules -e ts,js,css,scss,html,ejs,jade,pug,json", "dev": "ts-node-dev --clear --respawn --pretty -- ./src/index.ts", "live": "npm version patch --no-git-tag-version --no-commit-hooks && npm install --no-audit --no-fund --silent && concurrently --prefix none \"npm run livereload\" \"npm run dev\""}, "nodemonConfig": {"ext": "ts", "ignore": ["dist/**", "node_modules/**"], "exec": "clear && ts-node src/index.ts", "stdin": false}, "repository": {"type": "git", "url": "git+https://gitlab.com/telecomunicacoes/webapp.git"}, "keywords": ["site"], "author": "wendel <<EMAIL>>", "license": "ISC", "bugs": {"url": "https://gitlab.com/telecomunicacoes/webapp/issues"}, "homepage": "https://gitlab.com/telecomunicacoes/webapp#README", "dependencies": {"@aws-sdk/client-ses": "^3.840.0", "@aws-sdk/client-sesv2": "^3.840.0", "@google-cloud/recaptcha-enterprise": "^6.2.0", "@simplewebauthn/server": "^13.1.1", "@swc/core": "^1.12.3", "@types/bcrypt": "^5.0.2", "@types/bytes": "^3.1.5", "@types/compression": "^1.8.1", "@types/connect-ensure-login": "^0.1.9", "@types/cookie-parser": "^1.4.9", "@types/ejs": "^3.1.5", "@types/express": "^5.0.0", "@types/express-fileupload": "^1.5.1", "@types/express-session": "^1.18.2", "@types/fluent-ffmpeg": "^2.1.27", "@types/fs-extra": "^11.0.4", "@types/humanize-duration": "^3.27.4", "@types/jimp": "^0.2.28", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^22.10.2", "@types/numeral": "^2.0.5", "@types/passport": "^1.0.17", "@types/qrcode": "^1.5.5", "@types/randomatic": "^3.1.5", "@types/speakeasy": "^2.0.10", "@types/speakingurl": "^13.0.6", "@types/strftime": "^0.9.8", "@types/validator": "^13.12.2", "aws-sdk": "^2.1692.0", "axios": "^1.10.0", "bcrypt": "^6.0.0", "body-parser": "^1.20.3", "bytes": "^3.1.2", "check-disk-space": "^3.4.0", "chokidar": "^4.0.1", "colors": "^1.4.0", "compression": "^1.7.5", "concurrently": "latest", "connect-ensure-login": "^0.1.1", "connect-redis": "^7.1.1", "cookie-parser": "^1.4.7", "cookie-session": "^2.1.0", "cpf-cnpj-validator": "^1.0.3", "discord.js": "^14.16.3", "dotenv": "^16.4.5", "ejs": "^3.1.10", "ejs-lint": "^2.0.1", "email-deep-validator": "^3.3.0", "enhanced-email-deep-validator": "^1.1.0", "express": "^4.21.1", "express-fileupload": "^1.5.1", "express-mysql-session": "^3.0.3", "express-rate-limit": "^7.5.1", "express-session": "^1.18.1", "fluent-ffmpeg": "^2.1.3", "fs": "0.0.1-security", "generate-password": "^1.7.1", "helmet": "^8.0.0", "html-entities": "^2.5.2", "humanize-duration": "^3.32.1", "ip-country": "^1.0.2", "jimp": "^1.6.0", "json-colorizer": "^3.0.1", "json-stable-stringify": "^1.3.0", "jsonwebtoken": "^9.0.2", "livereload": "^0.9.3", "moment": "^2.30.1", "mysql2": "^3.14.1", "nodemon": "^3.1.7", "numeral": "^2.0.6", "passport": "^0.7.0", "passport-facebook": "^3.0.0", "passport-google-oauth": "^2.0.0", "passport-google-oauth20": "^2.0.0", "passport-local": "^1.0.0", "passport-oauth": "^1.0.0", "passport-twitter": "^0.1.5", "qrcode": "^1.5.4", "randomatic": "^3.1.1", "rate-limiter-flexible": "^7.1.1", "redis": "^4.7.0", "request": "^2.88.2", "session-file-store": "^1.5.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "speakeasy": "^2.0.0", "speakingurl": "^14.0.1", "strftime": "^0.10.3", "systeminformation": "^5.23.5", "tough-cookie": "^5.1.2", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "tsqlstring": "^1.0.1", "typescript": "^5.7.2", "uuid": "^11.0.3", "validator": "^13.12.0", "websocket": "^1.0.35", "yargs": "^17.7.2"}, "devDependencies": {"@types/passport-facebook": "^3.0.3", "@types/passport-google-oauth": "^1.0.45", "@types/passport-local": "^1.0.38", "@types/passport-twitter": "^1.0.40", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "jiti": "^2.4.2", "snyk": "^1.1294.0"}}