if (!req.user.ACL_DEV && !req.user.ACL_ADMIN) {
    return tr.adm_err(req, res, 'req.user.ACL_DEV|ACL_ADMIN', 'SOMENTE PARA DESENVOLVEDOR.')
}

const op = req.query.op
r.status = ''
if (op == 'send') {
    const from = req.body.from
    const to = req.body.to
    const subject = req.body.subject
    const text = req.body.text
    const html = main.nl2br(text)
    main.sendmail_template(req, from, to, subject, html, text, from, function (err) {
        if (err) {
            const msg = err.message
            const code = err.code
            r.status = code + ': ' + msg
            r.type = 'danger'
        } else {
            r.status = ''
            r.type = 'success'
        }
        return tr.display_adm(req, res, automake_name, r)
    })
} else {
    return tr.display_adm(req, res, automake_name, r)
}
