<!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML//EN">
<html><head>
<script src="../src/prettify.js"></script>
<script src="../src/lang-css.js"></script>
<style>
body { margin: 0; padding: 0 }
pre { margin: 0 }
</style>
</head>
<script>
// This page displays some code styled using a theme named in the
// query part of the URL.
var themeName = decodeURIComponent(document.location.search.replace(/^\?/, ''));

// Call out to the parent so that it can resize the iframe once this
// document's body is loaded.
function adjustHeightInParent() {
  if (parent !== window) {
    try {
      var div = document.body.getElementsByTagName('div')[0];
      parent.adjustChildIframeSize(
          themeName, div.offsetWidth, div.offsetHeight);
    } catch (ex) {
      // Can happen when this page is opened in its own tab.
    }
  }
}

// Load the necessary CSS
(function () {
  document.title = 'Theme ' + themeName;
  // Load the stylesheet that we're demoing.
  var link = document.createElement('link');
  link.rel = 'stylesheet';
  link.type = 'text/css';
  link.href = themeName === 'default'
      ? '../src/prettify.css' : themeName + '.css';
  document.getElementsByTagName('head')[0].appendChild(link);
})();
</script>

<body onload="prettyPrint(); adjustHeightInParent()">
<div style="width: 40em; display: inline-block">
<pre class="prettyprint lang-html linenums">
&lt;script type="text/javascript"&gt;
// Say hello world until the user starts questioning
// the meaningfulness of their existence.
function helloWorld(world) {
  for (var i = 42; --i &gt;= 0;) {
    alert('Hello ' + String(world));
  }
}
&lt;/script&gt;
&lt;style&gt;
p { color: pink }
b { color: blue }
u { color: "umber" }
&lt;/style&gt;
</pre>
</div>
</body></html>
