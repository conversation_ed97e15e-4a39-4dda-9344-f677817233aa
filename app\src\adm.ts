import express from 'express'
import fs from 'fs'
import moment from 'moment'
import path from 'path'
import Auth from './auth'
import {red, yellow} from './colors'
import {REQ} from './interfaces'
import Main from './main'
export default class ADM {
    main: Main
    auth: Auth
    constructor(arg_main: Main, arg_auth: Auth) {
        this.main = arg_main
        this.auth = arg_auth
        this.main.ALL('/app/adm_menu_form', this.adm_menu_form.bind(this))
        this.main.ALL('/app/adm_menu', this.adm_menu.bind(this))
        this.main.GET('/adm_site_config', this.adm_site_config.bind(this), true)
        this.main.GET('/adm_site_config_save', this.adm_site_config_save.bind(this), true)
        this.main.GET('/adm_site_config_rm', this.adm_site_config_rm.bind(this), true)
        this.main.GET('/adm_site_views', this.adm_site_views.bind(this), true)
        this.main.GET('/adm_site_view_form/:id', this.adm_site_view_form.bind(this), true)
        this.main.GET('/adm_site_view_form_breadcumb', this.adm_site_view_form_breadcumb.bind(this), true)
        this.main.GET('/adm_site_view_rm/:id', this.adm_site_view_rm.bind(this), true)
        this.main.GET('/site_views_backup/:id', this.site_views_backup.bind(this), true)
        this.main.GET('/site_views_backup_by_id/:id', this.site_views_backup_by_id.bind(this), true)
        this.main.POST('/adm_site_view_save', this.adm_site_view_save.bind(this), true)
        this.main.POST('/adm_site_view_save_simple', this.adm_site_view_save_simple.bind(this), true)
        this.main.GET('/adm_get_item_history/:id', this.adm_get_item_history.bind(this), true)
        this.main.POST('/adm_save_item_history/:id', this.adm_save_item_history.bind(this), true)
        this.main.GET('/adm/user/rm/:id', this.adm_user_rm.bind(this), true)
        this.main.GET('/adm/user/impersonate/:id', this.adm_user_impersonate.bind(this), true)
        this.main.GET('/adm/user/impersonation_exit', this.adm_user_impersonation_exit.bind(this), true)
        this.main.GET('/adm/site/refresh', this.adm_site_refresh.bind(this), true)
        this.main.GET('/adm_list_all_applications', this.adm_list_all_applications.bind(this), true)
    }
    async adm_menu_form(req: REQ, res: express.Response) {
        yellow('adm_menu_form', req.body)
        let r: any = {}
        const oid = req.oid
        const gid = req.gid
        const uid = req.uid
        const ip = req.IP
        r.menu_id = req.query.menu_id || 0
        r.cat = req.query.cat || 0
        if (req.body.menu_name) {
            const menu_name = req.body.menu_name
            const menu_cat_id = req.body.menu_cat_id
            let menu_acl = req.body.menu_acl || []
            menu_acl = menu_acl.join(',')
            let args = [oid, gid, uid, ip, req.body.id]
            args.push(menu_name)
            args.push(menu_cat_id)
            args.push(menu_acl)
            args.push(req.body.menu_ico)
            args.push(req.body.menu_order)
            args.push(req.body.menu_action_url)
            args.push(req.body.menu_automake_app)
            args.push(req.body.menu_automake_page)
            args.push(req.body.menu_status)
            args.push(req.body.menu_desc)
            const result = await this.main.db.call('api_adm_menu_save', args)
            if (result.is_err()) return res.json({error: result.error()})
            return this.main.redirect(req, res, '/app/adm_menu?name=' + menu_name + '&cat=' + menu_cat_id)
        } else {
            const result = await this.main.db.call('api_adm_menu_by_id', [oid, gid, uid, ip, r.menu_id])
            if (result.is_err()) return res.json({error: result.error()})
            const menu = result.gr()
            r.COPIANDO = req.query.COPIANDO ? 1 : 0
            r.cb = req.query.cb
            r.r = menu
            yellow(r)
            return this.main.display(req, res, 'adm', 'adm_menu_form', r)
        }
    }
    async adm_menu(req: REQ, res: express.Response) {
        let r: any = {}
        const oid = req.oid
        const gid = req.gid
        const uid = req.uid
        const ip = req.IP
        const result = await this.main.db.call('api_adm_menu', [oid, gid, uid, ip, r.cat])
        if (result.is_err()) return res.json({error: result.error()})
        r.tree = result.ga()
        return this.main.display(req, res, 'adm', 'adm_menu', r)
    }
    async adm_site_config(req: REQ, res: express.Response) {
        if (!req.user?.ACL_DEV && !req.user?.ACL_ADMIN) return res.json({error: 'NO ACL_DEV or ACL_ADMIN'})
        const args = [req.site?.site_id]
        const result = await this.main.db.call('site_config_by_id', args)
        if (result.is_err()) return res.json({error: result.error()})
        let r: any = {}
        r.data_values = result.ga()
        return this.main.display(req, res, 'adm', 'adm_site_config', r)
    }
    async adm_site_config_save(req: REQ, res: express.Response) {
        if (!req.user?.ACL_DEV && !req.user?.ACL_ADMIN) return res.json({error: 'NO ACL_DEV or ACL_ADMIN'})
        const args = [req.site?.site_id, req.query.id, req.query.k, req.query.v]
        const k: string = req.query.k as string
        if (!k) return res.json({error: 'NO K parameter'})
        if (req.site) req.site[k] = req.query.v
        const result = await this.main.db.call('adm_site_config_save', args)
        if (result.is_err()) return res.json({error: result.error()})
        const r = result.gr()
        return res.send(r.str)
    }
    async adm_site_config_rm(req: REQ, res: express.Response) {
        if (!req.user?.ACL_DEV && !req.user?.ACL_ADMIN) return res.json({error: 'NO ACL_DEV or ACL_ADMIN'})
        const args = [req.site?.site_id, req.query.id]
        const k: string = req.query.k as string
        if (!k) return res.json({error: 'NO K parameter'})
        if (req.site) req.site[k] = req.query.v
        const result = await this.main.db.call('adm_site_config_rm', args)
        if (result.is_err()) return res.json({error: result.error()})
        return res.json({})
    }
    async adm_site_views(req: REQ, res: express.Response) {
        if (!req.user?.ACL_DEV) return res.json({error: 'SOMENTE PARA DESENVOLVEDOR'})
        let r: any = {}
        const result = await this.main.db.call('api_dev_views', [req.site?.site_id, req.user?.gid, req.user?.uid, req.IP])
        if (result.is_err()) return res.json({error: result.error()})
        r.tree = result.ga()
        return this.main.display(req, res, 'adm', 'adm_site_views', r)
    }
    async adm_site_view_form(req: REQ, res: express.Response) {
        let self = this
        let session = req.session || {}
        if (!req.session) red('error !req.session')
        if (!this.auth.is_dev_user(req, res)) return
        const id = req.params.id
        const cp = req.query.cp
        const result = await this.main.db.call('adm_site_view_form', [id, req.user?.uid])
        if (result.is_err()) return res.json({error: result.error()})
        const r = result.gr()
        const rr = {id: r.view_id, label: r.view_name}
        let breadcumb = session?.adm_site_view_form || []
        let found = false
        for (let b in breadcumb)
            if (breadcumb[b].id == r.view_id) {
                found = true
                break
            }
        if (!found) breadcumb.unshift(rr)
        if (breadcumb.length > 10) breadcumb = breadcumb.slice(10)
        session.adm_site_view_form = breadcumb
        let args: any = {data_values: r, pk: r.view_id}
        if (cp) args.view_name = cp
        if (req.xhr) return res.send(r)
        args.cp = cp
        args.no_live = true
        args.view_id = id
        args.view_name = r.view_name
        args.breadcumb = session.adm_site_view_form
        args.TITLE = '!' + r.view_name
        return self.main.display(req, res, 'adm', 'adm_site_view_form', args)
    }
    async site_views_backup(req: REQ, res: express.Response) {
        if (!req.user?.ACL_DEV) return res.json({error: 'SOMENTE PARA DESENVOLVEDOR'})
        const result = await this.main.db.call('site_views_backup', [req.params.id, req.user.uid])
        if (result.is_err()) return res.json({error: result.error()})
        return res.json(result.ga())
    }
    async adm_site_view_rm(req: REQ, res: express.Response) {
        if (!req.user?.ACL_DEV) return res.json({error: 'SOMENTE PARA DESENVOLVEDOR'})
        const result = await this.main.db.call('site_view_rm', [req.params.id, req.user?.uid])
        if (result.is_err()) return res.json({error: result.error()})
        return res.json(result.ga())
    }
    async site_views_backup_by_id(req: REQ, res: express.Response) {
        if (!req.user?.ACL_DEV) return res.json({error: 'SOMENTE PARA DESENVOLVEDOR'})
        const result = await this.main.db.call('site_views_backup_by_id', [req.params.id, req.user?.uid])
        if (result.is_err()) return res.json({error: result.error()})
        return res.json(result.gr())
    }
    async new_site_setup_form(req: REQ, res: express.Response) {
        let self = this
        if (!req.headers) {
            red('error !req.headers')
            return this.main.display(req, res, 'public', 'site_not_found', {})
        }
        if (!req.headers.host) {
            red('error !req.headers.host')
            return this.main.display(req, res, 'public', 'site_not_found', {})
        }
        const host: string = req.headers.host as string
        if (req.query.setup) {
            let r: any = {}
            r.host = host
            r.password = self.main.gen_password()
            r.site_url = 'http://' + host
            req.site = {}
            const themes_result = await this.main.db.call('site_themes', [])
            if (themes_result.is_err()) return res.json({error: themes_result.error()})
            r.site_themes = themes_result.ga()
            const lang_result = await this.main.db.call('tr_lang', [])
            if (lang_result.is_err()) return res.json({error: lang_result.error()})
            r.tr_lang = lang_result.ga()
            const connectors_result = await this.main.db.call('automake_connectors', [0])
            if (connectors_result.is_err()) return res.json({error: connectors_result.error()})
            r.connectors = connectors_result.ga()
            self.main.display(req, res, 'public', 'setup_site_copy_from', r)
        } else {
            let session = req.session || {}
            if (!req.session) red('error !req.session')
            session.nosite = session.nosite || {}
            if (!session.nosite[host]) session.nosite[host] = 1
            let r: any = {}
            r.host = host
            return self.main.display(req, res, 'public', 'site_not_found', r)
        }
    }
    async new_site_setup_save(req: REQ, res: express.Response) {
        let self = this
        yellow(req.body)
        const password = req.body.password
        const host = req.headers.host
        const email = req.body.email.toLowerCase()
        let args: any = []
        args.push(host)
        args.push(req.lang)
        args.push(req.IP)
        args.push(req.agent)
        args.push(req.body.title)
        args.push(req.body.description)
        args.push(req.body.id)
        args.push(req.body.src)
        args.push(req.body.site_themes)
        args.push(req.body.tr_lang)
        args.push(req.body.username)
        args.push(email)
        args.push(password)
        args.push(this.main.uuid())
        args.push(this.main.encrypt(this.main.uuid()))
        args.push(req.body.tel)
        args.push(req.country)
        args.push(req.body.site_org)
        args.push(req.body.site_db)
        args.push(req.body.site_connector)
        const result = await this.main.db.call('setup_site_copy_from', args)
        if (result.is_err()) return res.json({error: result.error()})
        const r = result.gr()
        if (r.err) {
            return res.json({error: r.err})
        } else {
            const ts = this.main.timestamp()
            const magic = self.auth.hash_password(password + ts + 'daf8k32sadfhdsufhasiujfa')
            const uri = '/magic?origin=magic&ts=' + ts + '&h=' + magic + '&u=' + email
            this.main.site_setup(true)
            return res.redirect(uri)
        }
    }
    new_site_setup_load(req: REQ, res: express.Response, next: any) {
        res
        const id = req.query.setup_site_copy_load
        yellow(req.query)
        yellow('setup_site_copy_load', id)
        this.main.site_setup(true)
        next()
    }
    async adm_get_item_history(req: REQ, res: express.Response) {
        if (!req.user?.ACL_DEV) return res.json({error: 'SOMENTE PARA DESENVOLVEDOR'})
        const result = await this.main.db.call('adm_get_item_history', [req.user?.uid, req.params.id])
        if (result.is_err()) return res.json({error: result.error()})
        const r = result.gr()
        if (r.str) return res.send(r.str)
        res.send('[]')
    }
    async adm_save_item_history(req: REQ, res: express.Response) {
        if (!req.user?.ACL_DEV) return res.json({error: 'SOMENTE PARA DESENVOLVEDOR'})
        const result = await this.main.db.call('adm_save_item_history', [req.user?.uid, req.params.id, req.body.str])
        if (result.is_err()) return res.json({error: result.error()})
        return res.json(result.gr())
    }
    async adm_user_impersonate(req: REQ, res: express.Response) {
        const is_dev = req.user && req.user.acl.indexOf('ACL_DEV') !== -1 ? true : false
        const is_adm = req.user && req.user.acl.indexOf('ACL_ADMIN') !== -1 ? true : false
        if (!req.user) return res.json({error: 'NO USER'})
        if (!is_dev && !is_adm) return res.json({error: 'NO ACL_DEV or ACL_ADMIN'})
        const adm = is_dev || is_adm
        let session = req.session || {}
        if (!req.session) red('error !req.session')
        const result = await this.main.db.call('adm_user_impersonate', [adm, req.user?.uid, req.params.id, req.IP])
        if (result.is_err()) return res.json({error: result.error()})
        const tree = result.ga()
        let uri = '/dashboard?err=INVALID-USER'
        if (tree) {
            let r = tree[0]
            if (r.acl) r.acl = r.acl.split(',')
            yellow(r)
            session.impersonate = r
            uri = '/dashboard?u=' + req.user?.uid
        }
        return res.redirect(uri)
    }
    async adm_user_rm(req: REQ, res: express.Response) {
        const is_dev = req.user && req.user.acl.indexOf('ACL_DEV') !== -1 ? true : false
        const is_adm = req.user && req.user.acl.indexOf('ACL_ADMIN') !== -1 ? true : false
        if (!is_dev && !is_adm) return res.json({error: 'NO ACL_DEV or ACL_ADMIN'})
        const adm = is_dev || is_adm
        let session = req.session || {}
        if (!req.session) red('error !req.session')
        const result = await this.main.db.call('adm_user_rm', [adm, req.user?.uid, req.params.id, req.IP])
        if (result.is_err()) return res.json({error: result.error()})
        const tree = result.ga()
        let uri = '/dashboard?err=INVALID-USER'
        if (tree) {
            let r = tree[0]
            if (r.acl) r.acl = r.acl.split(',')
            yellow(r)
            session.impersonate = r
            uri = '/dashboard?u=' + req.user?.uid
        }
        return res.redirect(uri)
    }
    adm_user_impersonation_exit(req: REQ, res: express.Response) {
        let session = req.session || {}
        if (!req.session) red('error !req.session')
        session.impersonate = undefined
        const uri = '/dashboard'
        return res.redirect(uri)
    }
    async adm_list_all_applications(req: REQ, res: express.Response) {
        let self = this
        if (!req.user?.ACL_DEV) return res.json({error: 'NO ACL_DEV'})
        const args = [req.user?.uid, req.IP]
        const result = await this.main.db.call('automake_applications', args)
        if (result.is_err()) return res.json({error: result.error()})
        let r: any = {}
        r.tree = result.ga()
        return self.main.display(req, res, 'adm', 'adm_list_all_applications', r)
    }
    new_site_setup(req: REQ, res: express.Response, next: any) {
        if (req.query.setup_site_copy_load) {
            red('LOAD - Loading site setup data for host:', req.headers.host)
            this.new_site_setup_load(req, res, next)
        } else if (req.body.setup_site_copy_from > 0) {
            red('SAVE - Saving new site setup from template ID:', req.body.setup_site_copy_from, 'for host:', req.headers.host)
            this.new_site_setup_save(req, res)
        } else {
            red('FORM - Displaying site setup form for host:', req.headers.host)
            this.new_site_setup_form(req, res)
        }
    }
    adm_site_refresh(req: REQ, res: express.Response) {
        let self = this
        if (req.user?.ACL_DEV || req.user?.ACL_ADMIN) {
            yellow('Recarregando configurações do site.')
            self.main.site_setup(true)
            return res.json({msg: 'ok'})
        }
        return res.json({error: 'Somente ACL_DEV e ACL_ADMIN podem recarregar configurações'})
    }
    adm_site_view_form_breadcumb(req: REQ, res: express.Response) {
        let breadcumb = req.session?.adm_site_view_form || []
        return res.json(breadcumb)
    }
    async adm_site_view_save(req: REQ, res: express.Response) {
        let self = this
        if (!req.user?.ACL_DEV) return res.json({error: 'SOMENTE PARA DESENVOLVEDOR'})
        let args: any = []
        args.push(req.site?.site_id)
        args.push(req.user?.uid)
        args.push(req.IP)
        args.push(req.body.pk)
        args.push(req.body.name)
        args.push(req.body.mode)
        args.push(req.body.visibility)
        args.push(req.body.descr)
        args.push(req.body.ed_exe)
        args.push(req.body.view_css ? req.body.view_css : req.body.css)
        args.push(req.body.view_js ? req.body.view_js : req.body.js)
        const result = await this.main.db.call('adm_site_view_save', args)
        if (result.is_err()) return res.json({error: result.error()})
        const tree = result.gr()
        self.save_template(tree.id, tree.view_data, tree.view_exec, tree.view_js, tree.view_css)
        if (req.xhr) return res.json({txt: tree.txt, view_id: tree.view_id})
        return self.main.redirect(req, res, `/adm_site_view_form/${tree.view_id}`)
    }
    save_template(id: string, view_data: string, view_exec: string, view_js: string, view_css: string) {
        let file_ejs = path.normalize(path.join(__dirname, '..', 'views', id)) + '.ejs'
        let file_ts = path.normalize(path.join(__dirname, '..', 'views', id)) + '.ts'
        let file_css = path.normalize(path.join(__dirname, '..', 'views', id)) + '.css'
        let backup_dir = path.normalize(path.join(__dirname, '..', 'data', 'views', id))
        if (!fs.existsSync(backup_dir)) fs.mkdirSync(backup_dir, {recursive: true})
        let date_str = moment().format('YYYY-MM-DD_HH-mm-ss')
        if (view_data) {
            if (fs.existsSync(file_ejs)) {
                let backup_file = path.join(backup_dir, `${date_str}.ejs`)
                fs.copyFileSync(file_ejs, backup_file)
            }
            fs.writeFileSync(file_ejs, view_data)
        }
        if (view_js) {
            if (fs.existsSync(file_ts)) {
                let backup_file = path.join(backup_dir, `${date_str}.ts`)
                fs.copyFileSync(file_ts, backup_file)
            }
            fs.writeFileSync(file_ts, view_exec)
        }
        if (view_css) {
            if (fs.existsSync(file_css)) {
                let backup_file = path.join(backup_dir, `${date_str}.css`)
                fs.copyFileSync(file_css, backup_file)
            }
            fs.writeFileSync(file_css, view_css)
        }
    }
    async adm_site_view_save_simple(req: REQ, res: express.Response) {
        let self = this
        if (!this.auth.is_dev_user(req, res)) return
        let args: any = []
        args.push(req.site?.site_id)
        args.push(req.user?.uid)
        args.push(req.IP)
        args.push(req.body.pk)
        args.push(req.body.descr)
        const result = await this.main.db.call('adm_site_view_save_simple', args)
        if (result.is_err()) return res.json({error: result.error()})
        const tree = result.gr()
        const id = tree.id
        self.save_template(id, tree.view_data, tree.view_exec, tree.view_js, tree.view_css)
        if (req.xhr) return res.json({txt: tree.txt, view_id: tree.view_id})
        const uri = '/adm_site_view_form/' + tree.view_id
        return self.main.redirect(req, res, uri)
    }
}
