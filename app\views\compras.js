const fornecedor = req.query.fornecedor || 0
const cat = req.query.cat || 0
const setor = req.query.setor || 0
const situacao = req.query.situacao || 1
let ds = req.query.ds
let de = req.query.de
if (!ds) ds = moment(new Date()).subtract(2, 'month').format('YYYY-MM-DD')
if (!de) de = moment(new Date()).format('YYYY-MM-DD')
ds = moment(ds).format('YYYY-MM-DD')
de = moment(de).format('YYYY-MM-DD')

r.fornecedor = fornecedor
r.cat = cat
r.setor = setor
r.situacao = situacao
r.ds = ds
r.de = de
r.todos = req.query.todos || 0
const args = [req.oid, req.gid, req.uid, req.IP, r.todos, ds, de, situacao, setor, cat, fornecedor]
//return res.json(args);
ga('financeiro.api_account_financeiro_compras', args, function (tree) {
    r.tree = tree
    ga('financeiro.api_account_situacoes', [req.oid, req.gid, req.uid, req.IP], function (situacoes) {
        r.situacoes = situacoes
        return tr.display_adm(req, res, automake_name, r)
    })
})
