{"name": "bootstrap-wysiwyg", "version": "1.0.4", "description": "A tiny Bootstrap and jQuery based WYSIWYG rich text editor based on the browser function execCommand.", "contributors": [{"name": "<PERSON>", "url": "https://github.com/steveathon"}, {"name": "<PERSON>", "url": "https://github.com/RandomlyKnighted"}], "dependencies": {}, "devDependencies": {"check-pages": "^0.9.0", "grunt": "^0.4.5", "grunt-bootlint": "^0.9.1", "grunt-bumpup": "^0.6.2", "grunt-check-pages": "^0.9.0", "grunt-contrib-jshint": "^0.11.2", "grunt-contrib-rename": "^0.0.3", "grunt-contrib-uglify": "^0.9.1", "grunt-contrib-watch": "^0.6.1", "grunt-release": "^0.13.0", "gulp": "^3.9.0", "gulp-bootlint": "^0.5.0", "gulp-html5-lint": "^1.0.1", "gulp-jshint": "^1.11.0", "gulp-rename": "^1.2.2", "gulp-uglify": "^1.2.0"}, "repository": {"type": "git", "url": "https://github.com/steveathon/bootstrap-wysiwyg"}, "keywords": ["css", "js", "responsive", "front-end", "web", "wysiwyg", "editor"], "license": "MIT", "private": false, "bugs": {"url": "https://github.com/steveathon/bootstrap-wysiwyg/issues"}, "homepage": "https://github.com/steveathon/bootstrap-wysiwyg"}