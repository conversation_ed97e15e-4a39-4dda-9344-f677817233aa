const id = req.query.id ? req.query.id : 0
if (req.body.id) {
    if (req.body.equipe_usuarios.length > 7) {
        return res.send('Equipe não pode ter mais que 7 usuários.')
    }

    let args = [
        sid,
        gid,
        uid,
        ip,
        req.body.id,
        req.body.equipe_telefone,
        req.body.equipe_veiculo1,
        req.body.equipe_veiculo1_km,
        req.body.equipe_veiculo2,
        req.body.equipe_veiculo2_km,
        req.body.equipe_base,
        req.body.equipe_cilindro_grade_01,
        req.body.equipe_cilindro_portatil,
        req.body.equipe_cilindro_grade_02,
        req.body.equipe_cilindro_reserva,
        req.body.equipe_respirador,
        req.body.equipe_circuitos,
        req.body.equipe_incubadora,
        req.body.equipe_cfr,
        req.body.equipe_ativa ? 1 : 0,
        req.body.equipe_usuarios.join(',')
    ]
    for (let i = 0; i < 7; i++) {
        const u = req.body.equipe_usuarios[i] ? req.body.equipe_usuarios[i] : 0
        args.push(u)
    }
    // return res.json(args);

    gr(
        'api_account_formacao_salvar',
        args,
        function (row) {
            return tr.redirect(req, res, '/app/enfermagem')
        },
        true
    )
} else {
    ga('api_account_formacao_usuarios_todos', [sid, gid, uid, ip], function (usuarios) {
        r.usuarios = usuarios
        gr('api_account_despacho_equipe', [sid, gid, uid, ip, id], function (equipe) {
            gr('api_account_formacao_ultima', [sid, gid, uid, ip, id], function (formacao) {
                r.formacao = formacao
                r.equipe = equipe
                r.selecionados = equipe.equipe_usuarios ? equipe.equipe_usuarios.split(',') : []
                return tr.display_adm(req, res, automake_name, r)
            })
        })
    })
}
