const id = req.query.id
const force_open = 1
if (req.body.chamado_id) {
    let args = [sid, gid, uid, ip]
    args.push(req.body.chamado_id)
    args.push(req.body.tipo)
    args.push(req.body.demanda_reprimida)
    args.push(req.body.follow_up_pendente)
    args.push(req.body.condutas.join(','))
    args.push(req.body.observacao)
    args.push(req.body.pendencias)
    gr('api_account_nap_salvar', args, function () {
        return tr.redirect(req, res, '/app/nap')
    })
} else {
    gr('api_account_chamado_abre_ultima_regulacao', [sid, gid, uid, ip, id, force_open], function (chamado) {
        ga('api_account_regulacoes_por_chamado', [sid, gid, uid, ip, id], function (regulacoes) {
            r.chamado = chamado
            r.regulacoes = regulacoes
            r.ultima_regulacao = regulacoes[regulacoes.length - 1]
            ga('api_account_chamado_observacoes', [sid, gid, uid, ip, id], function (observacoes) {
                r.observacoes = observacoes
                ga('api_account_nap_condutas', [sid, gid, uid, ip], function (condutas) {
                    r.condutas = condutas
                    ga('api_account_nap_atendimentos', [sid, gid, uid, ip, id], function (atendimentos) {
                        r.atendimentos = atendimentos
                        return tr.display_adm(req, res, automake_name, r)
                    })
                })
            })
        })
    })
}
