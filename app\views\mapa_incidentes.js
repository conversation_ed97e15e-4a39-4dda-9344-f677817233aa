console.log('user', req.user)
if (req.query.op == 'poi') {
    return res.json(`{
      "DENGUE1-109": {
        "TXT": "DENGUE1-109",
        "LAT": -15.8112744,
        "LON": -47.8886276,
        "TYPE": "car",
        "KMH": 12,
        "ICON": "car",
        "BGCL": "008000",
        "FGCL": "FFFFFF",
        "SEC": "4"
      },
      "DENGUE2-103": {
        "TXT": "DENGUE2-103",
        "LAT": -15.8074091,
        "LON": -47.8926028,
        "TYPE": "car",
        "KMH": 0,
        "ICON": "car",
        "BGCL": "008000",
        "FGCL": "FFFFFF",
        "SEC": "4"
      },
      "DENGUE1-98": {
        "TXT": "DENGUE1-98",
        "LAT": -15.8296613,
        "LON": -47.9070792,
        "TYPE": "car",
        "KMH": 12,
        "ICON": "car",
        "BGCL": "008000",
        "FGCL": "FFFFFF",
        "SEC": "4"
      },
      "DENGUE1-18": {
        "TXT": "DENGUE1-18",
        "LAT": -15.8340819,
        "LON": -47.9803756,
        "TYPE": "car",
        "KMH": 12,
        "ICON": "car",
        "BGCL": "008000",
        "FGCL": "FFFFFF",
        "SEC": "4"
      },
      "DENGUE1-13": {
        "TXT": "DENGUE1-13",
        "LAT": -15.8394626,
        "LON": -47.974145,
        "TYPE": "car",
        "KMH": 12,
        "ICON": "car",
        "BGCL": "008000",
        "FGCL": "FFFFFF",
        "SEC": "4"
      },
      "DENGUE1-63": {
        "TXT": "DENGUE1-13",
        "LAT": -15.764584,
        "LON": -47.8805634,
        "TYPE": "car",
        "KMH": 12,
        "ICON": "car",
        "BGCL": "008000",
        "FGCL": "FFFFFF",
        "SEC": "4"
      },
      "DENGUE1-73": {
        "TXT": "DENGUE1-73",
        "LAT": -15.7545866,
        "LON": -47.8785734,
        "TYPE": "car",
        "KMH": 12,
        "ICON": "car",
        "BGCL": "008000",
        "FGCL": "FFFFFF",
        "SEC": "4"
      }
        
        
    }`)
} else if (req.query.op == 'incidents') {
    const tree = {
        'ASA NORTE': {
            ID: main.uuid(),
            LOC: [-15.7651912, -47.8831283],
            RAIO: {BG: '#CDEB8B', BG_LINE: '#006E2E', strokeOpacity: 0.8, strokeWeight: 3, fillOpacity: 0.1, TOTAL: 100 * 30},
            LOCALIDADES: {
                '70750-512': {
                    ID: main.uuid(),
                    LOC: [-15.767026, -47.8825623],
                    RAIO: {BG: '#FF0000', BG_LINE: '#FF0000', strokeOpacity: 0.8, strokeWeight: 1, fillOpacity: 0.2, TOTAL: 10 * 60},
                    ALERTAS: {
                        DENGUE: {
                            ID: main.uuid(),
                            LOC: [-15.767026, -47.8825623],
                            RAIO: {BG: '#4096EE', BG_LINE: '#FFFF88', strokeOpacity: 0.8, strokeWeight: 0, fillOpacity: 0.2, TOTAL: 10 * 50},
                            CASOS: [
                                {ID: main.uuid(), TXT: 'JOS&#201;', INFO: 'HNORTE', LOC: [-15.7627129, -47.8857687]},
                                {ID: main.uuid(), TXT: 'MARIA', INFO: 'HNORTE', LOC: [-15.767026, -47.8825623]},
                                {ID: main.uuid(), TXT: 'CARLOS', INFO: 'HNORTE', LOC: [-15.7693053, -47.8790279]},
                                {ID: main.uuid(), TXT: 'BATISTA', INFO: 'HNORTE', LOC: [-15.752189, -47.8850572]},
                                {ID: main.uuid(), TXT: 'SOUZA', INFO: 'HNORTE', LOC: [-15.7530379, -47.891275]},
                                {ID: main.uuid(), TXT: 'MÁRCIO', INFO: 'HNORTE', LOC: [-15.7623897, -47.8862574]}
                            ]
                        } // DENGUE
                    } // ALERTAS
                } // CEP
            } // LOCALIDADES
        }, // BAIRRO

        'ASA SUL': {
            ID: main.uuid(),
            LOC: [-15.8220538, -47.8989698],
            RAIO: {BG: '#CDEB8B', BG_LINE: '#006E2E', strokeOpacity: 0.8, strokeWeight: 3, fillOpacity: 0.1, TOTAL: 100 * 30},
            LOCALIDADES: {
                '70700-112': {
                    ID: main.uuid(),
                    LOC: [-15.826191, -47.9035672],
                    RAIO: {BG: '#FF0000', BG_LINE: '#FF0000', strokeOpacity: 0.8, strokeWeight: 1, fillOpacity: 0.2, TOTAL: 10 * 60},
                    ALERTAS: {
                        DENGUE: {
                            ID: main.uuid(),
                            LOC: [-15.826191, -47.9035672],
                            RAIO: {BG: '#4096EE', BG_LINE: '#FFFF88', strokeOpacity: 0.8, strokeWeight: 0, fillOpacity: 0.2, TOTAL: 10 * 50},
                            CASOS: [
                                {ID: main.uuid(), TXT: 'JOS&#201;', INFO: 'H SANTA MARTA', LOC: [-15.8292408, -47.9086319]},
                                {ID: main.uuid(), TXT: 'MARIA', INFO: 'H SANTA HELENA', LOC: [-15.826191, -47.9035672]},
                                {ID: main.uuid(), TXT: 'CARLOS', INFO: 'H SANTA MARTA', LOC: [-15.8211781, -47.9038587]}
                            ]
                        } // DENGUE
                    } // ALERTAS
                } // CEP
            } // LOCALIDADES
        }, // BAIRRO

        CRUZEIRO: {
            ID: main.uuid(),
            LOC: [-15.7952771, -47.928498],
            RAIO: {BG: '#CDEB8B', BG_LINE: '#006E2E', strokeOpacity: 0.8, strokeWeight: 3, fillOpacity: 0.1, TOTAL: 100 * 30},
            LOCALIDADES: {
                '70700-112': {
                    ID: main.uuid(),
                    LOC: [-15.7952063, -47.9323425],
                    RAIO: {BG: '#FF0000', BG_LINE: '#FF0000', strokeOpacity: 0.8, strokeWeight: 1, fillOpacity: 0.2, TOTAL: 10 * 60},
                    ALERTAS: {
                        DENGUE: {
                            ID: main.uuid(),
                            LOC: [-15.7952063, -47.9323425],
                            RAIO: {BG: '#4096EE', BG_LINE: '#FFFF88', strokeOpacity: 0.8, strokeWeight: 0, fillOpacity: 0.2, TOTAL: 10 * 50},
                            CASOS: [
                                {ID: main.uuid(), TXT: 'JOS&#201;', INFO: 'H SANTA MARTA', LOC: [-15.7926398, -47.931993]},
                                {ID: main.uuid(), TXT: 'MARIA', INFO: 'H SANTA HELENA', LOC: [-15.7961267, -47.9339612]},
                                {ID: main.uuid(), TXT: 'CARLOS', INFO: 'H SANTA MARTA', LOC: [-15.798109, -47.9289579]}
                            ]
                        }, // DENGUE
                        ESCORPIAO: {
                            ID: main.uuid(),
                            LOC: [-15.7964099, -47.9371618],
                            RAIO: {BG: '#4096EE', BG_LINE: '#4096EE', strokeOpacity: 0.1, strokeWeight: 1, fillOpacity: 0.3, TOTAL: 10 * 50},
                            CASOS: [
                                {ID: main.uuid(), TXT: 'JOS&#201;', INFO: 'H SANTA MARTA', COR: 'yellow', LOC: [-15.7929053, -47.9371434]},
                                {ID: main.uuid(), TXT: 'MARIA', INFO: 'H SANTA HELENA', COR: 'yellow', LOC: [-15.7963214, -47.933207]},
                                {ID: main.uuid(), TXT: 'CARLOS', INFO: 'H SANTA MARTA', COR: 'yellow', LOC: [-15.7999675, -47.9339612]}
                            ]
                        } // ESCORPIÃO
                    } // ALERTAS
                } // CEP
            } // LOCALIDADES
        }, // BAIRRO

        'N. BANDEIRATE': {
            ID: main.uuid(),
            LOC: [-15.8375513, -47.9853066],
            RAIO: {BG: '#CDEB8B', BG_LINE: '#006E2E', strokeOpacity: 0.8, strokeWeight: 3, fillOpacity: 0.1, TOTAL: 100 * 30},
            LOCALIDADES: {
                '70200-010': {
                    ID: main.uuid(),
                    LOC: [-15.8417968, -47.9695886],
                    RAIO: {BG: '#FF0000', BG_LINE: '#FF0000', strokeOpacity: 0.8, strokeWeight: 1, fillOpacity: 0.2, TOTAL: 10 * 60},
                    ALERTAS: {
                        DENGUE: {
                            ID: main.uuid(),
                            LOC: [-15.8417968, -47.9695886],
                            RAIO: {BG: '#4096EE', BG_LINE: '#FFFF88', strokeOpacity: 0.8, strokeWeight: 0, fillOpacity: 0.2, TOTAL: 10 * 50},
                            CASOS: [
                                {ID: main.uuid(), TXT: 'JOS&#201;', INFO: 'UPA NB', LOC: [-15.8411082, -47.9701407]},
                                {ID: main.uuid(), TXT: 'MARIA', INFO: 'UPA NB', LOC: [-15.8436657, -47.9696908]},
                                {ID: main.uuid(), TXT: 'CARLOS', INFO: 'P.S. NB', LOC: [-15.8417968, -47.9695886]}
                            ]
                        } // DENGUE
                    } // ALERTAS
                } // CEP
            } // LOCALIDADES
        }, // BAIRRO

        'LAGO NORTE': {
            ID: main.uuid(),
            LOC: [-15.7394281, -47.8578463],
            RAIO: {BG: '#CDEB8B', BG_LINE: '#006E2E', strokeOpacity: 0.8, strokeWeight: 3, fillOpacity: 0.1, TOTAL: 100 * 20},
            LOCALIDADES: {
                '70100-210': {
                    ID: main.uuid(),
                    LOC: [-15.7296783, -47.8654979],
                    RAIO: {BG: '#FF0000', BG_LINE: '#FF0000', strokeOpacity: 0.8, strokeWeight: 1, fillOpacity: 0.2, TOTAL: 10 * 60},
                    ALERTAS: {
                        DENGUE: {
                            ID: main.uuid(),
                            LOC: [-15.7296783, -47.8654979],
                            RAIO: {BG: '#4096EE', BG_LINE: '#FFFF88', strokeOpacity: 0.8, strokeWeight: 0, fillOpacity: 0.2, TOTAL: 10 * 50},
                            CASOS: [
                                {ID: main.uuid(), TXT: 'JOS&#201;', INFO: 'H. LAGO NORTE', LOC: [-15.7296783, -47.8654979]},
                                {ID: main.uuid(), TXT: 'MARIA', INFO: 'P.S. LN', LOC: [-15.7289769, -47.8673198]},
                                {ID: main.uuid(), TXT: 'CARLOS', INFO: 'P.S. LN', LOC: [-15.7355002, -47.8653158]}
                            ]
                        } // DENGUE
                    } // ALERTAS
                } // CEP
            } // LOCALIDADES
        } // BAIRRO
    } // tree
    return res.json(tree)
} else if (req.query.op == 'legends') {
    return res.json(`[
      {
        "MIN_SEC": "00:00:00",
        "MAX_SEC": "00:00:59",
        "BG_COLOR": "#008000",
        "FG_COLOR": "#FFFFFF",
        "LAST": "0"
      },
      {
        "MIN_SEC": "00:01:00",
        "MAX_SEC": "23:59:59",
        "BG_COLOR": "#ffce08",
        "FG_COLOR": "#ffffff",
        "LAST": "0"
      },
      {
        "MIN_SEC": "24:00:00",
        "MAX_SEC": "72:00:00",
        "BG_COLOR": "#bf0000",
        "FG_COLOR": "#ffffff",
        "LAST": "0"
      },
      {
        "MIN_SEC": "72:00:00",
        "MAX_SEC": "838:59:59",
        "BG_COLOR": "#000000",
        "FG_COLOR": "#ffffff",
        "LAST": "1"
      }
    ]`)
} else {
    r.TITULO_MAPA = 'Vigilante'
    r.ATUALIZAR_LEGENDAS = 0 // segundos pra atualizar legenda ou 0 pra nao atualizar
    r.url_poi = '/app' + automake_name + '?op=poi'
    r.url_legends = '/app' + automake_name + '?op=legends'
    r.url_incidents = '/app' + automake_name + '?op=incidents'
    r.CENTER_MAP_ON = {LAT: -15.8020764, LON: -47.9705259}
    r.MAP_ZOOM = 13
    return tr.display_adm(req, res, automake_name, r)
}
