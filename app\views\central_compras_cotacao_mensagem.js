if (req.query.op == 'salvar') {
    const pedido_id = req.body.pedido_id
    const para = req.body.para
    const cotacao_uuid = req.body.cotacao_uuid
    const assunto = req.body.assunto
    const mensagem = req.body.mensagem
    const fornecedor = req.body.fornecedor
    const GERENTE_NOME = req.site.GERENTE_NOME
    const GERENTE_EMAIL = req.site.GERENTE_EMAIL
    let args = [oid, gid, uid, req.IP, pedido_id, cotacao_uuid, para, assunto, mensagem, fornecedor, GERENTE_NOME, GERENTE_EMAIL]
    gr('financeiro.api_account_financeiro_cotacao_salvar', args, function (row) {
        //return res.json( {success: true} );
        io.sendmail(GERENTE_EMAIL, para, assunto, mensagem, '', GERENTE_EMAIL, function (err) {
            let tree = {success: true}
            if (err) {
                tree = {success: false, message: err.message}
            }
            return res.json(tree)
        })
    })
} else {
    gr('financeiro.api_account_financeiro_pedido_por_id', [oid, gid, uid, req.IP, req.query.id], function (pedido) {
        r.cotacao_uuid = req.query.uuid
        r.pedido = pedido
        gr('financeiro.api_account_fornecedor_por_id', [oid, gid, uid, req.IP, req.query.fornecedor], function (fornecedor) {
            r.fornecedor = fornecedor
            return tr.display_adm(req, res, automake_name, r)
        })
    })
}
