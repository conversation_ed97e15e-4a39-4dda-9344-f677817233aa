const uf = req.query.uf || 0
const status = req.query.status || 0
const args = [req.oid, req.gid, req.uid, req.IP, uf, status]
ga('financeiro.api_account_fornecedores', args, function (tree) {
    ga('financeiro.api_account_fornecedor_status', [req.oid, req.gid, req.uid, req.IP], function (situacoes) {
        r.situacoes = situacoes
        r.uf = uf
        r.status = status
        r.tree = tree
        return tr.display_adm(req, res, automake_name, r)
    })
})
