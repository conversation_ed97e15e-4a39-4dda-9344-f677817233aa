# Secure Authentication Service

A robust, enterprise-grade authentication service for Node.js applications with advanced security features.

## Features Implemented

### 🔐 Security Enhancements

- **JWT Token Authentication**: Secure token-based authentication with access and refresh tokens
- **Bcrypt Password Hashing**: Industry-standard password hashing with configurable rounds
- **Multi-Factor Authentication (MFA)**: TOTP-based 2FA with QR code generation
- **CSRF Protection**: Token-based CSRF protection for all state-changing operations
- **Rate Limiting**: Built-in rate limiting to prevent brute force attacks
- **SQL Injection Prevention**: Input validation and parameterized queries
- **Secure Encryption**: AES-256-GCM encryption (upgraded from CBC mode)
- **Session Management**: Automatic session cleanup and invalidation
- **Password Strength Validation**: Enforced strong password requirements
- **Exponential Backoff**: Smart reconnection strategy for socket connections

### 🚀 New Features

- **Email Verification**: User email verification system
- **Password Reset**: Secure password reset flow with time-limited tokens
- **Remember Me**: Persistent login sessions
- **Login History**: Track authentication events
- **Account Lockout**: Automatic lockout after failed login attempts
- **Session Invalidation**: Logout from all devices functionality

### 💻 UI Components

- Beautiful Bootstrap 5.3 login page with animations
- SweetAlert2 integration for elegant alerts
- Toastr notifications for real-time feedback
- Responsive design with mobile support
- Password visibility toggle
- Loading states and animations

## Installation

```bash
npm install
```

## Configuration

Create a `.env` file with the following variables:

```env
# JWT Configuration
JWT_SECRET=your-very-long-random-string
JWT_REFRESH_SECRET=another-very-long-random-string

# Encryption Key (32 bytes)
ENCRYPTION_KEY=your-32-byte-hex-encryption-key

# Database Configuration
RDS=your-database-connection-string
CONFIG_STORE=your-config-store-name

# AWS Configuration (for S3 and SES)
S3_USER=your-aws-access-key
S3_PASS=your-aws-secret-key
S3_REGION=us-east-1
SES_SMTP_USER=your-ses-smtp-user
SES_SMTP_PASS=your-ses-smtp-pass
SES_SMTP_FROM=<EMAIL>
```

## Usage

### Setting up Express App

```typescript
import express from 'express'
import authRoutes from './routes/auth.routes'
import {authenticate, rateLimit} from './middleware/auth.middleware'

const app = express()

// Middleware
app.use(express.json())
app.use(express.urlencoded({extended: true}))

// Auth routes
app.use('/auth', authRoutes)

// Protected route example
app.get('/api/user/profile', authenticate, (req, res) => {
    res.json({
        success: true,
        user: req.user
    })
})

// Rate limited endpoint
app.post('/api/data', authenticate, rateLimit('uid'), (req, res) => {
    // Your protected logic here
})
```

### Frontend Integration

```javascript
// Login
const response = await fetch('/auth/login', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        email: '<EMAIL>',
        password: 'SecurePassword123!',
        mfaToken: '123456' // Optional, if MFA is enabled
    })
})

const data = await response.json()
if (data.success) {
    // Store tokens
    localStorage.setItem('accessToken', data.accessToken)
    localStorage.setItem('refreshToken', data.refreshToken)
    localStorage.setItem('csrf', data.csrf)
}

// Making authenticated requests
const authResponse = await fetch('/api/protected', {
    headers: {
        Authorization: `Bearer ${localStorage.getItem('accessToken')}`,
        'X-CSRF-Token': localStorage.getItem('csrf')
    }
})
```

## API Endpoints

### Authentication

- `POST /auth/login` - User login
- `POST /auth/register` - User registration
- `POST /auth/refresh` - Refresh access token
- `POST /auth/logout` - Logout current session
- `POST /auth/logout-all` - Logout all sessions

### MFA

- `POST /auth/mfa/enable` - Generate MFA secret
- `POST /auth/mfa/confirm` - Confirm MFA setup

### Password Management

- `POST /auth/password/reset-request` - Request password reset
- `POST /auth/password/reset-confirm` - Confirm password reset

## Security Best Practices

1. **Environment Variables**: Never commit sensitive keys to version control
2. **HTTPS Only**: Always use HTTPS in production
3. **Secure Headers**: Use helmet.js for security headers
4. **Input Validation**: Always validate and sanitize user input
5. **Error Handling**: Never expose sensitive error details to users
6. **Token Storage**: Store tokens securely (httpOnly cookies for web apps)
7. **Regular Updates**: Keep all dependencies up to date

## Database Schema Required

The authentication system expects the following database procedures:

```sql
-- User management
auth.get_user_by_email(email)
auth.check_user_exists(email, username)
auth.create_user(email, username, password_hash, verification_token)
auth.update_login_success(uid)
auth.increment_login_attempts(uid)

-- MFA
auth.store_temp_mfa_secret(uid, secret)
auth.get_temp_mfa_secret(uid)
auth.enable_mfa(uid, secret)

-- Password reset
auth.create_password_reset_token(email, token, expiry)
auth.verify_password_reset_token(token)
auth.update_password(uid, password_hash)
auth.delete_password_reset_token(token)

-- Logging
auth.log_auth_event(uid, event_type, ip, user_agent)
```

## License

MIT
