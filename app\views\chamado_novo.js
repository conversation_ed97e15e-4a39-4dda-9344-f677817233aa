if (req.query.transferencia == 1) {
    main.emit('broadcast', {event_id: 'pbxip', data: {cmd: 'transfer', id: req.query.chamado_id, channel: req.query.channel, exten: req.query.ramal}})
    return res.json(req.query)
} else if (req.body.CHAMADO_TARM_INICIADO) {
    const id = req.body.id ? parseInt(req.body.id) : 0
    const args = [
        sid,
        gid,
        uid,
        ip,
        req.body.id,
        req.body.SOLICITANTE_NOME,
        req.body.SOLICITANTE_TELEFONE,
        req.body.PACIENTE_NOME,
        req.body.IDADE_A,
        req.body.IDADE_M,
        req.body.IDADE_D,
        req.body.SEXO,
        req.body.QUEIXA,
        req.body.ENDERECO,
        req.body.UF,
        req.body.CIDADE,
        req.body.BAIRRO,
        req.body.REFERENCIA,
        req.body.EVENTO_SOCIAL,
        req.body.MOTIVO,
        req.body.TRANSPORTE_DE ? req.body.TRANSPORTE_DE : 0,
        req.body.TRANSPORTE_PARA ? req.body.TRANSPORTE_PARA : 0,
        req.body.CHAMADO_TARM_INICIADO,
        req.body.CHAMADO_INCIDENTE
    ]
    gr(
        'api_account_chamados_tarm_salvar',
        args,
        function (row) {
            const CHANNEL = row.CHANNEL
            if (CHANNEL && req.body.CHAMADO_INCIDENTE == 45 && req.body.id) main.emit('broadcast', {event_id: 'pbxip', data: {cmd: 'park', id: req.body.id, CHANNEL: CHANNEL}})
            else if (CHANNEL && req.body.CHAMADO_INCIDENTE != 45 && req.body.id)
                main.emit('broadcast', {event_id: 'pbxip', data: {cmd: 'hangup', id: req.body.id, CHANNEL: CHANNEL}})

            return tr.redirect(req, res, '/tarm/chamados')
        },
        true
    )
} else {
    ga('api_account_motivos', [sid, gid, uid, ip], function (motivos) {
        const id = parseInt(req.query.id)
        gr('api_account_chamado_tarm_por_id', [sid, gid, uid, ip, id], function (row) {
            gr('api_account_estado_cidade_padrao', [sid, gid, uid, ip], function (padroes) {
                r.CHAMADO_TARM_INICIADO = Math.floor(Date.now() / 1000)
                r.chamado = row
                r.motivos = motivos
                r.padroes = padroes
                ga('api_account_ramais_transferencia', [sid, gid, uid, ip], function (ramais) {
                    r.ramais = ramais
                    return tr.display_adm(req, res, automake_name, r)
                })
            })
        })
    })
}
