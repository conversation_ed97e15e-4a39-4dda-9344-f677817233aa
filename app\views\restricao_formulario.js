const id = req.query.id ? req.query.id : 0
if (req.body.id) {
    let args = [sid, gid, uid, ip, req.body.id, req.body.restricao]
    gr(
        'api_account_restricao_salva',
        args,
        function (row) {
            return tr.redirect(req, res, '/app/restricoes_lista')
        },
        true
    )
} else {
    gr('api_account_restricoes_por_id', [sid, gid, uid, ip, id], function (restricao) {
        r.restricao = restricao
        return tr.display_adm(req, res, automake_name, r)
    })
}
