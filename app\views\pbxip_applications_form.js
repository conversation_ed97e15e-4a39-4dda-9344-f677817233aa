if (!req.user.ACL_DEV) {
    return tr.adm_err(req, res, 'req.user.ACL_DEV', 'SOMENTE PARA DESENVOLVEDOR.')
}
const id = req.query.id ? req.query.id : 0
const cp = req.query.cp ? req.query.cp : ''
if (req.query.rm) {
    gr(
        'api_account_pbxip_application_rm',
        [oid, gid, uid, ip, req.query.rm],
        function (r) {
            main.emit('broadcast', {event_id: 'pbxip', data: {reload: 1}})
            return tr.redirect(req, res, '/app/pbxip_applications')
        },
        true
    )
} else if (req.body.descr && req.body.name) {
    gr(
        'api_account_pbxip_application_save',
        [oid, gid, uid, ip, req.body.name, req.body.descr],
        function (r) {
            main.emit('broadcast', {event_id: 'pbxip', data: {reload: 1}})
            if (req.xhr) {
                return res.send(r)
            }
            return tr.redirect(req, res, '/app/pbxip_applications')
        },
        false
    )
} else {
    gr(
        'api_account_pbxip_application_by_id',
        [oid, gid, uid, ip, id],
        function (row) {
            if (cp) {
                r.view_name = cp
            }
            if (req.xhr) {
                return res.send(r)
            }
            r.cp = cp
            r.TITLE = '*' + r.view_name
            r.data_values = row
            r.pk = id
            return tr.adm(req, res, '/pbxip_applications_form', r)
        },
        true
    )
}
