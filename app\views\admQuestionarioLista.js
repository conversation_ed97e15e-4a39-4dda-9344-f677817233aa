const app_save_data = app
const app_rm_data = app
const app_edit_data = app
const app_title = 'Questionários'
const app_desc = '--'
const adm = req.user.ACL_DEV || req.user.ACL_ADMIN ? 1 : 0
r.op = req.query.op
r.app_save_data = app_save_data
const id = req.query.id ? req.query.id : '0'
ga(
    'quiz.adm_questionarios',
    [adm, uid, req.IP, req.site.site_id, id],
    function (tree) {
        r.click_src = req.query.src
        r.app_save_data = app_save_data
        r.app_rm_data = app_rm_data
        r.app_edit_data = app_edit_data
        r.app_title = app_title
        r.app_desc = app_desc
        r.tree = tree
        r.r = {}
        r.id = id
        if (req.query.op == 'edit') r.r = tree[0]
        return tr.display_adm(req, res, automake_name, r)
    },
    true
)
