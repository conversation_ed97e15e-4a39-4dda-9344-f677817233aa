const iniciaInterface = function () {
    gr('api_account_estado_cidade_padrao', [sid, gid, uid, ip], function (padroes) {
        r.padroes = padroes

        const novoAtendimento = req.query.novoAtendimento ? req.query.novoAtendimento : 0 // USADO CLICANDO NO BOTÃO ABRIR NOVA
        const triagemId = req.query.id ? req.query.id : 0 // USADO QUANDO CLICA NO EDITAR LISTA
        const novoAtendimentoId = req.body.id ? req.body.id : 0 // USADO QUANDO EDITA OU ADICIONA OS DADOS

        ga('telemedicina.api_verifica_usuario', [sid, gid, uid, ip, 1], function (usuario) {
            r.usuario = usuario

            if (novoAtendimento > 0)
                // CLICOU NO BOTÃO NOVA TRIAGEM NA LISTA
                iniciaAtendimento()
            else if (triagemId > 0)
                // CLICOU NO EDITAR TRIAGEM NA LISTA
                carregaDadosEditarAtendimento()
            else if (novoAtendimentoId > 0)
                // CLICOU NO BOTÃO DO SUBMIT MAS COM ID PARA SALVAR
                salvarAtendimento()
            else if (req.body.SOLICITANTE_NOME) {
                // ESTAMOS ADICIONANDO UMA NOVA TRIAGEM OU EDITANDO

                if (novoAtendimentoId > 0) salvarAtendimento()
                else adicionaNovoAtendimento()
            } // CONDIÇÃO DESCONHECIDA
            else return tr.display_adm(req, res, automake_name, r)
        })
    })
}

// EDIÇÃO DOS DADOS

const carregaDadosEditarAtendimento = function () {
    ga('telemedicina.api_verifica_triagem', [sid, gid, uid, ip, req.query.id], function (chamado) {
        r.chamado = chamado
        return tr.display_adm(req, res, automake_name, r)
    })
}

// ADICIONANDO

const adicionaNovoAtendimento = function () {
    if (!req.body.TEMPERATURA) req.body.TEMPERATURA = 'Não informado'

    if (!req.body.PRESSAO) req.body.PRESSAO = 'Não informado'

    if (!req.body.SATURACAO) req.body.SATURACAO = 'Não informado'

    const args = [sid, gid, uid, ip, 1, 1, req.body.TEMPERATURA, req.body.PRESSAO, req.body.SATURACAO]

    gr(
        'telemedicina.api_triagem_passo_1',
        args,
        function () {
            return tr.redirect(req, res, '/app/telemedicina_atendimento_2?novoAtendimento=1')
        },
        true
    )
}

// SALVANDO

const salvarAtendimento = function () {
    if (!req.body.TEMPERATURA) req.body.TEMPERATURA = 'Não informado'

    if (!req.body.PRESSAO) req.body.PRESSAO = 'Não informado'

    if (!req.body.SATURACAO) req.body.SATURACAO = 'Não informado'

    const args = [sid, gid, uid, ip, req.body.id, req.body.PRESSAO, req.body.TEMPERATURA, req.body.SATURACAO]

    gr(
        'telemedicina.api_triagem_sinais_vitais_paciente',
        args,
        function () {
            return tr.redirect(req, res, '/app/telemedicina_atendimento_2?id=' + req.body.id + '&editaDados=1')
        },
        true
    )
}

//  NO NOVO ATENDIMENTO, SE NÃO HOUVER DADOS APENAS CARREGA A PÁGINA
//  CASO CONTRÁRIO SALVA TRIAGEM E VAI PARA PR&#211;XIMA PÁGINA

const iniciaAtendimento = function () {
    if (req.body.SOLICITANTE_NOME) {
        adicionaNovoAtendimento()
    } else {
        r.chamado = []
        return tr.display_adm(req, res, automake_name, r)
    }
}

iniciaInterface()
