const equipe_id = req.query.equipe_id ? req.query.equipe_id : 0
const maca = req.query.maca ? req.query.maca : 0
const status = req.query.status ? req.query.status : 0
const obs = req.query.obs ? req.query.obs : ''
if (equipe_id > 0 && status > 0) {
    gr(
        'api_account_despacho_altera_status',
        [sid, gid, uid, ip, equipe_id, status, obs],
        function (row) {
            return res.send(`<script>document.location.href='/app${automake_name}?id=${equipe_id}'</script>`)
        },
        true
    )
} else if (maca) {
    const maca_retida = req.query.maca_retida
    const unidade = req.query.unidade
    const diagnostico = req.query.diagnostico
    gr('api_account_despacho_maca_retida', [sid, gid, uid, ip, equipe_id, maca_retida, unidade, diagnostico], function (row) {
        return res.json(row)
    })
} else {
    ga('api_account_regulacao_equipe_status', [sid, gid, uid, ip], function (equipe_status) {
        r.equipe_status = equipe_status
        const id = req.query.id > 0 ? req.query.id : 0
        gr('api_account_equipe_por_id', [sid, gid, uid, ip, id], function (equipe) {
            if (equipe.botoes) equipe.botoes = equipe.botoes.split(',')
            r.equipe = equipe
            gr('api_account_despacho_chamado_por_id', [sid, gid, uid, ip, equipe.equipe_chamado], function (chamado) {
                r.chamado = chamado
                gr('api_account_despacho_primeira_regulacao', [sid, gid, uid, ip, equipe.equipe_chamado], function (primeira_reg) {
                    r.primeira_reg = primeira_reg
                    ga('api_account_equipe_telefones', [sid, gid, uid, ip, id], function (telefones) {
                        r.telefones = telefones
                        return tr.display_adm(req, res, automake_name, r)
                    })
                })
            })
        })
    })
}
