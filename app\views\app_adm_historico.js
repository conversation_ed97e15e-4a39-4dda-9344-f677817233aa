const list_sp = 'site.app_cam_thumbnail'
const app_save_data = app
const app_rm_data = app
const app_edit_data = app
const app_title = 'Hist&#243;rico de C&#226;meras'
const app_desc = 'Hist&#243;rio de Eventos e Gravações das C&#226;meras.'
const adm = req.user.ACL_DEV || req.user.ACL_ADMIN ? 1 : 0
const ip = req.IP
ga(
    list_sp,
    [adm, uid, req.IP, req.site.site_id],
    function (tree) {
        r.app_save_data = app_save_data
        r.app_rm_data = app_rm_data
        r.app_edit_data = app_edit_data
        r.app_title = app_title
        r.app_desc = app_desc
        r.tree = tree
        r.r = {}
        return tr.display_adm(req, res, automake_name, r)
    },
    true
)
