import express from 'express'
import Adm from './adm'
import Automake from './automake'
import {red} from './colors'
import {REQ} from './interfaces'
import Main from './main'
export default class SAU {
    main: Main
    automake: Automake
    adm: Adm
    constructor(arg_main: Main, arg_automake: Automake, arg_adm: Adm) {
        this.main = arg_main
        this.automake = arg_automake
        this.adm = arg_adm
        this.main.GET('/', this.public_home.bind(this), false)
        this.main.GET('/adm', this.adm_home.bind(this), true)
        this.main.GET('/painel', this.adm_home.bind(this))
        this.main.GET('/dashboard', this.adm_home.bind(this))
        this.main.GET('/rpc/v1/:app', this.api_call.bind(this))
        this.main.POST('/rpc/v1/:app', this.api_call.bind(this))
        this.main.GET('/app/:app_name', this.automake.app_view.bind(this))
        this.main.POST('/app/:app_name', this.automake.app_view.bind(this))
        this.main.GET('/i/:app_name', this.automake.app_view.bind(this))
        this.main.POST('/i/:app_name', this.automake.app_view.bind(this))
        this.main.GET('/a/:uri/:app_name', this.automake.app_view.bind(this))
        this.main.POST('/a/:uri/:app_name', this.automake.app_view.bind(this))
        this.main.GET('/b/:uri/:app_name/:id', this.automake.app_view.bind(this))
        this.main.POST('/b/:uri/:app_name/:id', this.automake.app_view.bind(this))
        this.main.GET('/api/v2/:app', this.api_call.bind(this))
        this.main.POST('/api/v2/:app', this.api_call.bind(this))
        this.main.POST('/api/v2/jsonrpc/:app', this.api_jsonrpc_call.bind(this))
        this.main.GET('/api/docs/list', this.api_docs_list.bind(this), false)
        this.main.GET('/api/docs/get/:page_title', this.api_docs_get.bind(this), false)
        this.main.GET('/p/:page_name', this.automake.app_page_adm.bind(this))
        this.main.POST('/p/:page_name', this.automake.app_page_adm.bind(this))
        this.main.GET('/ap/:page_name', this.automake.app_page_public.bind(this))
        this.main.POST('/ap/:page_name', this.automake.app_page_public.bind(this))
        this.main.GET('/tarm', this.tarm.bind(this))
        this.main.GET('/tarm/chamados', this.tarm_chamados.bind(this))
        this.main.GET('/api/v1/tarm/chamados', this.tarm_chamados_api.bind(this))
        this.main.GET('/api/v1/pbxip/call_by_agent', this.api_account_pbxip_call_by_agent.bind(this))
        this.main.GET('/api/v1/cidades/:uf', this.api_account_cidades.bind(this))
        this.main.GET('/api/v1/bairros/:cidade', this.api_account_bairros.bind(this))
        this.main.GET('/api/v1/motivos', this.api_account_motivos.bind(this))
    }
    api_jsonrpc_call(req: REQ, res: express.Response) {
        return this.api_call(req, res)
    }
    async api_docs_list(req: REQ, res: express.Response) {
        const sid = req.site?.site_id || 0
        const gid = req.gid
        const uid = req.uid
        const args = [sid, gid, uid, req.IP]
        this.main.db.call('site.api_public_docs_list', args, res)
    }
    async api_docs_get(req: REQ, res: express.Response) {
        const page_title = req.params.page_title
        const sid = req.site?.site_id || 0
        const gid = req.gid
        const uid = req.uid
        const args = [sid, gid, uid, req.IP, page_title]
        this.main.db.call('site.api_public_docs_get', args, res)
    }
    async api_call(req: REQ, res: express.Response) {
        let app = req.body.method
        if (!app && req.params.app) {
            red('legado', req.params.app)
            red('-', req.originalUrl, req.headers.referrer || req.headers.referer)
            app = req.params.app
        }
        if (!app) return this.main.api_err(req, res, 'Invalid API', 'No application to load.')
        let namespace = 'site'
        let method = app.replace(/[^a-zA-Z0-9._]/g, '_')
        if (method.indexOf('.') !== -1) {
            let parts = method.split('.')
            namespace = parts[0]
            method = parts[1]
        }
        const id = namespace + '.' + method
        const sid = req.site?.site_id || 0
        const gid = req.gid
        const uid = req.uid
        const is_auth: boolean = req.user && req.user.uid ? true : false
        const is_dev: boolean = req.user && req.user.acl.indexOf('ACL_DEV') !== -1 ? true : false
        let is_adm: boolean = req.user && req.user.acl.indexOf('ACL_ADMIN') !== -1 ? true : false
        if (!is_adm && is_dev) is_adm = true
        if (method.indexOf('api_') !== 0) return this.main.api_err(req, res, 'ERR-NO-API-PREFIX', 'CHAMADA DE API NÃO INICIA COM PREFIXO.')
        if (method.indexOf('api_adm_') !== -1)
            if (!is_dev && !is_adm) return this.main.api_err(req, res, 'ERR-NO-ACL-ADMIN', 'API ADMINISTRATIVA SOMENTE PARA USUÁRIOS COM ACL ACL_ADMIN')
        if (method.indexOf('api_dev_') !== -1) if (!is_dev) return this.main.api_err(req, res, 'ERR-NO-ACL-DEV', 'API DE DESENVOLVIMENTO SOMENTE PARA USUÁRIOS COM ACL ACL_DEV')
        if (method.indexOf('api_account_') !== -1) if (!is_auth) return this.main.api_err(req, res, 'ERR-NO-AUTH', 'ACESSO SOMENTE PARA USUÁRIO AUTENTICADO.')
        let args = [sid, gid, uid, req.IP]
        if (req.body.params) {
            args = args.concat(req.body.params)
        } else if (req.body.args) {
            args = args.concat(req.body.args)
            red('LEGADO req.body.args', req.params.app)
            red('-', req.originalUrl, req.headers.referrer || req.headers.referer)
        } else if (req.query.args) {
            args = args.concat(req.query.args)
            red('LEGADO req.query.args', req.params.app)
            red('-', req.originalUrl, req.headers.referrer || req.headers.referer)
        }
        let debug: boolean = is_adm
        const debug_from_body: boolean = req.body.debug && (req.body.debug === 'true' || req.body.debug === true || req.body.debug === 1 || req.body.debug === '1')
        //@ts-ignore
        const debug_from_query: boolean = req.query.debug && (req.query.debug === 'true' || req.query.debug === true || req.query.debug === 1 || req.query.debug === '1')
        if (!debug && (debug_from_body || debug_from_query)) debug = true
        try {
            const result = await this.main.db.call(id, args, res)
            if (result.is_err()) return this.main.safe_send_response(res, {success: false, error: result.error()}, true)
            return this.main.safe_send_response(res, {success: true, result: result.ga()}, true)
        } catch (dbErr: any) {
            const error = dbErr.message
            red(error)
            if (!res.headersSent)
                return this.main.safe_send_response(
                    res,
                    {
                        success: false,
                        error,
                        message: 'An error occurred while processing your request'
                    },
                    true
                )
        }
    }
    public_home(req: REQ, res: express.Response) {
        this.main.display(req, res, 'public', 'public_home')
    }
    adm_home(req: REQ, res: express.Response) {
        this.main.display(req, res, 'adm', 'adm_home')
    }
    async tarm(req: REQ, res: express.Response) {
        const ramal_res = await this.main.db.call('api_account_ramal_por_usuario', [req.oid, req.gid, req.uid, req.IP])
        if (ramal_res.is_err()) return res.json({success: false, error: ramal_res.error()})
        const ramal = ramal_res.gr()
        const numero = ramal.numero
        if (!numero) return this.main.redirect(req, res, '/tarm/chamados')
        const atrelar_em = req.query.atrelar_em ? parseInt(req.query.atrelar_em as string) : 0
        const chan = req.query.chan ? req.query.chan : 0
        red('tarm', {atrelar_em, chan})
        if (atrelar_em > 0) {
            await this.main.db.call('api_account_pbxip_atrelar', [req.oid, req.gid, req.uid, req.IP, req.query.atrelar_de, atrelar_em, chan])
            this.main.emit('broadcast', {event_id: 'pbxip', data: {cmd: 'park', id: atrelar_em, chan: chan}})
        } else {
            return this.main.display(req, res, 'adm', 'softphone_tarm', {numero})
        }
    }
    async api_account_pbxip_call_by_agent(req: REQ, res: express.Response) {
        const args = [req.oid, req.gid, req.uid, req.IP]
        await this.main.db.call('api_account_pbxip_call_by_agent', args, res)
    }
    async api_account_cidades(req: REQ, res: express.Response) {
        const uf = req.params.uf || ''
        if (!uf) return res.json({error: 'UF não informada'})
        const args = [req.oid, req.gid, req.uid, req.IP, uf]
        await this.main.db.call('api_account_cidades', args, res)
    }
    async api_account_bairros(req: REQ, res: express.Response) {
        const cidade = req.params.cidade || ''
        if (!cidade) return res.json({error: 'Cidade não informada'})
        const args = [req.oid, req.gid, req.uid, req.IP, cidade]
        await this.main.db.call('api_account_bairros', args, res)
    }
    async api_account_motivos(req: REQ, res: express.Response) {
        const args = [req.oid, req.gid, req.uid, req.IP]
        await this.main.db.call('api_account_motivos', args, res)
    }
    async tarm_chamados_api(req: REQ, res: express.Response) {
        let r: any = {}
        r.uid = req.uid || 0
        r.oid = req.oid || 0
        r.gid = req.gid || 0
        r.IP = req.IP || ''
        r.motivo = req.query.motivo ? req.query.motivo : 0
        r.cidade = req.query.cidade ? req.query.cidade : req.site?.cidade
        r.bairro = req.query.bairro ? req.query.bairro : req.site?.bairro
        r.uf = req.query.uf ? req.query.uf : req.site?.uf
        r.q = req.query.q || {}
        const args = [req.oid, req.gid, req.uid, req.IP, r.motivo, r.cidade, r.bairro]
        const chamados_res = await this.main.db.call('api_account_chamados_tarm', args)
        if (chamados_res.is_err()) return res.json({success: false, error: chamados_res.error()})
        const chamados = chamados_res.ga()
        chamados.forEach((chamado: any) => {
            let status_class = 'secondary'
            if (chamado.status_nome) {
                const status_name = chamado.status_nome.toLowerCase()
                if (status_name.includes('finalizado')) status_class = 'success'
                else if (status_name.includes('cancelado')) status_class = 'danger'
                else if (status_name.includes('andamento')) status_class = 'primary'
                else if (status_name.includes('pen')) status_class = 'warning'
            }
            chamado.status_class = status_class
        })
        return res.json(chamados)
    }
    async tarm_chamados(req: REQ, res: express.Response) {
        let r: any = {}
        r.uid = req.uid || 0
        r.oid = req.oid || 0
        r.gid = req.gid || 0
        r.IP = req.IP || ''
        r.cat = req.query.cat || 0
        const localidade_res = await this.main.db.call('api_account_estado_cidade_padrao', [req.oid, req.gid, req.uid, req.IP])
        if (localidade_res.is_err()) return res.json({success: false, error: localidade_res.error()})
        r.localidade = localidade_res.gr()
        r.motivo = req.query.motivo ? req.query.motivo : 0
        r.estado = req.query.cidade ? req.query.cidade : r.localidade.estado
        r.cidade = req.query.cidade ? req.query.cidade : r.localidade.cidade
        r.bairro = req.query.bairro ? req.query.bairro : req.site?.bairro
        r.uf = req.query.uf ? req.query.uf : req.site?.uf
        r.bairro = r.bairro || '0'
        r.uf = r.uf || '0'
        r.q = req.query.q || {}
        const ramal_res = await this.main.db.call('api_account_ramal_por_usuario', [req.oid, req.gid, req.uid, req.IP])
        if (ramal_res.is_err()) return res.json({success: false, error: ramal_res.error()})
        r.ramal = ramal_res.gr()
        const call_res = await this.main.db.call('api_account_pbxip_call_by_agent', [req.oid, req.gid, req.uid, req.IP])
        if (call_res.is_err()) return res.json({success: false, error: call_res.error()})
        r.call = call_res.gr()
        const fila_res = await this.main.db.call('api_account_pbxip_default_queue', [req.oid, req.gid, req.uid, req.IP])
        if (fila_res.is_err()) return res.json({success: false, error: fila_res.error()})
        r.fila = fila_res.gr()
        return this.main.display(req, res, 'adm', 'tarm_chamados', r)
    }
}
