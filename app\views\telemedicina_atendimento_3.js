// USUARIO E STATUS INICIAL
const userId = 1
const statusAtendimento = 1

// DECLARAÇ&#213;ES DAS FUNÇ&#213;ES
// VERIFICO SE O ID VEIO DA PÁGINA ANTERIOR

const iniciaInterface = function () {
    if (req.query.editaDados === '1')
        // RECEBEMOS A FLAG PARA EDITAR
        carregaAtendimentoId()
    else if (req.query.novoAtendimento === '1')
        // RECEBEMOS A FLAG PARA NOVO ATENDIMENTO
        carregaUltimoAtendimento()
    else if (req.body.id && req.body.id !== '0')
        // SALVANDO DADOS RECUPERADOS
        adicionaDadosClinicos() // O USUÁRIO NÃO PODE CARREGAR A PÁGINA DIRETO, DEPOIS EU TRATO ISSO
    else carregaUltimoAtendimento()
}

// CASO VENHA O ID, EU CARREGO OS DADOS USANDO O ID DO ANTEIMENTO
// CASO NÃO TENHA ID, CARREGO O ÚLTIMO (ACABAMOS DE ADICIONAR UM NOVO ATENDIMENTO)

const carregaAtendimentoId = function () {
    ga('telemedicina.api_verifica_triagem', [sid, gid, uid, ip, req.query.id], function (chamado) {
        r.chamado = chamado

        ga('telemedicina.api_especialidades_perguntas', [sid, gid, uid, ip, chamado[0].TRIAGEM_ESPECIALIDADE_ID], function (perguntas) {
            r.tree = perguntas
            return tr.display_adm(req, res, automake_name, r)
        })
    })
}

const carregaUltimoAtendimento = function () {
    ga('telemedicina.api_verifica_triagem_usuario', [sid, gid, uid, ip, userId, statusAtendimento], function (chamado) {
        r.chamado = chamado

        ga('telemedicina.api_especialidades_perguntas', [sid, gid, uid, ip, chamado[0].TRIAGEM_ESPECIALIDADE_ID], function (perguntas) {
            r.tree = perguntas
            return tr.display_adm(req, res, automake_name, r)
        })
    })
}

const adicionaDadosClinicos = function () {
    const id = req.body.id
    var values = []

    Object.keys(req.body).forEach(function (key) {
        const val = req.body[key]

        if (val === 'on') values.push(Number(key))
    })

    values.forEach(element => {
        gr('telemedicina.api_triagem_passo_3', [Number(id), Number(element)], function (row) {}, true)
    })

    return tr.redirect(req, res, '/app/telemedicina_atendimento_4?id=' + req.body.id + '&editaDados=1')
}

iniciaInterface()
