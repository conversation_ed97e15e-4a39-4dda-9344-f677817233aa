import body_parser from 'body-parser'
import compression from 'compression'
import redis from 'connect-redis'
import cookie_parser from 'cookie-parser'
import crypto from 'crypto'
import dotenv from 'dotenv'
import ejs from 'ejs'
import express from 'express'
import fileUpload from 'express-fileupload'
import session from 'express-session'
import fs from 'fs'
import helmet from 'helmet'
import {decode, encode} from 'html-entities'
import https from 'https'
import humanizeDuration from 'humanize-duration'
import {default as moment, default as MOMENT} from 'moment'
import http from 'node:http'
import numeral from 'numeral'
import os from 'os'
import path from 'path'
import speakingurl from 'speakingurl'
import {v4 as uuidv4} from 'uuid'
import {bg_red, gray, green, red, white, yellow} from './colors'
const ejs_lint = require('ejs-lint')
const jimp = require('jimp')
//@ts-ignore
import ipCountry from 'ip-country'
import {ConnectionOptions} from 'mysql2/promise'
import {createClient} from 'redis'
import Adm from './adm'
import Auth from './auth'
import Automake from './automake'
import DB, {DB_RES} from './db'
import {REQ} from './interfaces'
import SAU from './sau'
export default class MAIN {
    //@ts-ignore
    socket_io_server: any
    //@ts-ignore
    server: http.Server
    //@ts-ignore
    express: express.Express
    //@ts-ignore
    session_server: express.RequestHandler
    COOKIE_SECRET: string
    session_store: any
    server_is_online = false
    watcher: any
    reds_url: string
    port: number = 4443
    site_config: {[key: string]: any} = {}
    sid_by_host: {[key: string]: string} = {}
    site_cache_refresh = false
    site_setup_running = false
    redis: any
    is_dev: boolean = false
    on_docker: boolean = false
    reconnectAttempts = 0
    MAX_RECONNECT_DELAY = 60000
    ENCRYPTION_KEY: string = process.env.ENCRYPTION_KEY || 'kdsf0a9sfuasd90fasdflkadjfalkdfa'
    id: any
    data_disk: any = {}
    root_fs: any
    root_fs_size: any
    mem: any
    interfaces: any
    default_interface: any
    PING_APP_SERVER = 'example.net'
    ttl_server = -1
    PING_GOOGLE = 'example.net'
    ttl_google: number = -1
    temp_current = 0
    temp_max = 0
    resource_pool_status = false
    cpu_qtd = 0
    //@ts-ignore
    //s3: AWS.S3
    db: DB
    //@ts-ignore
    adm: Adm
    //@ts-ignore
    auth: Auth
    //@ts-ignore
    sau: SAU
    db_admin: DB | null = null
    sites: any = {}
    CACHE_LANG: any = {}
    CACHE_LANG_ALL: any = {}
    lang_init_status = false
    FAS_ICONS: string[] = []
    recaptcha_pool: any = {}
    //@ts-ignore
    automake: Automake
    adm_automake_save_index = 0
    version: string = '0'
    constructor() {
        MOMENT.locale('pt-BR')
        this.on_docker = fs.existsSync('/.dockerenv')
        const env_file = this.on_docker ? '/.env' : path.join(__dirname, '..', '..', '.env')
        if (dotenv.config({path: env_file}).error) throw dotenv.config({path: env_file}).error
        this.port = parseInt(process.env.HTTPS_PORT || '4443') || 4443
        const package_json = JSON.parse(fs.readFileSync(path.join(__dirname, '..', 'package.json'), 'utf8'))
        this.version = package_json.version || '0'
        this.is_dev = process.env.OS === 'Windows_NT' && !this.on_docker
        const redis_ip = this.is_dev ? '127.0.0.1' : process.env.bg_redIS_IP || '127.0.0.1'
        const redis_port = this.is_dev ? '6379' : process.env.bg_redIS_PORT || '6379'
        this.reds_url = `redis://${redis_ip}:${redis_port}`
        this.redis = createClient({
            url: this.reds_url,
            password: process.env.bg_redIS_PASSWORD
        })
        this.redis.connect().catch((err: any) => {
            red('Redis Client Error: ' + this.reds_url + ' ' + process.env.bg_redIS_PASSWORD)
            const err_msg = err.message
            red(err_msg)
            process.exit(1)
        })
        this.redis.on('error', (err: any) => {
            red('Redis Client Error: ' + this.reds_url + ' ' + process.env.bg_redIS_PASSWORD)
            const err_msg = err.message
            red(err_msg)
            process.exit(1)
        })
        this.COOKIE_SECRET = process.env.COOKIE_SECRET || '73askjfgasdf7asdg8fgadsjfagdsj'
        this.express = express()
        /*
        AWS.config.update({region: 'us-east-1'})
        this.ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || 'df89asd9fas6df9asdf6a9fa9s'
        this.s3 = new AWS.S3({
            accessKeyId: process.env.S3_USER,
            secretAccessKey: process.env.S3_PASS,
            region: process.env.S3_REGION
        })
        */
        const db_config: ConnectionOptions = {
            user: process.env.APP_MYSQL_USER,
            password: process.env.APP_MYSQL_PASSWORD,
            database: process.env.APP_MYSQL_DB,
            host: this.is_dev ? '127.0.0.1' : process.env.APP_MYSQL_HOST || '',
            port: parseInt(process.env.APP_MYSQL_PORT || '3306'),
            //eslint-disable-next-line @typescript-eslint/naming-convention
            decimalNumbers: true,
            //eslint-disable-next-line @typescript-eslint/naming-convention
            supportBigNumbers: true,
            //eslint-disable-next-line @typescript-eslint/naming-convention
            bigNumberStrings: true,
            //eslint-disable-next-line @typescript-eslint/naming-convention
            multipleStatements: false,
            //eslint-disable-next-line @typescript-eslint/naming-convention
            enableKeepAlive: true,
            //eslint-disable-next-line @typescript-eslint/naming-convention
            keepAliveInitialDelay: 10,
            //eslint-disable-next-line @typescript-eslint/naming-convention
            connectTimeout: 1000,
            timezone: '-03:00',
            charset: 'utf8mb4',
            ssl: undefined
        }
        this.db = new DB('main', db_config, this.start.bind(this))
    }
    async start(error?: string) {
        if (error) throw Error('[STOP]' + error)
        const admin_db_user = process.env.MYSQL_USER_DEV || ''
        const admin_db_password = process.env.MYSQL_PASSWORD_DEV || ''
        if (this.is_dev && admin_db_user != '' && admin_db_user != this.db.config.user) {
            const config: ConnectionOptions = {
                user: admin_db_user,
                password: admin_db_password,
                database: this.db.config.database,
                host: this.db.config.host,
                port: this.db.config.port,
                //eslint-disable-next-line @typescript-eslint/naming-convention
                decimalNumbers: true,
                //eslint-disable-next-line @typescript-eslint/naming-convention
                supportBigNumbers: true,
                //eslint-disable-next-line @typescript-eslint/naming-convention
                bigNumberStrings: true,
                //eslint-disable-next-line @typescript-eslint/naming-convention
                multipleStatements: false,
                //eslint-disable-next-line @typescript-eslint/naming-convention
                enableKeepAlive: true,
                //eslint-disable-next-line @typescript-eslint/naming-convention
                keepAliveInitialDelay: 10,
                //eslint-disable-next-line @typescript-eslint/naming-convention
                connectTimeout: 1000,
                timezone: this.db.config.timezone,
                charset: this.db.config.charset,
                ssl: this.db.config.ssl
            }
            this.db_admin = new DB('admin', config)
        }
        await this.db_upgrades()
        this.site_setup()
    }
    site_cache_changed(id: string, r: any) {
        const site_objects = ['acl', 'automake_field', 'sites', 'tr_lang', 'themes', 'menu', 'menu_cat', 'site_alias']
        if (id != '*' && site_objects.indexOf(id) === -1) return
        const reset_menu = id.indexOf('menu') != -1
        white('RECONFIGURANDO SITE PORQUE "' + id + '" FOI ALTERADO. reset_menu=' + reset_menu, r)
        this.site_setup(reset_menu)
    }
    site_req_setup(_req: express.Request, res: express.Response, next: any) {
        let req: REQ = _req as unknown as REQ
        const host = req.headers.host?.toLowerCase() || ''
        const sid = this.sid_by_host[host]
        req.site = {}
        req.site.site_url = 'https://' + req.headers.host
        if (!sid || this.site_cache_refresh) {
            this.site_cache_refresh = false
            red('site_req_setup sid=' + sid + ' host=' + host)
            red('this.adm.new_site_setup(req, res, next)')
        } else {
            req.site = this.site_config[sid]
            req.oid = parseInt(req.site?.site_id || '0') || 0
            req.sid = parseInt(req.site?.site_id || '0') || 0
            req.site_id = parseInt(req.site?.site_id || '0') || 0
            req.global = this.site_config
            next()
        }
    }
    async site_setup(reset_menu: boolean = true) {
        if (this.site_setup_running) {
            red('site_setup already running, skipping...')
            return
        }
        this.site_setup_running = true
        let self = this
        const hostname = os.hostname()
        const res_sites: DB_RES = await self.db.call('sites', [hostname])
        if (res_sites.is_err()) {
            red('site_setup error: ' + res_sites.error)
            return
        }
        const arg_sites = res_sites.ga()
        self.site_setup_running = false
        const res_site_config: DB_RES = await self.db.call('site_config', [hostname])
        if (res_site_config.is_err()) {
            red('site_setup error: ' + res_site_config.error)
            return
        }
        const arg_site_config = res_site_config.ga()
        for (let i in arg_sites) {
            const sid = arg_sites[i].site_id
            const dns = arg_sites[i].site_name
            const sites_alias_hostname = arg_sites[i].sites_alias_hostname
            let vars = arg_sites[i]
            for (let j in arg_site_config) {
                const r = arg_site_config[j]
                if (r.sid != sid) continue
                vars[r.k] = r.v
            }
            vars.site_url = '//' + dns
            self.sid_by_host[dns] = sid
            if (sites_alias_hostname) self.sid_by_host[sites_alias_hostname] = sid
            if (!self.site_config[sid]) self.site_config[sid] = vars
        }
        const res_menu: DB_RES = await self.db.call('site_menu', [hostname])
        if (res_menu.is_err()) {
            red('site_setup error: ' + res_menu.error)
            return
        }
        const arg_menu = res_menu.ga()
        for (let i in arg_sites) {
            const sid = arg_sites[i].site_id
            if (!reset_menu && self.site_config[sid].menu) continue
            let menu: {[key: string]: any} = {}
            for (let j in arg_menu) {
                let cat = arg_menu[j]
                if (cat.sid > 0 && cat.sid != sid) continue
                if (cat.menu_acl) cat._MENU_ACL = cat.menu_acl.split(',')
                else cat._MENU_ACL = []
                menu[cat.cid] = cat
            }
            for (let cid in menu) {
                let cat = menu[cid]
                cat.items = []
                for (let l in arg_menu) {
                    const item = arg_menu[l]
                    if (item.cid > 0 && cid != item.cid) continue
                    if (item.menu_acl) item._MENU_ACL = item.menu_acl.split(',')
                    else item._MENU_ACL = []
                    if (item.menu_cat_acl) item._MENU_CAT_ACL = item.menu_cat_acl.split(',')
                    else item._MENU_CAT_ACL = []
                    cat.items.push(item)
                }
                menu[cid] = cat
            }
            self.site_config[sid].menu = menu
        }
        this.https_init()
    }
    rnd(length: number, numbers_only: boolean = false): string {
        if (numbers_only) {
            let result = ''
            while (result.length < length) result += Math.floor(Math.random() * 10).toString()
            return result
        }
        return crypto.randomBytes(length).toString('hex')
    }
    https_init() {
        let self = this
        if (self.server_is_online) return self.site_setup_init()
        const redis_session_cfg = {
            client: self.redis,
            secret: process.env.bg_redIS_PASSWORD,
            resave: true,
            ttl: 999999
        }
        self.redis.on('error', (err: any) => {
            red('Redis Client Error: ' + self.reds_url + ' ' + process.env.bg_redIS_PASSWORD)
            const err_msg = err.message
            red(err_msg)
            process.exit(1)
        })
        self.session_store = new redis(redis_session_cfg)
        self.express.use(helmet())
        self.express.use(body_parser.urlencoded({extended: true, limit: '50mb'}))
        self.express.use(body_parser.json({limit: '50mb'}))
        self.express.set('trust proxy', 1)
        self.express.set('json spaces', 40)
        self.express.set('view engine', 'ejs')
        this.session_server = session({
            secret: this.COOKIE_SECRET,
            store: this.session_store,
            proxy: true,
            resave: false,
            //eslint-disable-next-line @typescript-eslint/naming-convention
            saveUninitialized: true,
            rolling: true
        })
        self.express.use(this.session_server)
        self.express.use(cookie_parser(process.env.COOKIE_SECRET))
        self.express.use(compression())
        self.express.use(
            express.static(path.join(__dirname, '../public_html'), {
                //eslint-disable-next-line @typescript-eslint/naming-convention
                maxAge: '1y',
                etag: true,
                //eslint-disable-next-line @typescript-eslint/naming-convention
                lastModified: true,
                index: false
            })
        )
        const mmdb = self.is_dev ? path.join(__dirname, '..', 'mmdb', 'GeoLite2-Country.mmdb') : '/mmdb/GeoLite2-Country.mmdb'
        this.express.use(
            ipCountry.setup({
                mmdb,
                //eslint-disable-next-line @typescript-eslint/naming-convention
                fallbackCountry: 'BR',
                //eslint-disable-next-line @typescript-eslint/naming-convention
                exposeInfo: false
            })
        )
        this.express.use((_req: express.Request, res: express.Response, next: any) => {
            let req: REQ = _req as unknown as REQ
            res.removeHeader('X-Powered-By')
            let ip_list: string[] = []
            if (req.headers['x-forwarded-for']) {
                const forwarded_for = req.headers['x-forwarded-for']
                ip_list = Array.isArray(forwarded_for) ? forwarded_for : forwarded_for.split(',')
            }
            if (req.connection.remoteAddress) ip_list.push(req.connection.remoteAddress)
            req.IP = ip_list.join(',') || '0.0.0.0'
            const agent = req.headers['user-agent']
            let lang = req.headers['accept-language']
            if (!lang) lang = 'pt-br'
            if (lang.indexOf(';') != -1) lang = lang.split(';')[0]
            if (lang.indexOf(',') != -1) lang = lang.split(',')[0]
            if (res.locals && res.locals.country) req.country = res.locals.country
            else req.country = 'BR'
            if (req.country) req.country = req.country.toLowerCase()
            req.lang = lang.toLowerCase()
            req.agent = agent
            let uid: number = 0
            let gid: number = 0
            let sid: number = 0
            if (req.user) {
                uid = req.user.uid
                gid = req.user.gid
            }
            if (req.site) sid = req.site.site_id || 0
            req.uid = uid
            req.gid = gid
            req.sid = sid
            req.site_id = sid
            next()
        })
        self.express.use((_req: express.Request, res: express.Response, next: any) => {
            res
            let req: REQ = _req as unknown as REQ
            let uuid_cookie = req.signedCookies.UUID ? req.signedCookies.UUID : req.cookies.UUID
            if (!uuid_cookie || !this.safe_uuid(uuid_cookie, req)) {
                const new_uuid = this.rnd(32, false)
                req.UUID = new_uuid
                if (req && typeof req.res === 'object' && typeof req.res.cookie === 'function')
                    req.res.cookie('UUID', new_uuid, {
                        //eslint-disable-next-line @typescript-eslint/naming-convention
                        maxAge: 31536000000,
                        //eslint-disable-next-line @typescript-eslint/naming-convention
                        httpOnly: false,
                        path: '/',
                        signed: true
                    })
            } else {
                req.UUID = uuid_cookie
            }
            req.IP = this.express_remote_ip(req)
            let session = req.session as any
            if (session && session.impersonate) req.user = session.impersonate
            if (req.session && req.query.softphone) {
                let softphone: string = req.query.softphone as string
                req.softphone = softphone || ''
                req.session.softphone = softphone || ''
            }
            if (req.session && req.session.softphone) req.softphone = req.session.softphone
            next()
        })
        this.express.use(this.site_req_setup.bind(this))
        this.express.use(
            fileUpload({
                limits: {
                    //eslint-disable-next-line @typescript-eslint/naming-convention
                    fileSize: 50 * 1024 * 1024
                },
                debug: false
            })
        )
        self.server = http.createServer(self.express)
        self.server.on('error', (err: any) => red('error', err))
        green(`http://127.0.0.1:${self.port}`)
        self.server.listen(self.port, self.site_setup_init.bind(this))
    }
    private safe_uuid(uuid: string | undefined, fd: REQ): boolean {
        if (!uuid) return false
        const ok: boolean = uuid && typeof uuid === 'string' && uuid !== 'null' && uuid.length >= 18 ? true : false
        if (!ok) {
            red('invalid uuid', {uuid, ip: fd.IP, type: typeof uuid})
            return false
        }
        const rx_test = /^[a-zA-Z0-9\-_]+$/.test(uuid)
        if (!rx_test) {
            red('invalid uuid', {uuid, ip: fd.IP})
            return false
        }
        return true
    }
    file_upload_setup(_req: express.Request, res: express.Response, next: any) {
        res
        let req: REQ = _req as unknown as REQ
        req.upload_path = '/upload/' + req.site?.site_id || '0'
        req.upload_url = '/upload/' + req.site?.site_id || '0'
        if (req.files)
            if (!this.exists(req.upload_path)) {
                yellow('Criado diretório de uplaod: ' + req.upload_path)
                this.mkpath(req.upload_path)
            }
        next()
    }
    express_initialized: boolean = false
    site_setup_init() {
        if (this.express_initialized) {
            red('[STOP] express_initialized')
            return
        }
        this.express_initialized = true
        if (!this.server) {
            red('[STOP] !server')
            return
        }
        this.server_is_online = true
        this.lang_init()
        this.express.use((req: express.Request, res: express.Response, next: any) => {
            req
            res.setHeader(
                'Content-Security-Policy',
                "default-src * 'unsafe-inline' 'unsafe-eval' data: blob:; script-src * 'unsafe-inline' 'unsafe-eval' data: blob:; style-src * 'unsafe-inline'; img-src * data: blob:; font-src * data:; connect-src *; frame-src * data: blob: about:;"
            )
            res.setHeader('Cross-Origin-Opener-Policy', 'unsafe-none')
            res.setHeader('Cross-Origin-Resource-Policy', 'cross-origin')
            next()
        })
        this.express.use(
            express.static('/public_html', {
                //eslint-disable-next-line @typescript-eslint/naming-convention
                maxAge: '1y',
                etag: true,
                //eslint-disable-next-line @typescript-eslint/naming-convention
                lastModified: true
            })
        )
        this.automake = new Automake(this)
        this.auth = new Auth(this)
        this.adm = new Adm(this, this.auth)
        this.sau = new SAU(this, this.automake, this.adm)
        green('--APP ONLINE--')
    }
    private async db_upgrades() {
        try {
            this.gen_mdc()
        } catch (e) {
            throw Error('Error generating MDC files: ' + e)
        }
    }
    private async gen_mdc() {
        if (!this.is_dev) return
        if (!this.db_admin) return
        const d = this.db_admin
        const res_table: DB_RES = await d.sql('SHOW FULL TABLES WHERE Table_type="BASE TABLE"')
        const table_key = res_table.type() === 'ROWS' ? Object.keys(res_table.ga()[0])[0] : ''
        const table_names = res_table
            .ga()
            .map((t: any) => t[table_key])
            .join('\n')
        const res_proc: DB_RES = await d.sql('SHOW PROCEDURE STATUS WHERE Db = DATABASE()')
        const proc_names = res_proc
            .ga()
            .map((p: any) => p.Name)
            .join('\n')
        const res_func: DB_RES = await d.sql('SHOW FUNCTION STATUS WHERE Db = DATABASE()')
        const func_names = res_func
            .ga()
            .map((f: any) => f.Name)
            .join('\n')
        const res_trigger: DB_RES = await d.sql('SHOW TRIGGERS')
        const trigger_names = res_trigger
            .ga()
            .map((t: any) => t.Trigger)
            .join('\n')
        const res_trigger_event: DB_RES = await d.sql('SHOW TRIGGERS')
        const trigger_event_names = res_trigger_event
            .ga()
            .map((t: any) => t.Event)
            .join('\n')
        const res_view: DB_RES = await d.sql('SHOW FULL TABLES WHERE Table_type="VIEW"')
        const view_names = res_view
            .ga()
            .map((v: any) => v.Tables_in_sico)
            .join('\n')
        const res_event: DB_RES = await d.sql('SHOW EVENTS')
        const event_names = res_event
            .ga()
            .map((e: any) => e.Event)
            .join('\n')
        await this.dev_gen_mdc('tables.mdc', table_names, 'Tabelas do banco de dados', ['*.ts'], false)
        await this.dev_gen_mdc('procedures.mdc', proc_names, 'Procedures do banco de dados', ['*.ts'], false)
        await this.dev_gen_mdc('functions.mdc', func_names, 'Funções do banco de dados', ['*.ts'], false)
        await this.dev_gen_mdc('triggers.mdc', trigger_names, 'Triggers do banco de dados', ['*.ts'], false)
        await this.dev_gen_mdc('trigger_events.mdc', trigger_event_names, 'Eventos dos Triggers do banco de dados', ['*.ts'], false)
        await this.dev_gen_mdc('views.mdc', view_names, 'Views do banco de dados', ['*.ts'], false)
        await this.dev_gen_mdc('events.mdc', event_names, 'Eventos do banco de dados', ['*.ts'], false)
        green('✅ Arquivos de definição do banco de dados gerados com sucesso')
    }
    async dev_gen_mdc(file_name: string, str: string, description: string, globs: string[], always_apply: boolean): Promise<any> {
        str = str.trim()
        if (!str) return
        const rules_dir = path.join(process.cwd(), '.cursor', 'rules')
        if (!fs.existsSync(rules_dir)) fs.mkdirSync(rules_dir, {recursive: true})
        const file_path = path.join(rules_dir, file_name)
        if (!fs.existsSync(file_path)) {
            const dir = path.dirname(file_path)
            if (!fs.existsSync(dir)) fs.mkdirSync(dir, {recursive: true})
        }
        const globs_str = globs.length ? '\nglobs: ' + globs.join(',') : ''
        const s = `---
description: ${description}${globs_str}
alwaysApply: ${always_apply}
---
${str}`
        fs.writeFileSync(file_path, s.trim())
    }
    nl2br(str: string) {
        if (!str) return ''
        if (typeof str === 'string') return str.replace('\n', '<br>\n')
        if (typeof str === 'object') return Buffer.from(str).toString('utf8').replace('\n', '<br>\n')
        return str
    }
    bytes(v: number) {
        if (!v || v === 0) return '0 B'
        const units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB']
        const i = Math.floor(Math.log(v) / Math.log(1024))
        return Math.round((v / Math.pow(1024, i)) * 100) / 100 + ' ' + units[i]
    }
    s2i(v: string) {
        const vvv = parseInt(v)
        if (isNaN(vvv)) return 0
        return vvv
    }
    cat(file: string) {
        return fs.readFileSync(file, 'utf8')
    }
    put(file: string, str: string): boolean {
        try {
            fs.writeFileSync(file, str, 'utf8')
            return true
        } catch (e: any) {
            red(file, e)
            return false
        }
    }
    exists(file: string): boolean {
        try {
            return fs.existsSync(file)
        } catch (e: any) {
            red(file, e)
            return false
        }
    }
    encrypt(text: string, k?: string) {
        const key = k || this.ENCRYPTION_KEY
        const iv = crypto.randomBytes(16)
        const cipher = crypto.createCipheriv('aes-256-gcm', Buffer.from(key, 'hex'), iv)
        const encrypted = Buffer.concat([cipher.update(text, 'utf8'), cipher.final()])
        const auth_tag = cipher.getAuthTag()
        return iv.toString('hex') + ':' + auth_tag.toString('hex') + ':' + encrypted.toString('hex')
    }
    decrypt(text: string, k?: string) {
        const key = k || this.ENCRYPTION_KEY
        const parts = text.split(':')
        if (parts.length === 2) {
            const [iv, encrypted] = parts
            const decipher = crypto.createDecipheriv('aes-256-cbc', Buffer.from(key, 'hex'), Buffer.from(iv, 'hex'))
            return Buffer.concat([decipher.update(Buffer.from(encrypted, 'hex')), decipher.final()]).toString()
        }
        if (parts.length !== 3) throw new Error('Invalid encrypted data format')
        const [iv, auth_tag, encrypted] = parts
        const decipher = crypto.createDecipheriv('aes-256-gcm', Buffer.from(key, 'hex'), Buffer.from(iv, 'hex'))
        decipher.setAuthTag(Buffer.from(auth_tag, 'hex'))
        return decipher.update(Buffer.from(encrypted, 'hex'), undefined, 'utf8') + decipher.final('utf8')
    }
    timestamp() {
        return Math.round(new Date().getTime() / 1000)
    }
    timezone() {
        const tz = -(new Date().getTimezoneOffset() / 60)
        return tz
    }
    sha256(str: string) {
        return crypto.createHash('sha256').update(str).digest('hex')
    }
    sha512(str: string) {
        return crypto.createHash('sha512').update(str).digest('hex')
    }
    md5(str: string) {
        return crypto.createHmac('md5', str).digest('hex')
    }
    hmac(str: string, key: string) {
        return crypto.createHmac('sha256', key).update(str).digest('hex')
    }
    uuid() {
        return this.md5(uuidv4())
    }
    files(dir: string) {
        return fs.readdirSync(dir)
    }
    formated_seconds(ts: number) {
        const now: Date = new Date()
        const diff: number = now.getTime() - new Date(ts).getTime()
        return humanizeDuration(diff, {units: ['y', 'mo', 'w', 'd', 'h', 'm'], round: true})
    }
    mkpath(path: string) {
        return fs.mkdirSync(path, {recursive: true})
    }
    upload(req: any, res: any, id: string) {
        let self = this
        if (!id) return res.json({err: 'upload: falta nome.'})
        const f = req.files[id]
        if (f.truncated) return res.json({err: 'upload: imagem foi truncada.'})
        if (!f) return res.json({err: 'upload: campo com nome ' + id + ' não encontrado em files.'})
        const dir = req.upload_path
        if (!dir) return res.json({err: 'upload: req.upload_path inválido.'})
        const base = self.date_ymd()
        const base_dir = dir + '/' + base
        self.mkpath(base_dir)
        const file_id = self.uuid()
        const name = f.name
        const ext = path.extname(name)
        const file_name = base_dir + '/' + file_id + ext
        let r: any = {}
        r.original_url = req.upload_url + '/' + base + '/' + file_id + ext
        r.origial_file = file_name
        r.file_id = file_id
        r.extension = ext
        r.dir = dir
        r.name = name
        r.base = base
        r.base_dir = base_dir
        try {
            const fd = fs.openSync(file_name, 'w')
            r.status = fs.writeSync(fd, f.data)
            fs.closeSync(fd)
            r.mimetype = f.mimetype
            r.md5 = f.md5
            r.size = self.file_size(file_name)
            red(r)
            return res.json(r)
        } catch (e: any) {
            red(e)
            return res.json({err: e.toString()})
        }
    }
    file_size(file: string) {
        const stats = fs.statSync(file)
        return stats['size']
    }
    date_ymd() {
        const date_obj = new Date()
        const month = date_obj.getUTCMonth() + 1
        const day = date_obj.getUTCDate()
        const year = date_obj.getUTCFullYear()
        return year + '/' + month + '/' + day
    }
    font_by_index(index: number) {
        if (index == 0) return jimp.FONT_SANS_32_WHITE
        if (index == 1) return jimp.FONT_SANS_8_BLACK
        if (index == 2) return jimp.FONT_SANS_10_BLACK
        if (index == 3) return jimp.FONT_SANS_12_BLACK
        if (index == 4) return jimp.FONT_SANS_14_BLACK
        if (index == 5) return jimp.FONT_SANS_16_BLACK
        if (index == 6) return jimp.FONT_SANS_32_BLACK
        if (index == 7) return jimp.FONT_SANS_64_BLACK
        if (index == 8) return jimp.FONT_SANS_128_BLACK
        if (index == 9) return jimp.FONT_SANS_8_WHITE
        if (index == 10) return jimp.FONT_SANS_16_WHITE
        if (index == 11) return jimp.FONT_SANS_32_WHITE
        if (index == 12) return jimp.FONT_SANS_64_WHITE
        if (index == 13) return jimp.FONT_SANS_128_WHITE
        return jimp.FONT_SANS_16_BLACK
    }
    align_x(index: string) {
        if (index == 'HORIZONTAL_ALIGN_LEFT') return jimp.HORIZONTAL_ALIGN_LEFT
        if (index == 'HORIZONTAL_ALIGN_CENTER') return jimp.HORIZONTAL_ALIGN_CENTER
        if (index == 'HORIZONTAL_ALIGN_RIGHT') return jimp.HORIZONTAL_ALIGN_RIGHT
        return jimp.HORIZONTAL_ALIGN_CENTER
    }
    align_y(index: string) {
        if (index == 'VERTICAL_ALIGN_TOP') return jimp.VERTICAL_ALIGN_TOP
        if (index == 'VERTICAL_ALIGN_MIDDLE') return jimp.VERTICAL_ALIGN_MIDDLE
        if (index == 'VERTICAL_ALIGN_BOTTOM') return jimp.VERTICAL_ALIGN_BOTTOM
        return jimp.VERTICAL_ALIGN_MIDDLE
    }
    async image_blur_and_text(req: any, res: any, args: any) {
        const img_font = this.font_by_index(args.img_font)
        const blur_q = parseInt(args.blur ? args.blur : 0)
        const ext = path.extname(args.img)
        const basename = path.basename(args.img, ext)
        const id = basename + '-social.jpg'
        const file_name = req.upload_path + '/' + id
        let r: any = {}
        r.url = req.upload_url + '/' + id
        r.file = file_name
        let loaded_image: any
        const s = {
            text: args.img_str,
            //eslint-disable-next-line @typescript-eslint/naming-convention
            alignmentX: this.align_x(args.alignmentX),
            //eslint-disable-next-line @typescript-eslint/naming-convention
            alignmentY: this.align_y(args.alignmentY)
        }
        loaded_image = await jimp.read(args.img)
        const font = await jimp.loadFont(img_font)
        const w = parseInt(args.maxWidth)
        const h = parseInt(args.maxHeight)
        const x = this.align_x(args.img_x)
        const y = this.align_y(args.img_y)
        if (blur_q > 0) loaded_image.blur(blur_q).print(font, x, y, s, w, h).write(file_name)
        else loaded_image.print(font, x, y, s, w, h).write(file_name)
        return res.send(r)
    }
    safe_send_response(res: express.Response, data: any, is_json: boolean = false) {
        if (res.headersSent) {
            red('Headers already sent, cannot send response')
            return false
        }
        try {
            if (is_json) res.json(data)
            else res.send(data)
            return true
        } catch (err: any) {
            red('Error sending response:', err.message)
            if (!res.headersSent)
                try {
                    res.status(500).send('Internal server error')
                } catch (finalErr: any) {
                    red('Final response send failed:', finalErr.message)
                }
            return false
        }
    }
    gen_password(): string {
        const length = 16
        const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+-='
        let password = ''
        const random_bytes = crypto.randomBytes(length)
        for (let i = 0; i < length; i++) password += charset[random_bytes[i] % charset.length]
        const has_lower = /[a-z]/.test(password)
        const has_upper = /[A-Z]/.test(password)
        const has_number = /[0-9]/.test(password)
        const has_symbol = /[!@#$%^&*()_+\-=]/.test(password)
        if (!has_lower || !has_upper || !has_number || !has_symbol) return this.gen_password()
        return password
    }
    replace_all(target: string, search: string, replacement: string) {
        if (!target) return ''
        if (!search) return target
        if (!replacement) return target
        return target.split(search).join(replacement)
    }
    https_get(url: string, cb: (data: any, error?: string) => void) {
        https
            .get(url, function (res) {
                let data = ''
                res.on('data', function (d) {
                    data += d.toString('utf8')
                })
                res.on('end', function () {
                    if (cb) cb(data)
                })
            })
            .on('error', function (err) {
                red('-- https_get ERROR: ', url, err.message)
                if (cb) cb(undefined, err.message)
            })
    }
    template_inject(view_name: string, str_ejs: string, str_js: string, str_css: string) {
        let ejs = str_ejs || '<!-- template sem conteúdo -->'
        if (str_js) {
            const js = '<script>\n// automake-start: ' + view_name + '\n' + str_js + '\n// automake-start: ' + view_name + '\n</script>'
            if (ejs.indexOf('<JAVASCRIPT/>') == -1) ejs = js + ejs
            else ejs = this.replace_all(ejs, '<JAVASCRIPT/>', js)
        } else {
            ejs = this.replace_all(ejs, '<JAVASCRIPT/>', '')
        }
        if (str_css) {
            const css = '<style>\n/* automake-start: ' + view_name + ' */\n' + str_css + '\n/* automake-end ' + view_name + ' */\n</style>'
            if (ejs.indexOf('') == -1) ejs = css + ejs
            else ejs = this.replace_all(ejs, '', css)
        } else {
            ejs = this.replace_all(ejs, '', '')
        }
        return ejs
    }
    colors_palletes() {
        let l: {pallete_name: string; theme: string; name: string; color: string}[] = []
        l.push({pallete_name: 'Bootstrap4', theme: 'light', name: 'primary', color: '#007bff'})
        l.push({pallete_name: 'Bootstrap4', theme: 'light', name: 'secondary', color: '#6c757d'})
        l.push({pallete_name: 'Bootstrap4', theme: 'light', name: 'success', color: '#28a745'})
        l.push({pallete_name: 'Bootstrap4', theme: 'light', name: 'danger', color: '#dc3545'})
        l.push({pallete_name: 'Bootstrap4', theme: 'light', name: 'warning', color: '#ffc107'})
        l.push({pallete_name: 'Bootstrap4', theme: 'light', name: 'info', color: '#17a2b8'})
        l.push({pallete_name: 'Bootstrap4', theme: 'light', name: 'dark', color: '#343a40'})
        l.push({pallete_name: 'Bootstrap4', theme: 'light', name: 'muted', color: '#6c757d'})
        l.push({pallete_name: 'Bootstrap4', theme: 'dark', name: 'light', color: '#f8f9fa'})
        l.push({pallete_name: 'Bootstrap4', theme: 'dark', name: 'white', color: '#ffffff'})
        l.push({pallete_name: 'Bootstrap4', theme: 'dark', name: 'white', color: '#ffffff'})
        l.push({pallete_name: 'US', theme: 'light', name: 'Greenish', color: '#55efc4'})
        l.push({pallete_name: 'US', theme: 'light', name: 'Faded', color: '#81ecec'})
        l.push({pallete_name: 'US', theme: 'light', name: 'Green Darner Tail', color: '#74b9ff'})
        l.push({pallete_name: 'US', theme: 'light', name: 'Shy Moment', color: '#a29bfe'})
        l.push({pallete_name: 'US', theme: 'light', name: 'City Lights', color: '#dfe6e9'})
        l.push({pallete_name: 'US', theme: 'light', name: 'Mint Leaf', color: '#00b894'})
        l.push({pallete_name: 'US', theme: 'light', name: "Robin's Egg Blue", color: '#00cec9'})
        l.push({pallete_name: 'US', theme: 'light', name: 'Electron Blue', color: '#0984e3'})
        l.push({pallete_name: 'US', theme: 'light', name: 'Exodus Fruit', color: '#6c5ce7'})
        l.push({pallete_name: 'US', theme: 'light', name: 'Soothing Breeze', color: '#b2bec3'})
        l.push({pallete_name: 'US', theme: 'light', name: 'Sour Lemon', color: '#ffeaa7'})
        l.push({pallete_name: 'US', theme: 'light', name: 'First Date', color: '#fab1a0'})
        l.push({pallete_name: 'US', theme: 'light', name: 'Pink Glamour', color: '#ff7675'})
        l.push({pallete_name: 'US', theme: 'light', name: 'Pico 8 Pink', color: '#fd79a8'})
        l.push({pallete_name: 'US', theme: 'light', name: 'Amercian River', color: '#636e72'})
        l.push({pallete_name: 'US', theme: 'light', name: 'Bright Yarrow', color: '#fdcb6e'})
        l.push({pallete_name: 'US', theme: 'light', name: 'Orangeville', color: '#e17055'})
        l.push({pallete_name: 'US', theme: 'light', name: 'Chi-Gong', color: '#d63031'})
        l.push({pallete_name: 'US', theme: 'light', name: 'Prunus Avium', color: '#e84393'})
        l.push({pallete_name: 'US', theme: 'light', name: 'Dracula Orchid', color: '#2d3436'})
        l.push({pallete_name: 'Web2.0', theme: 'light', name: 'silver', color: '#EEEEEE'})
        l.push({pallete_name: 'Web2.0', theme: 'light', name: 'white', color: '#FFFFFF'})
        l.push({pallete_name: 'Web2.0', theme: 'light', name: 'Mag.nolia', color: '#F9F7ED'})
        l.push({pallete_name: 'Web2.0', theme: 'light', name: 'yellow', color: '#FFFF88'})
        l.push({pallete_name: 'Web2.0', theme: 'light', name: 'Mint', color: '#CDEB8B'})
        l.push({pallete_name: 'Web2.0', theme: 'light', name: 'blue', color: '#C3D9FF'})
        l.push({pallete_name: 'Web2.0', theme: 'light', name: 'Shadows', color: '#36393D'})
        l.push({pallete_name: 'Web2.0', theme: 'light', name: 'bold/Mozilla', color: '#FF1A00'})
        l.push({pallete_name: 'Web2.0', theme: 'light', name: 'bold/Rollyo', color: '#CC0000'})
        l.push({pallete_name: 'Web2.0', theme: 'light', name: 'bold/RSS', color: '#'})
        l.push({pallete_name: 'Web2.0', theme: 'light', name: 'bold/Techcrunch', color: '#FF7400'})
        l.push({pallete_name: 'Web2.0', theme: 'light', name: 'bold/Newsvine', color: '#006E2E'})
        l.push({pallete_name: 'Web2.0', theme: 'light', name: 'bold/Flock', color: '#4096EE'})
        l.push({pallete_name: 'Web2.0', theme: 'light', name: 'bold/Flickr', color: '#FF0084'})
        l.push({pallete_name: 'Web2.0', theme: 'light', name: 'bold/Ruby', color: '#B02B2C'})
        l.push({pallete_name: 'Web2.0', theme: 'light', name: 'bold/Vermillion', color: '#D15600'})
        l.push({pallete_name: 'Web2.0', theme: 'light', name: 'bold/Gold', color: '#C79810'})
        l.push({pallete_name: 'Web2.0', theme: 'light', name: 'bold/Olive', color: '#73880A'})
        l.push({pallete_name: 'Web2.0', theme: 'light', name: 'bold/Basecamp', color: '#6BBA70'})
        l.push({pallete_name: 'Web2.0', theme: 'light', name: 'bold/Mozilla', color: '#3F4C6B'})
        l.push({pallete_name: 'Web2.0', theme: 'light', name: 'bold/Digg', color: '#356AA0'})
        l.push({pallete_name: 'Web2.0', theme: 'light', name: 'bold/Crimson', color: '#D01F3C'})
        return l
    }
    sendmail(from: string, to: string, subject: string, html: string, text: string, replyto: string, cb: (error?: string) => void) {
        red('sendmail', from, to, subject, html, text, replyto, typeof cb)
        /*
        let self = this
        if (!from || typeof from !== 'string' || !from.trim()) {
            const error = 'Invalid from email address'
            red(`sendmail validation error: ${error}`)
            if (cb) cb(error)
            return
        }
        if (!to || typeof to !== 'string' || !to.trim()) {
            const error = 'Invalid to email address'
            red(`sendmail validation error: ${error}`)
            if (cb) cb(error)
            return
        }
        if (!subject || typeof subject !== 'string' || !subject.trim()) {
            const error = 'Invalid subject'
            red(`sendmail validation error: ${error}`)
            if (cb) cb(error)
            return
        }
        if (!html || typeof html !== 'string' || !html.trim()) {
            const error = 'Invalid html content'
            red(`sendmail validation error: ${error}`)
            if (cb) cb(error)
            return
        }
        if (!process.env.SES_SMTP_FROM || !process.env.SES_SMTP_USER || !process.env.SES_SMTP_PASS) {
            const error = 'Missing SES configuration'
            red(`sendmail validation error: ${error}`)
            if (cb) cb(error)
            return
        }
        text = text || ''
        const reply = replyto ? replyto : from
        const params = {
            Destination: {
                CcAddresses: [],
                ToAddresses: [to]
            },
            Message: {
                Body: {
                    Html: {Charset: 'UTF-8', Data: html}
                },
                Subject: {Charset: 'UTF-8', Data: subject}
            },
            Source: process.env.SES_SMTP_FROM,
            ReplyToAddresses: [reply]
        }
        const c = {
            apiVersion: '2010-12-01',
            accessKeyId: process.env.SES_SMTP_USER,
            secretAccessKey: process.env.SES_SMTP_PASS,
            sslEnabled: true,
            region: 'us-east-1'
        }
        const sendPromise = new AWS.SES(c).sendEmail(params).promise()
        sendPromise
            .then(function (data) {
                const errstr = 'sendmail> ' + replyto + '->' + to + ': ' + subject
                green(errstr)
                console.log(data)
                if (cb) cb()
            })
            .catch(err => {
                const msg = err?.message || 'Unknown error'
                const code = err?.code || 'UNKNOWN_ERROR'
                const errmsg = code + ': ' + msg
                const errstr = 'sendmail> ' + replyto + '->' + to + ': ' + subject
                red(errstr)
                red(errmsg)
                if (cb) cb(errmsg)
            })
        */
    }
    sendmail_template(req: any, from: any, to: any, subject: any, html: any, text: any, replyto: any, cb: any) {
        red('sendmail_template', req.url, req.params, from, to, subject, html, text, replyto, typeof cb)
        /*
        let self = this
        if (!req?.site) {
            const error = 'Missing site configuration'
            red(`sendmail_template validation error: ${error}`)
            if (cb) cb(error)
            return
        }
        if (!from || !to || !subject) {
            const error = 'Missing required fields: from, to, or subject'
            red(`sendmail_template validation error: ${error}`)
            if (cb) cb(error)
            return
        }
        if (!process.env.SES_SMTP_FROM || !process.env.SES_SMTP_USER || !process.env.SES_SMTP_PASS) {
            const error = 'Missing SES configuration'
            red(`sendmail_template validation error: ${error}`)
            if (cb) cb(error)
            return
        }
        text = text || ''
        const reply = replyto ? replyto : from
        const HTML_FINAL = self.html_template(req.site, subject, text, html)
        const params = {
            Destination: {
                CcAddresses: [],
                ToAddresses: [to]
            },
            Message: {
                Body: {
                    Html: {Charset: 'UTF-8', Data: HTML_FINAL},
                    Text: {Charset: 'UTF-8', Data: text}
                },
                Subject: {Charset: 'UTF-8', Data: subject}
            },
            Source: process.env.SES_SMTP_FROM,
            ReplyToAddresses: [reply]
        }
        const c = {
            apiVersion: '2010-12-01',
            accessKeyId: process.env.SES_SMTP_USER,
            secretAccessKey: process.env.SES_SMTP_PASS,
            sslEnabled: true,
            region: 'us-east-1'
        }
        const sendPromise = new AWS.SES(c).sendEmail(params).promise()
        sendPromise
            .then(data => {
                const errstr = 'sendmail> ' + replyto + '->' + to + ': ' + subject
                green(errstr)
                console.log(data)
                if (cb) cb()
            })
            .catch(err => {
                const msg = err?.message || 'Unknown error'
                const code = err?.code || 'UNKNOWN_ERROR'
                const errmsg = code + ': ' + msg
                const errstr = 'sendmail> ' + replyto + '->' + to + ': ' + subject
                red(errstr)
                red(errmsg)
                if (cb) cb(errmsg)
            })
        */
    }
    html_template(site: any, subject: any, text: any, html: any) {
        let self = this
        let str =
            '<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml"><head style="-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%"><meta charset="utf-8" style="-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%"><meta name="viewport" content="width=device-width" style="-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%"><meta http-equiv="X-UA-Compatible" content="IE=edge" style="-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%"><meta name="x-apple-disable-message-reformatting" style="-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%"><title style="-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%"></title></head><body width="100%" style="-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%;background:#f1f1f1;background-color:#fff;color:rgba(0,0,0,.4);font-family:Poppins,sans-serif;font-size:15px;font-weight:400;height:100%!important;line-height:1.8;margin:0 auto!important;mso-line-height-rule:exactly;padding:0!important;width:100%!important"><style>@media only screen and (min-device-width:320px) and (max-device-width:374px){u~div .email-container{min-width:320px!important}}@media only screen and (min-device-width:375px) and (max-device-width:413px){u~div .email-container{min-width:375px!important}}@media only screen and (min-device-width:414px){u~div .email-container{min-width:414px!important}}@media screen and (max-width:500px){.icon{text-align:left}.text-services{padding-left:0;padding-right:20px;text-align:left}}</style><center style="-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%;background-color:#fff;background-image:url(/images/default_final_ltr.jpg);background-repeat:repeat-x;padding:10px;width:100%"><div style="-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%;display:none;font-family:sans-serif;font-size:1px;max-height:0;max-width:0;mso-hide:all;opacity:0;overflow:hidden">TEXTO</div><div style="-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%;border:1px solid #fff;margin:0 auto;max-width:600px" class="email-container"><table align="center" role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%;border-collapse:collapse!important;border-spacing:0!important;margin:0 auto!important;mso-table-lspace:0!important;mso-table-rspace:0!important;table-layout:fixed!important"><tbody><tr style="-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%"><td valign="top" class="bg_white" style="-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%;background:#fff;border-bottom:5px solid #eee;mso-table-lspace:0!important;mso-table-rspace:0!important;padding:1em 2.5em"><table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%" style="-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%;border-collapse:collapse!important;border-spacing:0!important;margin:0 auto!important;mso-table-lspace:0!important;mso-table-rspace:0!important;table-layout:fixed!important"><tbody><tr style="-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%"><td width="100%" class="logo" style="-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%;mso-table-lspace:0!important;mso-table-rspace:0!important;text-align:right"><img src="LOGO" alt="TITULO" title="TITULO" width="188" height="60" style="-ms-interpolation-mode:bicubic;-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%"></td></tr></tbody></table></td></tr><tr style="-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%"><td class="bg_light email-section" style="-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%;background:#fafafa;color:#000;mso-table-lspace:0!important;mso-table-rspace:0!important;padding:2.5em"><div class="heading-section" style="-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%;padding:0 30px;text-align:left"><h2 style="-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%;color:#ccc;font-family:Poppins,sans-serif;font-size:20px;font-weight:700;line-height:1.4;margin-top:0;text-transform:uppercase">ASSUNTO</h2><hr style="-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%"><p style="-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%">MENSAGEM</p></div></td></tr></tbody></table><table align="center" role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%;border-collapse:collapse!important;border-spacing:0!important;border-top:10px solid #ccc;margin:0 auto!important;mso-table-lspace:0!important;mso-table-rspace:0!important;table-layout:fixed!important"><tbody><tr style="-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%"><td valign="middle" class="bg_black footer email-section" width="100%" style="-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%;background:#2E3064;color:#2E3064;mso-table-lspace:0!important;mso-table-rspace:0!important;padding:2.5em"><h3 class="heading" style="-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%;color:#fff;font-family:Poppins,sans-serif;font-size:20px;margin-top:0">DESCRICAO</h3><ul style="-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%;margin:0;padding:0"><li style="-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%;list-style:none;margin-bottom:10px"><a href="mailto:site_email" style="-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%;color:rgba(255,255,255,1);text-decoration:none">site_email</a></li><li style="-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%;list-style:none;margin-bottom:10px">TELEFONE</li><li style="-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%;list-style:none;margin-bottom:10px"><a href="SITE_TWITTER" style="-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%;color:rgba(255,255,255,1);text-decoration:none">SITE_TWITTER</a></li><li style="-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%;list-style:none;margin-bottom:10px"><a href="SITE_TELEGRAM" style="-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%;color:rgba(255,255,255,1);text-decoration:none">SITE_TELEGRAM</a></li><li style="-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%;list-style:none;margin-bottom:10px"><a href="SITE_DISCORD" style="-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%;color:rgba(255,255,255,1);text-decoration:none">SITE_DISCORD</a></li></ul><span class="text" style="-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%;color:#fff">ENDERECO</span><p style="-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%"><a href="site_urlprivacy_url" style="-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%;color:rgba(255,255,255,.4);text-decoration:none">Política de Privacidade</a></p><p style="-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%;color:#fff">© copyright, Direitos Reservados.</p></td></tr></tbody></table></div></center></body></html>'
        str = self.replace_all(str, 'TELEFONE', site.TELEFONE)
        str = self.replace_all(str, 'ENDERECO', site.ENDERECO)
        str = self.replace_all(str, 'ASSUNTO', subject)
        str = self.replace_all(str, 'TEXTO', text)
        str = self.replace_all(str, 'MENSAGEM', html)
        str = self.replace_all(str, 'site_url', site.site_url)
        str = self.replace_all(str, 'privacy_url', site.terms_url)
        str = self.replace_all(str, 'TITULO', site.title)
        str = self.replace_all(str, 'DESCRICAO', site.description)
        str = self.replace_all(str, 'copyright', site.copyright)
        str = self.replace_all(str, 'site_email', site.site_email)
        str = self.replace_all(str, 'SITE_TWITTER', 'https://witter.com/' + site.SITE_TWITTER)
        str = self.replace_all(str, 'SITE_TELEGRAM', site.SITE_TELEGRAM)
        str = self.replace_all(str, 'SITE_DISCORD', site.SITE_DISCORD)
        str = self.replace_all(str, 'LOGO', site.site_url + '/org/' + site.site_org + '/logo.png')
        str = self.replace_all(str, 'TELEFONE', site.TELEFONE)
        str = self.replace_all(str, 'description', site.description)
        return str
    }
    esc(value: any): string {
        if (value === null || value === undefined) return 'NULL'
        if (typeof value === 'number') return value.toString()
        if (typeof value === 'boolean') return value ? '1' : '0'
        if (value instanceof Date) return `'${value.toISOString()}'`
        const str = value.toString()
        return "'" + str.replace(/'/g, "''").replace(/\\/g, '\\\\') + "'"
    }
    sanitize_identifier(identifier: string): string {
        return identifier.replace(/[^a-zA-Z0-9_]/g, '')
    }
    validate_sql(sql: string): boolean {
        const dangerous_patterns = [
            /;\s*DROP\s+/i,
            /;\s*DELETE\s+FROM\s+/i,
            /;\s*TRUNCATE\s+/i,
            /;\s*UPDATE\s+.*\s+SET\s+/i,
            /;\s*INSERT\s+INTO\s+/i,
            /UNION\s+SELECT/i,
            /OR\s+1\s*=\s*1/i,
            /--\s*$/,
            /\/\*.*\*\//
        ]
        for (const pattern of dangerous_patterns)
            if (pattern.test(sql)) {
                red('SQL injection attempt detected:', sql)
                return false
            }
        return true
    }
    security(req: any, res: any, id: any) {
        if (id && id.indexOf('api_account') !== -1)
            if (!req || !req.user || !req.user.uid || req.user.uid <= 0)
                if (req.xhr || req.url.indexOf('/api/') !== -1 || req.url.indexOf('/rpc/') !== -1) {
                    return res.status(401).json({
                        success: false,
                        message: 'Authentication required',
                        code: 'AUTH_REQUIbg_red'
                    })
                } else {
                    const referrer = encodeURIComponent(req.url)
                    const uri = req.site.site_url + '/login?referrer=' + referrer
                    return res.redirect(uri)
                }
        return true
    }
    format_size(size: any) {
        if (size < 1024) return size + ' bytes'
        if (size < 1024 * 1024) return (size / 1024).toFixed(2) + ' KB'
        if (size < 1024 * 1024 * 1024) return (size / 1024 / 1024).toFixed(2) + ' MB'
        return (size / 1024 / 1024 / 1024).toFixed(2) + ' GB'
    }
    moment(...args: any) {
        return moment(...args)
    }
    async lang_init() {
        if (!this.lang_init_status) {
            this.lang_init_status = true
            numeral.register('locale', 'pt-br', {
                delimiters: {
                    thousands: '.',
                    decimal: ','
                },
                abbreviations: {
                    thousand: 'mil',
                    million: 'milhões',
                    billion: 'b',
                    trillion: 't'
                },
                ordinal: () => {
                    return 'º'
                },
                currency: {
                    symbol: 'R$'
                }
            })
            numeral.locale('pt-br')
        }
        const res_lang: DB_RES = await this.db.call('tr_lang', [])
        if (res_lang.is_err()) {
            red('lang_init error: ' + res_lang.error)
            return
        }
        const tree = res_lang.ga()
        for (let i in tree)
            for (let j in tree[i]) {
                const r = tree[i][j]
                if (!r.lang_code) {
                    red('r.lang_code', i, j, r.lang_code)
                    continue
                }
                const id = r.lang_code.toLowerCase()
                const lang_alias = r.lang_alias.toLowerCase()
                this.CACHE_LANG[id] = r
                this.CACHE_LANG_ALL[id] = r
                this.CACHE_LANG_ALL[lang_alias] = r
            }
    }
    get(key: any, value: any, req: any, res: any) {
        const c = {
            expires: new Date(Date.now() + 900000),
            //eslint-disable-next-line @typescript-eslint/naming-convention
            httpOnly: true,
            path: '/',
            secure: true
        }
        res.cookie(key, value, c)
        req.session[key] = value
    }
    redirect(req: any, res: any, uri: any) {
        if (!uri) red('redirect', uri)
        const url = uri ? uri : req.url
        res.redirect(url)
    }
    menu_setup(req: any, res: any, r: any) {
        res
        if (req.user && req.user.MENU) {
            r.user_menu = req.user.MENU
            return
        }
        if (!req.site) return
        const menu_data = req.site.menu
        let user_acl: any = []
        if (!req.user && req.site._MENU) {
            red('req.site._MENU', req.site._MENU)
            return req.site._MENU
        }
        if (req.user && req.user.uid) user_acl = req.user.acl || []
        let user_menu: any = []
        for (const cid in menu_data) {
            let cat = menu_data[cid]
            if (!this.acl_check(user_acl, cat._MENU_CAT_ACL)) continue
            let new_cat: any = []
            for (let i in cat.items) {
                const j = cat.items[i]
                if (!this.acl_check(user_acl, j._MENU_ACL)) continue
                new_cat.push(j)
            }
            if (new_cat.length) {
                cat.items = new_cat
                user_menu[cid] = cat
            }
        }
        r.user_menu = user_menu
    }
    acl_check(user_acl: string[] | undefined, need_acl: string[]): boolean {
        if (need_acl.length < 1) return true
        if (!user_acl && need_acl.length > 1) return false
        for (const i in need_acl) {
            const need = need_acl[i]
            if (!need) continue
            const found = user_acl && user_acl.indexOf(need) !== -1
            if (found) return true
        }
        return false
    }
    debug_dump(o: any) {
        return this.debug_dump_list(o)
    }
    debug_dump_list(o: any) {
        let l: any = []
        l.push('<div class="table-responsive">')
        l.push('\t<table class="table table-striped jambo_table">')
        if (o)
            for (const i in o) {
                const r = o[i]
                let s = ''
                if (i === '0' || !i) {
                    l.push('\t\t<thead>')
                    s = '\n'
                    for (let j in r) s += '\t\t\t\t<th>' + j + '</th> \n'
                    l.push('\t\t\t<tr class="headings">' + s + '\t\t\t</tr>')
                    l.push('\t\t</thead>')
                    l.push('\t\t<tbody>')
                }
                s = '\t\t\t<% for( const i in tree){ %>\n'
                s += '\t\t\t<% const data_values = tree[i] %>\n'
                s += "\t\t\t   <tr class=\"<%- (i%2===0)?'even':'odd' %>pointer\">\n"
                for (let j in r) {
                    const v = r[j]
                    const c = '\t\t\t<!-- ' + v + ' -->'
                    s += '\t\t\t\t<td ><%= data_values.' + j + ' %></td> ' + c + ' \n'
                }
                s += '\t\t\t   </tr>\n'
                s += '\t\t\t<% } %>'
                l.push(s)
                break
            }
        l.push('\t\t</tbody>')
        l.push('\t</table>')
        l.push('</div>')
        const html = l.join('\n')
        const str = encode(html)
        return '<pre>' + str + '</pre>'
    }
    debug_dump_form(o: any, automake_name: any) {
        let l: any = []
        l.push('<h1><%= automake_name %></h1>')
        l.push('<form action="/app' + automake_name + '" id="form" data-parsley-validate class="form-horizontal form-label-left" method="POST" >')
        for (let j in o) {
            let v = o[j]
            if (!v) v = ''
            if (v.length > 32) v = v.substr(0, 31)
            let s = '\n'
            s += '\t<div class="form-group">\n'
            s += '\t\t<label class="control-label col-md-3 col-sm-3 col-xs-12" for="' + j + '">\n'
            s += '\t\t\t' + j + ' <span class="required">*</span>\n'
            s += '\t\t</label>\n'
            s += '\t\t<div class="col-md-6 col-sm-6 col-xs-12">\n'
            s += '\t\t\t<input value="<%= r.' + j + ' %>" placeholder="' + v + '" type="text" id="' + j + '" name="' + j + '" class="form-control col-md-7 col-xs-12">\n'
            s += '\t\t</div>\n'
            s += '\t</div>\n'
            l.push(s)
        }
        l.push('  <div class="ln_solid"></div>')
        l.push('  <div class="form-group">')
        l.push('    <div class="col-md-6 col-sm-6 col-xs-12 col-md-offset-3">')
        l.push('      <button type="submit" class="btn btn-success">Submit</button>')
        l.push('    </div>')
        l.push('  </div>')
        l.push('</form>')
        const html = l.join('\n')
        const str = encode(html)
        return '<pre>' + str + '</pre>'
    }
    debug_pre(o: any) {
        const s = this.debug_dump(o)
        return decode(s)
    }
    debug_form(req: REQ, app: string, on_submit: any) {
        if (!this.site_config.recaptcha_enabled) red('ERRO: recaptcha_enabled=0')
        if (!app) red('ERRO: !app')
        //return;
        if (req._form_app) {
            red('ignore req._form_app=' + req._form_app + ' new=' + app)
            return
        }
        red('_form_app=' + app)
        req._form_app = app
        req._form_on_submit = on_submit
    }
    combo_gen(label: any, name: any, api_id: any, val: any, required: any, placeholder: any) {
        placeholder = placeholder ? placeholder : ''
        required = required ? 'required' : ''
        val = val ? val : ''
        const required_label = required ? '<span class="red">*</span>' : ''
        let ln = []
        ln.push('<div class="form-group">')
        ln.push('<label for="' + name + '">' + label + required_label + ' <span id="' + name + '_loading" class="label label-default"></span> </label>')
        ln.push('<select name="' + name + '" type="text" placeholder="' + placeholder + '" ')
        ln.push(' ' + required + ' class="form-control ' + required + '" id="' + name + '" ')
        ln.push(' disabled autocomplete="off" >')
        ln.push('</select>')
        ln.push('<script>combo_gen("' + name + '", "' + api_id + '", "' + val + '", "' + required + '")</script>')
        ln.push('</div>')
        return ln.join(' ')
    }
    input_gen(label: any, name: any, val: any, required: any, placeholder: any) {
        placeholder = placeholder ? placeholder : ''
        required = required ? 'required' : ''
        val = val ? val : ''
        const required_label = required ? '<span class="red">*</span>' : ''
        let ln = []
        ln.push('<div class="form-group">')
        ln.push('<label for="' + name + '">' + label + required_label + '</label>')
        ln.push('<input name="' + name + '" type="text" placeholder="' + placeholder + '" ')
        ln.push(' ' + required + ' class="form-control ' + required + '" id="' + name + '" ')
        ln.push(' value="' + val + '" autocomplete="off"> ')
        ln.push('</div>')
        return ln.join(' ')
    }
    password_gen(label: any, name: any, val: any, required: any, placeholder: any) {
        placeholder = placeholder ? placeholder : ''
        required = required ? 'required' : ''
        val = val ? val : ''
        const required_label = required ? '<span class="red">*</span>' : ''
        let ln = []
        ln.push('<div class="form-group">')
        ln.push('<label for="' + name + '">' + label + required_label + '</label>')
        ln.push('<input name="' + name + '" type="password" ')
        ln.push('placeholder="' + placeholder + '" ')
        ln.push(' ' + required + ' class="form-control ' + required + '" id="' + name + '" ')
        ln.push(' value="' + val + '" autocomplete="off"> ')
        ln.push('<input name="' + name + '1" type="password" ')
        ln.push(' placeholder="' + placeholder + ' (novamente)" ')
        ln.push(' ' + required + ' class="form-control ' + required + '" id="' + name + '1" ')
        ln.push(' value="' + val + '"> ')
        ln.push('</div>')
        return ln.join(' ')
    }
    display_err(h1: any, h2: any) {
        const cli_msg = `
        -- DISPLAY ERROR --
        > ${h1}
        > ${h2}
        `
        red(cli_msg)
        let html = []
        html.push('<h1>' + h1 + '</h1>')
        html.push('<pre><code>' + h2 + '</code></pre>')
        return html.join('\n')
    }
    set(key: string, value: any, req: any) {
        req.session[key] = value
    }
    get_lang(req: REQ, res: express.Response, r: any) {
        r = r || {}
        let lang = 'pt-br'
        const default_lang = 'pt-br'
        if (req.user && req.user.lang) {
            lang = req.user.lang
        } else {
            lang = req.headers['accept-language'] || 'pt-br'
            if (!lang) {
                lang = default_lang
            } else if (lang.indexOf(',') === -1) {
                lang = default_lang
            } else {
                const ar = lang.split(',')
                if (!ar[0]) lang = default_lang
                else lang = ar[0]
            }
        }
        if (req.session && req.session.lang) lang = req.session.lang
        lang = lang.toLowerCase()
        if (!lang || !this.CACHE_LANG[lang]) {
            red('CACHE_LANG', lang, ' NÃO ENCONTRADA NO CACHE.')
            lang = default_lang
        }
        req.lang = lang
        r.CACHE_LANG = this.CACHE_LANG
        r.country_id = res.locals.country
        r.LANG = lang
        r.LANG_CONFIG = this.CACHE_LANG_ALL[lang]
        if (!r.LANG_CONFIG) r.LANG_CONFIG = this.CACHE_LANG.en
        return lang
    }
    async form_table_gen(db_id: any, tbl: any, automake_name: any, res: any) {
        const r0: DB_RES = await this.db.call('site.adm_automake_fields_of', [1, db_id, tbl])
        if (r0.is_err()) {
            red('form_table_gen error: ' + r0.error)
            return
        }
        const rows = r0.ga()
        let html = `<h1><%= automake_name %></h1><form action="/app${automake_name}" id="form" data-parsley-validate class="form-horizontal form-label-left" method="POST">`
        for (const row of rows) {
            const field = row.id
            const type = row.type as string
            const val = row.VAL || type
            const max_len = row.maxlen
            const req_attr = row.required ? 'required="required"' : ''
            let input = ''
            if (type.includes('bigint'))
                input = `<input ${req_attr} max="${max_len}" value="<%= r.${field} %>" placeholder="${val}" type="text" id="${field}" name="${field}" class="form-control col-md-7 col-xs-12">`
            else if (type.includes('int') || type.includes('double'))
                input = `<input ${req_attr} value="<%= r.${field} %>" placeholder="${val}" type="number" id="${field}" name="${field}" class="form-control col-md-7 col-xs-12">`
            else if (type.includes('text'))
                input = `<textarea ${req_attr} rows="5" cols="30" placeholder="${val}" id="${field}" name="${field}" class="form-control col-md-7 col-xs-12"><%= r.${field} %></textarea>`
            else if (type.includes('datetime') || type.includes('date'))
                input = `<input ${req_attr} value="<%= r.${field} %>" placeholder="${val}" type="localdate" id="${field}" name="${field}" class="form-control col-md-7 col-xs-12">`
            else
                input = `<input ${req_attr} max="${max_len}" value="<%= r.${field} %>" placeholder="${val}" type="text" id="${field}" name="${field}" class="form-control col-md-7 col-xs-12">`
            html += `<div class="form-group"><label class="control-label col-md-3 col-sm-3 col-xs-12" for="${field}">${field} <span class="required">*</span></label><div class="col-md-6 col-sm-6 col-xs-12">${input}</div></div>`
        }
        html +=
            '<div class="ln_solid"></div><div class="form-group"><div class="col-md-6 col-sm-6 col-xs-12 col-md-offset-3"><button type="submit" class="btn btn-success">Submit</button></div></div></form>'
        return res.send(html)
    }
    debug_dump_args(req: any, res: any) {
        const o = req.body
        let l: any = []
        for (let i in o) {
            const r = o[i]
            const s = 'args.push( req.body.' + i + ' ); // ' + r
            l.push(s)
        }
        l.push('')
        for (let i in o) {
            const r = o[i]
            const s = 'IN arg_' + i + ' text, -- "' + r + '"'
            l.push(s)
        }
        l.push('')
        l.push('alter table XYP ')
        for (let i in o) {
            const r = o[i]
            const s = '     ADD `' + i + '` text, -- "' + r + '"'
            l.push(s)
        }
        l.push('')
        for (let i in o) {
            const r = o[i]
            const s = '`' + i + '` = arg_' + i + ', -- "' + r + '"'
            l.push(s)
        }
        l.push('')
        const html = l.join('\n')
        const str = encode(html)
        const p = '<pre>' + str + '</pre>'
        return res.send(p)
    }
    image_save(base64_buffer: any, label: any, site: any, uid: any) {
        if (!base64_buffer) {
            red('image_save: no image')
            return undefined
        }
        if (base64_buffer.indexOf(',') == -1 || base64_buffer.indexOf('base64') == -1) {
            red('image_save: invalid format')
            return undefined
        }
        const id = this.md5(base64_buffer)
        let base64_data = base64_buffer.split(',')
        let ext = base64_data[0].replace(';base64', '').replace('data:image/', '')
        base64_data = base64_data[1]
        let p = ['/storage', 'images']
        if (site) p.push(site)
        let dt = new Date()
        const y = dt.getFullYear().toString()
        const m = (dt.getMonth() + 1).toString()
        const d = dt.getDate().toString()
        p.push(y)
        p.push(m)
        p.push(d)
        if (uid) p.push(uid)
        if (label) p.push(label.replace(/[^a-zA-Z0-9]/g, '-'))
        const path = p.join('/')
        this.mkpath(path)
        p.push(id)
        let file_name = p.join('/')
        file_name += '.' + ext
        if (!this.exists(file_name)) {
            const binary_data = new Buffer(base64_data, 'base64').toString('binary')
            fs.writeFileSync(file_name, binary_data, 'binary')
        }
        const stats = fs.statSync(file_name)
        const file_size_in_bytes = stats.size
        red(file_name, file_size_in_bytes)
        if (!this.exists(file_name)) {
            red(file_name + ' nao existe')
            return
        }
        return {file_name: file_name, file_size: file_size_in_bytes, ext: ext, path: path, id: id, stats: stats}
    }
    icons(type: string) {
        red('icons', type)
        if (!type || type == 'fas') {
            if (this.FAS_ICONS.length > 0) return this.FAS_ICONS
            const icons_file = path.join(__dirname, '../public_html/icons.json')
            const icons_str = this.cat(icons_file)
            this.FAS_ICONS = JSON.parse(icons_str)
            return this.FAS_ICONS
        }
        return []
    }
    var_parse(req: any, q: any) {
        if (!q) return ''
        q = this.replace_all(q, '${uid}', req.uid)
        q = this.replace_all(q, '${gid}', req.gid)
        q = this.replace_all(q, '${sid}', req.sid)
        for (let i in req.site) {
            const k = '${' + i + '}'
            const v = req.site[i]
            if (typeof v != 'string') continue
            if (v.indexOf('$') == -1) continue
            if (typeof v != 'string') continue
            q = this.replace_all(q, k, v)
        }
        for (let i in req.user) {
            const k = '${' + i + '}'
            const v = req.site[i]
            if (typeof v != 'string') continue
            if (v.indexOf('$') == -1) continue
            q = this.replace_all(q, k, v)
        }
        return q
    }
    convert(str: string, lang: string, mc: boolean) {
        //eslint-disable-next-line @typescript-eslint/naming-convention
        return speakingurl(str, {lang: lang ? lang : 'pt', maintainCase: mc})
    }
    datepicker(id: string, fctl: any, label: any, ds: any, de: any) {
        let html = `
    <script src="/vendors/moment/min/MOMENT.min.js"></script>
    <link href="/vendors/bootstrap-daterangepicker/daterangepicker.css" rel="stylesheet" >
    <script src="/vendors/bootstrap-daterangepicker/daterangepicker.js"></script>	
    
    <div id="ID${id}" class="rounded shadow" style="padding:5px;" data-id="${id}" data-fctl="${fctl}" data-label="${label}" data-ds="${ds}" data-de="${de}">
	  <i class="glyphicon glyphicon-calendar fa fa-calendar"></i>
	  <span>Erro: calend&aacute;rio n&atilde;o pode ser configurado.</span> <b class="caret"></b>
	</div>
	<script>
	let ${id}_PICKER;
    $(document).ready(function() {
		
		let optionSet1 = {
		  startDate: moment('${ds}'),
		  endDate: moment('${de}'),
		  timePicker: false,
		  timePickerIncrement: 1,
		  timePicker12Hour: false,
		  ranges: {
			'Hoje': [moment(), moment()],
			'Otem': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
			'&Uacute;ltimos 7 dias': [moment().subtract(6, 'days'), moment()],
			'&Uacute;ltimos 60 dias': [moment().subtract(60, 'days'), moment()],
			'&Uacute;ltimos m&ecirc;s': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
		  },
		  opens: 'left',
		  buttonClasses: ['btn btn-default'],
		  applyClass: 'btn-small btn-primary',
		  cancelClass: 'btn-small',
		  format: 'DD/MM/YYYY',
		  separator: ' At&eacute; ',
		  locale: {
			format: 'DD/MM/YYYY',
			applyLabel: 'Filtrar',
			cancelLabel: 'Limpar',
			fromLabel: 'De',
			toLabel: 'At&eacute;',
			customRangeLabel: 'Personalizado',
			daysOfWeek: ['Dom', 'Seg', 'Ter', 'Quar', 'Qui', 'Sex', 'S&aacute;b'],
			monthNames: ['Janeiro', 'Fevereiro', 'Mar&ccedil;o', 'Abril', 'Maio', 'Junho', 
				'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'],
			firstDay: 1
		  }
		};
		$('#ID${id} span').html( moment('${ds}').format('DD/MM/YYYY') + ' - ' + moment('${de}').format('DD/MM/YYYY') );
		var ${id}_SELECAO = function(start, end, label) { $('#ID${id} span').html(start.format('DD/MM/YYYY') + ' - ' + end.format('DD/MM/YYYY'));  };
		${id}_PICKER = $('#ID${id}').daterangepicker(optionSet1, ${id}_SELECAO);
		$('#ID${id}').on('apply.daterangepicker', function(ev, picker) { ${fctl}(${id}_PICKER); });
		$('#options1').click(function() { ${id}_PICKER.data('daterangepicker').setOptions(optionSet1, ${id}_SELECAO); });
		$('#options2').click(function() { ${id}_PICKER.data('daterangepicker').setOptions(optionSet2, ${id}_SELECAO); });
		$('#destroy').click(function() { ${id}_PICKER.data('daterangepicker').remove(); });
	});
	</script>
	<style>
.daterangepicker.opensright .ranges, .daterangepicker.opensright .calendar, .daterangepicker.openscenter .ranges, .daterangepicker.openscenter .calendar {
  float: right; }

.daterangepicker table {
  width: 100%;
  margin: 0; }

.daterangepicker td, .daterangepicker th {
  text-align: center;
  width: 20px;
  height: 20px;
  cursor: pointer;
  white-space: nowrap; }

.daterangepicker td.off {
  color: #999; }

.daterangepicker td.disabled {
  color: #999; }

.daterangepicker td.available:hover, .daterangepicker th.available:hover {
  background: #eee;
  color: #34495E; }

.daterangepicker td.in-range {
  background: #E4E7EA;
  border-radius: 0; }

.daterangepicker td.available + td.start-date {
  border-radius: 4px 0 0 4px; }

.daterangepicker td.in-range + td.end-date {
  border-radius: 0 4px 4px 0; }

.daterangepicker td.start-date.end-date {
  border-radius: 4px !important; }

.daterangepicker td.active, .daterangepicker td.active:hover {
  background-color: #536A7F;
  color: #fff; }

.daterangepicker td.week, .daterangepicker th.week {
  font-size: 80%;
  color: #ccc; }

.daterangepicker select.monthselect, .daterangepicker select.yearselect {
  font-size: 12px;
  padding: 1px;
  height: auto;
  margin: 0;
  cursor: default;
  height: 30px;
  border: 1px solid #ADB2B5;
  line-height: 30px;
  border-radius: 0px !important; }

.daterangepicker select.monthselect {
  margin-right: 2%;
  width: 56%; }

.daterangepicker select.yearselect {
  width: 40%; }

.daterangepicker select.hourselect, .daterangepicker select.minuteselect, .daterangepicker select.ampmselect {
  width: 50px;
  margin-bottom: 0; }

.daterangepicker_start_input {
  float: left; }

.daterangepicker_end_input {
  float: left;
  padding-left: 11px; }

.daterangepicker th.month {
  width: auto; }

.daterangepicker .daterangepicker_start_input label, .daterangepicker .daterangepicker_end_input label {
  color: #333;
  display: block;
  font-size: 11px;
  font-weight: normal;
  height: 20px;
  line-height: 20px;
  margin-bottom: 2px;
  text-shadow: #fff 1px 1px 0px;
  text-transform: uppercase;
  width: 74px; }

.daterangepicker .ranges input {
  font-size: 11px; }

.daterangepicker .ranges .input-mini {
  background-color: #eee;
  border: 1px solid #ccc;
  border-radius: 4px;
  color: #555;
  display: block;
  font-size: 11px;
  height: 30px;
  line-height: 30px;
  vertical-align: middle;
  margin: 0 0 10px 0;
  padding: 0 6px;
  width: 74px; }

.daterangepicker .ranges .input-mini:hover {
  cursor: pointer; }

.daterangepicker .ranges ul {
  list-style: none;
  margin: 0;
  padding: 0; }

.daterangepicker .ranges li {
  font-size: 13px;
  background: #f5f5f5;
  border: 1px solid #f5f5f5;
  color: #536A7F;
  padding: 3px 12px;
  margin-bottom: 8px;
  border-radius: 5px;
  cursor: pointer; }

.daterangepicker .ranges li.active, .daterangepicker .ranges li:hover {
  background: #536A7F;
  border: 1px solid #536A7F;
  color: #fff; }

.daterangepicker .calendar {
  display: none;
  max-width: 310px; }

.daterangepicker.show-calendar .calendar {
  display: block; }

.daterangepicker .calendar.single .calendar-date {
  border: none; }

.daterangepicker.single .ranges, .daterangepicker.single .calendar {
  float: none; }

.daterangepicker .ranges {
  width: 160px;
  text-align: left;
  margin: 4px; }

.daterangepicker .ranges .range_inputs > div {
  float: left; }

.daterangepicker .ranges .range_inputs > div:nth-child(2) {
  padding-left: 11px; }

.daterangepicker.opensleft .ranges, .daterangepicker.opensleft .calendar {
  float: left;
  margin: 4px; }

.daterangepicker .icon {
  width: 20px;
  height: 20px;
  display: inline-block;
  vertical-align: middle; 
}
  

/** bootstrap-daterangepicker **/
/** FullCalendar **/
.fc-state-default {
  background: #f5f5f5;
  color: #73879C; }

.fc-state-down,
.fc-state-active {
  color: #333;
  background: #ccc; }

/** /FullCalendar **/
/** Dropzone.js **/
.dropzone {
  min-height: 300px;
  border: 1px solid #e5e5e5; }	
	</style>
    `
        return html
    }
    whatsapp(tel: string) {
        if (!tel) return '-'
        let num = '55' + tel.replace(/\D/g, '')
        return '<a href="https://wa.me/' + num + '" class="text-success" target="_blank"><i class="fab fa-whatsapp"></i> ' + tel + '</a>'
    }
    moeda(v: string) {
        if (!v) return '-'
        return numeral(parseFloat(v)).format('0,0.00')
    }
    humanize(secons: any) {
        return MOMENT.duration(secons, 'seconds').humanize()
    }
    date_format(d: any, f: any) {
        f = f || 'DD/MM/YY hh:mm'
        return moment(d).format(f)
    }
    //eslint-disable-next-line @typescript-eslint/naming-convention
    GET(path: string, callback: (req: any, res: any) => void, ensure_login: boolean = true) {
        if (ensure_login) this.express.get(path, this.auth.ensure_login(), callback)
        else this.express.get(path, callback)
    }
    //eslint-disable-next-line @typescript-eslint/naming-convention
    POST(path: string, callback: (req: any, res: any) => void, ensure_login: boolean = true) {
        if (ensure_login) this.express.post(path, this.auth.ensure_login(), callback)
        else this.express.post(path, callback)
    }
    //eslint-disable-next-line @typescript-eslint/naming-convention
    ALL(path: string, callback: (req: any, res: any) => void, ensure_login: boolean = true) {
        if (ensure_login) this.express.all(path, this.auth.ensure_login(), callback)
        else this.express.all(path, callback)
    }
    async user_add_social(req: any, access_token: any, refresh_token: any, profile: any, cb: any) {
        access_token
        refresh_token
        red('profile', profile)
        const provider = profile.provider
        let pic = ''
        if (profile.photos && profile.photos[0]) pic = profile.photos[0].value
        const display_name = profile.displayName
        let username = profile.username
        if (!username) username = display_name
        let email = username + '@' + provider
        if (profile.emails && profile.emails[0]) email = profile.emails[0].value
        const id = profile.id
        const email_hash = this.auth.hash_password(email + provider + req.site?.site_id)
        const pwd = this.auth.hash_password(profile.email + id)
        const api_id = this.uuid()
        const api_key = this.encrypt(api_id, this.site_config.IV)
        let lang = 'pt-br'
        if (profile._json.language) lang = profile._json.language
        if (profile._json.lang) lang = profile._json.lang
        let location = profile._json.location ? profile._json.location : ''
        let url = profile._json.url ? profile._json.url : ''
        let followers_count = profile.url ? profile.url : 0
        let created: number = this.timestamp()
        if (profile._json.created_at) created = new Date(profile._json.created_at).getTime() / 1000
        let reflink = ''
        const args = [
            req.site?.site_id,
            provider,
            id,
            username,
            display_name,
            email,
            email_hash,
            pwd,
            lang,
            api_key,
            api_id,
            this.site_config.SALT,
            reflink,
            pic,
            location,
            url,
            followers_count,
            created
        ]
        const r = await this.db.call('user_add_social', args)
        if (r.error) return cb(r.error)
        const data = r.gr()
        if (data?.acl) data.acl = data.acl.split(',')
        return cb(null, data)
    }
    get_safe_user_data(user: any) {
        return {
            uid: user.uid,
            user_name: user.user_name,
            user_email: user.user_email,
            acl: user.acl || [],
            group_name: user.group_name,
            lang: user.lang
        }
    }
    is_ajax_request(req: REQ): boolean {
        return req.headers['x-requested-with'] === 'XMLHttpRequest' || req.headers['content-type']?.includes('application/json') || req.url.startsWith('/api/')
    }
    automake_add(app_name: any, arg_query: any) {
        const div = 'automake_div_' + app_name
        let query = ''
        for (let q in arg_query) query += q + '=' + arg_query[q] + '&'
        red('INTERNAL automake_add', app_name, arg_query)
        let html = `
    <div id="${div}">
        <div class="alert alert-danger" role="alert">
            <i class="fas fa-hourglass-half"></i>
            Aguarde, estamos carregando a aplicação ${app_name}...
        </div>
    </div>
    <script>
    $( document ).ready( function(){
        HTML('/aa/${app_name}?${query}', function(str){
            $('#${div}').html(str);
        });
    } );
    </script>
    `
        return html
    }
    api_err(req: REQ, res: express.Response, message: any, details: any) {
        red('jsonrpc err', req.originalUrl, req.headers.referrer || req.headers.referer)
        red(req.IP, message, details)
        if (req.query) red('query', req.query)
        if (req.body) red('body', req.body)
        const err = {success: false, message: message, details: details}
        return res.json(err)
    }
    express_remote_ip(req: REQ): string {
        let ip_list: string | string[] = req.ip || req.headers['x-forwarded-for'] || req.headers['x-forwarded-for'] || []
        let ip: string = ip_list && typeof ip_list === 'string' ? ip_list : ''
        if (!ip) ip = ip_list && Array.isArray(ip_list) ? ip_list[0] : ''
        if (!ip) ip = req.connection.remoteAddress || ''
        if (!ip) ip = '0.0.0.0'
        return ip
    }
    display_adm(req: REQ, res: express.Response, view: string, data: any = {}) {
        return this.display(req, res, 'adm', view, data)
    }
    display(req: REQ, res: express.Response, template: string, view: string = 'NO VIEW', data: any = {}) {
        if (data.error) {
            if (req.xhr) return res.status(500).json(data)
            return data
        }
        let self = this
        data = data || {}
        data.version = this.version
        data.LANG = this.get_lang(req, res, data)
        data.user = req.user
        if (req.user && req.user.uid > 0) data.auth = true
        else data.auth = false
        data.is_dev = process.env.NODE_ENV === 'Windows_NT'
        data.req = req
        data.acl_check = (acl: string[]) => {
            let user_acl: string[] = []
            if (req.user && req.user.uid) user_acl = req.user.acl || []
            return self.acl_check(user_acl, acl)
        }
        data.site = req.site || {}
        if (req.site && req.site.recaptcha_site_key3) {
            data.recaptcha_site_key3 = req.site.recaptcha_site_key3
            data.recaptcha_site_key2 = req.site.recaptcha_site_key2
        } else {
            data.recaptcha_site_key3 = ''
            data.recaptcha_site_key2 = ''
        }
        if (!req.site || !req.site.title) {
            data.TITULO = 'SAU'
            data.copyright = 'INOVA COMUNICAÇÕES LTDA'
        }
        if (!data.TITULO && req.site && req.site.title) data.TITULO = req.site.title
        self.menu_setup(req, res, data)
        data.ACL = (id: string) => {
            let user_acl: any[] = []
            if (req.user && req.user.uid) user_acl = req.user.acl || []
            return self.acl_check(user_acl, [id])
        }
        data.var_parse = self.var_parse.bind(self)
        if (!data.FORM)
            data.FORM = (app: any, on_submit: any) => {
                self.debug_form(req, app, on_submit)
            }
        if (data._form_app && req._form_app) {
            yellow('IGNORE data._form_app=' + data._form_app + ' new=' + req._form_app)
        } else if (!data._form_app && req._form_app) {
            data._form_app = req._form_app
            data._form_on_submit = req._form_on_submit
            yellow('data._form_app=' + req._form_app)
        }
        data.TIMESTAMP = self.timestamp.bind(self)
        data.GETTIME = new Date().getTime()
        data.UUID = self.uuid.bind(self)
        data.convert = self.convert.bind(self)
        data.input_gen = self.input_gen.bind(self)
        data.password_gen = self.password_gen.bind(self)
        data.combo_gen = self.combo_gen.bind(self)
        data.nl2br = self.nl2br.bind(self)
        data.bytes = self.bytes.bind(self)
        data.tr = self
        data.TR = self
        data.dump = self.debug_dump.bind(self)
        data.pre = self.debug_pre.bind(self)
        data.moeda = self.moeda.bind(self)
        data.nl2br = self.nl2br.bind(self)
        data.req = req
        data.ip = req.IP
        data.site = req.site
        data.USERNAME = req.session?.user_name || ''
        data.country_id = req.country_id
        data.__view = view
        data.xhr = req.xhr
        data.LANG = self.get_lang(req, res, data)
        data.localdev = self.site_config.localdev
        data.recaptcha_enabled = self.site_config.recaptcha_enabled
        data.AUTH_UID = 0
        data.auth = req.isAuthenticated()
        data.AUTH_UID = req.uid
        if (!data.user) data.user = req.user
        if (req.user) data.country_id = req.user.country
        if (!data._form_app) data._form_app = ''
        if (!data._form_on_submit) data._form_on_submit = ''
        if (req.session && req.session.impersonate) data.impersonate = req.session.impersonate
        else data.impersonate = false
        if (!data.user) data.user = {}
        data.recaptcha_site_key3 = req.site?.recaptcha_site_key3
        data.recaptcha_site_key2 = req.site?.recaptcha_site_key2
        data.rds_debug = data.rds_debug || []
        data.TITLE = data.TITLE ? data.TITLE : req.site?.title
        data.referrer = req.headers.referrer || req.headers.referer
        if (!data.site_url) data.site_url = 'https://' + req.headers.host
        data.title = data.TITULO ? data.TITULO : data.id
        data.description = data.description || 'Portal Corporativo'
        data.og_title = data.og_title || data.TITULO
        data.og_url = data.og_url || req.url
        data.og_image = data.og_image || data.site_url + '/images/mini-logo.png'
        data.og_type = data.og_type || 'article'
        data.softphone = req.softphone ? req.softphone : 0
        data.host = req.headers.host
        data.hostname = req.hostname ? req.hostname : req.headers.host
        data.no_live = data.no_live || false
        data.LIVE_RELOAD_PORT = process.env.LIVE_RELOAD_PORT
        data.LIVE_RELOAD_HOST = process.env.LIVE_RELOAD_HOST
        data.data = data
        data.moment = this.moment.bind(this)
        res.setHeader(
            'Content-Security-Policy',
            "default-src * 'unsafe-inline' 'unsafe-eval' data: blob: about:; script-src * 'unsafe-inline' 'unsafe-eval' data: blob:; style-src * 'unsafe-inline'; img-src * data: blob:; font-src * data:; connect-src *; frame-src * data: blob: about:;"
        )
        res.setHeader('Cross-Origin-Opener-Policy', 'unsafe-none')
        res.setHeader('Cross-Origin-Resource-Policy', 'cross-origin')
        const is_html = req.query.output == 'html'
        const filename = is_html || req.xhr ? view : template
        const file = path.join(__dirname, '..', 'views', filename + '.ejs')
        const view_file = path.join(__dirname, '..', 'views', view + '.ejs')
        if (file !== view_file) gray(`${file} > ${view_file}`)
        else gray(`${file}`)
        ejs.renderFile(file, data, {filename}, async (err: any, str: any) => {
            if (err) {
                const err_str = err.toString()
                const file_name = path.basename(file)
                let lint_error: any
                bg_red(`EJS ERROR: ${file}`)
                try {
                    const template_content = fs.readFileSync(file, 'utf8')
                    const lint_result = ejs_lint.default(template_content, data)
                    if (lint_result) lint_error = lint_result
                } catch (lint_err: any) {
                    bg_red(lint_err.toString())
                }
                if (lint_error) bg_red(`${file}:${lint_error.line}:${lint_error.column} - ${lint_error.message}`)
                else bg_red(lint_error)
                if (res.headersSent) {
                    red('Headers already sent, cannot send template error response')
                    return
                }
                try {
                    const r = {h1: file_name, h2: err_str, pre: lint_error, nl2br: self.nl2br}
                    const html = await ejs.renderFile(path.join(__dirname, '..', 'views', 'html_err.ejs'), r)
                    res.send(html)
                } catch (templateErr: any) {
                    red('Failed to render error template:', templateErr.message)
                    if (!res.headersSent)
                        try {
                            res.status(500).send(`
                                <h1>Template Error</h1>
                                <p>${file_name}: ${err_str}</p>
                                <pre>${lint_error || 'No additional error details'}</pre>
                            `)
                        } catch (finalErr: any) {
                            red('Final template error send failed:', finalErr.message)
                        }
                }
            } else {
                self.safe_send_response(res, str)
            }
        })
    }
    async emit(event: string, data: any) {
        red(event, data)
    }
}
