{"compilerOptions": {"target": "esnext", "lib": ["esnext"], "module": "nodenext", "moduleResolution": "nodenext", "allowImportingTsExtensions": false, "sourceMap": true, "removeComments": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "strict": true, "allowUnreachableCode": true, "skipLibCheck": true, "noErrorTruncation": false, "outDir": "./dist"}, "include": ["src/**/*"], "exclude": ["dist", "node_modules", "**/node_modules/**"], "ts-node": {"compilerOptions": {"module": "commonjs", "moduleResolution": "node"}}}