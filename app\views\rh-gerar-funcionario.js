if (req.query.op == 'enviar_email') {
    const para = req.body.email
    const assunto = req.body.assunto
    const mensagem = req.body.mensagem
    const from = req.site.site_email
    const reply_to = req.user.site_email
    io.sendmail(from, para, assunto, mensagem, '', reply_to, function (err) {
        let tree = {success: true, message: 'Mensagem enviada!'}
        if (err) {
            tree = {success: false, message: err.message}
        }
        return res.json(tree)
    })
} else {
    gr(
        'api_account_user_by_uid',
        [req.site.site_id, req.user.gid, req.user.uid, req.IP, req.query.funcionario_login],
        function (funcionario, s, err) {
            //console.log(funcionario);
            r.funcionario = funcionario
            return tr.adm(req, res, '/rh-gerar-funcionario', r)
        },
        true
    )
}
