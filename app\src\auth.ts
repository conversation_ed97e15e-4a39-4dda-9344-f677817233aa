import express from 'express'
import passport from 'passport'
import randomize from 'randomatic'
import isEmail from 'validator/lib/isEmail'
import {green, red, yellow} from './colors'
import {REQ} from './interfaces'
import Main from './main'
const email_validator = require('email-deep-validator')
const local_strategy = require('passport-local')
export default class AUTH {
    main: Main
    user_error_cache: any = {}
    user_by_uid: any = {}
    constructor(arg_main: Main) {
        this.main = arg_main
        setInterval(() => {
            this.user_error_cache = {}
        }, 60000)
        this.main.express.use(passport.initialize())
        this.main.express.use(passport.session())
        const local_strategy_config = {
            //eslint-disable-next-line @typescript-eslint/naming-convention
            usernameField: 'u',
            //eslint-disable-next-line @typescript-eslint/naming-convention
            passwordField: 'h',
            //eslint-disable-next-line @typescript-eslint/naming-convention
            passReqToCallback: true
        }
        passport.serializeUser((user: any, done: any) => {
            done(null, user.uid)
        })
        passport.deserializeUser(async (uid: string, done: any) => {
            if (this.user_by_uid[uid]) {
                done(null, this.user_by_uid[uid])
            } else {
                const res = await this.main.db.call('_user_by_uid', [uid])
                if (res.is_err()) return done(null, false, {message: res.error()})
                const r = res.gr()
                const processed_user = this.process_user_data(r)
                this.user_by_uid[uid] = processed_user
                done(null, processed_user)
            }
        })
        passport.use(
            new local_strategy(local_strategy_config, async (req: any, username: string, password: string, done: any) => {
                try {
                    if (!req.session) return done(null, false, {message: 'Session not available'})
                    req.session.returnTo = req.body.referrer
                    const q = req.body || req.query
                    if (q.name) {
                        red('Bot detected:', q.name)
                        return done(null, false, {message: 'Bot detected'})
                    }
                    const error_count = this.user_error_cache[username] || 0
                    if (error_count >= 5) {
                        red(`User ${username} blocked after ${error_count} failed attempts`)
                        return done(null, false, {message: 'Account temporarily locked'})
                    }
                    const origin = q.origin || 'local'
                    let ts = this.main.timestamp()
                    if (origin === 'magic' && req.query.ts && req.query.h && req.query.u) {
                        const magic_result = this.validate_magic_login(req.query)
                        if (!magic_result.valid) return done(null, false, {message: magic_result.error})
                        ts = req.query.ts
                        password = req.query.h
                        username = req.query.u
                    }
                    const auth_args = [origin, ts, req.site_id, username, password, req.lang, req.country, req.IP, req.agent]
                    const res = await this.main.db.call('user_auth', auth_args)
                    if (res.is_err()) return done(null, false, {message: res.error()})
                    const result = res.gr()
                    try {
                        if (!result || !result.uid) {
                            this.user_error_cache[username] = error_count + 1
                            red(`Auth failed for ${username} from ${req.IP}`)
                            return done(null, false, {message: 'Invalid credentials'})
                        }
                        const processed_user = this.process_user_data(result)
                        green(`Auth success: uid=${result.uid} email=${result.user_email}`)
                        return done(null, processed_user)
                    } catch (dbError: any) {
                        red('Database auth callback error:', dbError.message)
                        return done(null, false, {message: 'Authentication failed'})
                    }
                } catch (error: any) {
                    red('LocalStrategy error:', error.message)
                    return done(null, false, {message: 'Authentication failed'})
                }
            })
        )
        this.main.POST('/api/v1/login', this.handle_ajax_login.bind(this), false)
        this.main.POST('/api/auth/login', this.handle_ajax_login.bind(this), false)
        this.main.GET('/Conta/VerificarSeExiste/:type', this.existe.bind(this), false)
        this.main.POST('/Conta/Cadastrar', this.cadastrar.bind(this), false)
        this.main.GET('/api/v1/logoff', this.sair.bind(this))
        this.main.GET('/api/v1/last-sessions', this.last_sessions.bind(this))
        this.main.POST('/api/auth/logout', this.handle_ajax_logout.bind(this))
        this.main.GET('/login', this.login.bind(this), false)
        this.main.GET('/change_user_language/:lang', this.change_user_language.bind(this))
        this.main.GET('/forgot', this.forgot.bind(this), false)
        this.main.POST('/forgot/:ts_hmac', this.forgot_save.bind(this), false)
        this.main.GET('/forgot_reset/:ts/:uid/:hash', this.forgot_reset.bind(this), false)
        this.main.GET('/api/v1/menu_pins', this.menu_pins.bind(this), false)
        this.main.POST('/api/v1/menu_pin_toggle', this.menu_pin_toggle.bind(this), false)
    }
    handle_ajax_login(req: REQ, res: express.Response) {
        passport.authenticate('local', (err: any, user: any, info: any) => {
            if (err) {
                red('Login error:', err)
                return res.status(500).json({success: false, message: 'Internal server error'})
            }
            if (!user) {
                const message = info?.message || 'Invalid credentials'
                return res.status(401).json({success: false, message})
            }
            req.logIn(user, (login_err: any) => {
                if (login_err) {
                    red('Login process error:', login_err)
                    return res.status(500).json({success: false, message: 'Login process failed'})
                }
                if (req.session) req.session.user = user
                else red('!session', req.session)
                const redirect_url = user.group_login_page || '/'
                return res.json({
                    success: true,
                    message: 'Login successful',
                    redirect_url,
                    user: this.main.get_safe_user_data(user)
                })
            })
        })(req, res)
    }
    async existe(req: REQ, res: express.Response) {
        const args = [req.site_id, req.IP, req.params.type, req.query.val]
        const r_res = await this.main.db.call('user_verify', args)
        if (r_res.is_err()) return res.json({error: r_res.error()})
        const r = r_res.gr()
        return res.json(r)
    }
    ensure_login(): express.RequestHandler {
        return (req: express.Request, res: express.Response, next: express.NextFunction) => {
            let session: any = req.session
            let user: any = req.user || {}
            if (user && user?.uid && user.uid > 0) return next()
            if (session && session.user) {
                user = session.user
                req.user = user
            }
            if (session) session.returnTo = req.originalUrl
            return res.redirect('/login')
        }
    }
    conta_validar(req: REQ, res: express.Response) {
        const token = req.body.TOKEN
        if (!token) return this.account_err(req, res, false, 'Token de verificação não informado.')
        const secret_key = req.site?.recaptcha_site_secret
        const verification_url = 'https://www.google.com/recaptcha/api/siteverify?secret=' + secret_key + '&response=' + token + '&remoteip=' + req.IP
        this.main.https_get(verification_url, (body: any, error?: string) => {
            let session = req.session ? req.session : {}
            if (!req.session) red('req.session', req.session)
            if (error) {
                red(error)
                return this.account_err(req, res, false, 'Erro durante verificação (captcha).')
            }
            let result = JSON.parse(body)
            if (result.success !== undefined && !result.success) {
                const errstr = result['error-codes'].join(', ')
                return this.account_err(req, res, false, 'Erro na verificação do cadastro (' + errstr + ').')
            }
            session.score = result.score
            yellow('req.session?.score=', req.session?.score)
            if (req.session?.score < req.site?.recaptcha_threshold)
                return this.account_err(req, res, false, 'Erro na verificação do cadastro (pontuação ' + req.session?.score + ' baixa).')
            session.captcha = result
            return this.cadastro_salvar(req, res)
        })
    }
    cadastrar(req: REQ, res: express.Response) {
        if (req.session?.score >= req.site?.recaptcha_threshold) {
            yellow('já passu pelo reCAPTCHA SCORE=' + req.session?.score)
            return this.cadastro_salvar(req, res)
        }
        if (req.site?.recaptcha_site_key3) {
            yellow('PASSANDO PELO reCAPTCHA threshold>=' + req.site?.recaptcha_threshold)
            return this.conta_validar(req, res)
        } else {
            yellow('Sem reCAPTCHA configurado.')
            return this.cadastro_salvar(req, res)
        }
    }
    async cadastro_salvar(req: REQ, res: express.Response) {
        if (req.session) req.session.returnTo = req.body.returnTo
        else red('Session not available during registration')
        const q = req.body
        const score = req.session?.score || 0
        const nome_abreviado = q.NOME_ABREVIADO?.toUpperCase() || ''
        const nome = q.NOME?.toUpperCase() || ''
        const documento = q.DOCUMENTO?.toUpperCase() || ''
        const email = q.EMAIL?.toLowerCase() || ''
        const whatsapp = q.WHATSAPP || ''
        const tel = q.TEL || ''
        const password = q.SENHA || ''
        const op = q.OP || ''
        if (!nome) return this.account_err(req, res, false, 'NOME NÃO INFORMADO')
        if (!documento) return this.account_err(req, res, false, 'DOCUMENTO NÃO INFORMADO')
        if (!email) return this.account_err(req, res, false, 'EMAIL NÃO INFORMADO')
        if (op !== 'EDITAR' && !password) return this.account_err(req, res, false, 'SENHA NÃO INFORMADA')
        const api_id = this.main.uuid()
        const api_key = this.main.encrypt(this.main.uuid())
        let reflink = req.session?.ref || req.cookies?.ref || 1
        const args = [
            req.site_id,
            req.IP,
            req.lang,
            req.country,
            req.agent,
            nome,
            email,
            password,
            tel,
            'local',
            score,
            api_id,
            api_key,
            reflink,
            documento,
            nome_abreviado,
            whatsapp
        ]
        const proc = op === 'EDITAR' ? 'user_update' : 'user_add'
        const result_r = await this.main.db.call(proc, args)
        if (result_r.is_err()) return this.account_err(req, res, false, 'ERRO AO SALVAR A CONTA')
        const result = result_r.gr()
        if (!result) return this.account_err(req, res, false, 'ERRO AO SALVAR A CONTA')
        if (result.err) return this.account_err(req, res, false, result.err)
        if (!result.uid) return this.account_err(req, res, false, 'ERRO AO SALVAR, UID INVÁLIDO')
        const ts = this.main.timestamp()
        const magic = this.hash_password(password + ts + 'daf8k32sadfhdsufhasiujfa')
        const uri = `/magic?origin=magic&ts=${ts}&h=${magic}&u=${email}`
        yellow('Registration success:', nome_abreviado, email)
        const notification_email = req.site?.email_alertas || req.site?.email_suporte || req.site?.site_email
        if (notification_email && op !== 'EDITAR')
            this.send_registration_notification(req, {
                to: notification_email,
                from: notification_email,
                reply_to: email,
                user_data: {
                    nome_abreviado,
                    nome,
                    email,
                    tel,
                    whatsapp,
                    uid: result.uid
                },
                score
            })
        if (op === 'EDITAR') return this.account_err(req, res, true, `CADASTRO ALTERADO em ${result.em}`)
        else return this.account_err(req, res, true, 'CADASTRO CONCLUÍDO.', uri)
    }
    send_registration_notification(req: REQ, params: any) {
        const {to, from, reply_to, user_data, score} = params
        const subject = `[${req.site?.id}] Nova conta ${user_data.nome_abreviado}`
        let html = '<h1>Uma nova conta foi criada</h1>'
        html += '<i>Segue informações da conta</i><hr/>'
        html += `<b>Apelido: </b>${user_data.nome_abreviado}<br/>`
        html += `<b>Nome: </b>${user_data.nome}<br/>`
        html += `<b>Email: </b>${user_data.email}<br/>`
        html += `<b>Telefone: </b>${user_data.tel}<br/>`
        html += `<b>WhatsApp: </b>${user_data.whatsapp}<br/>`
        html += `<b>IP (País): </b>${req.IP} (${req.country})<br/>`
        html += `<b>Navegador: </b>${req.agent}<br/>`
        html += `<b>Score: </b>${score}<br/>`
        html += '<hr/>'
        html += 'Favor conceder privilégios ao usuário em: '
        html += `<a href="${req.site?.site_url}/app/rh-gerar-funcionario?funcionario_login=${user_data.uid}">${req.site?.site_url}/adm</a>`
        this.main.sendmail(from, to, subject, html, '', reply_to, (error?: string) => {
            if (error) red('Registration notification error:', error)
        })
    }
    async sair(req: REQ, res: express.Response) {
        const uid = req.user?.uid
        yellow('uid', uid)
        if (!uid) return res.status(401).json({error: 'Unauthorized'})
        const r_res = await this.main.db.call('api_account_logout', [uid])
        if (r_res.is_err()) return res.status(500).json({error: r_res.error()})
        req.logout((err: any) => {
            if (err) {
                red('logout error:', err)
                return res.status(500).json({error: 'Logout failed'})
            }
            req.session?.destroy((session_err: any) => {
                if (session_err) red('session destroy error:', session_err)
                res.clearCookie('connect.sid')
                if (req.softphone) res.send("<script>window.parent.document.location='/?logout=STAGE1'</script>")
                else res.redirect('/?logout=STAGE1')
            })
        })
    }
    handle_ajax_logout(req: REQ, res: express.Response) {
        req.logout((err: any) => {
            if (err) {
                red('logout error:', err)
                return res.status(500).json({success: false, message: 'Logout failed'})
            }
            req.session?.destroy((session_err: any) => {
                if (session_err) red('session destroy error:', session_err)
                res.clearCookie('connect.sid')
                return res.json({success: true, message: 'Logout successful'})
            })
        })
    }
    async menu_pins(req: REQ, res: express.Response) {
        const oid = req.oid
        const gid = req.gid
        const uid = req.uid
        const ip = req.IP
        const r_res = await this.main.db.call('api_menu_pins', [oid, gid, uid, ip])
        if (r_res.is_err()) return res.json({error: r_res.error()})
        const r = r_res.ga()
        return res.json(r)
    }
    async menu_pin_toggle(req: REQ, res: express.Response) {
        const oid = req.oid
        const gid = req.gid
        const uid = req.uid
        const ip = req.IP
        const r_res = await this.main.db.call('api_menu_pin_toggle', [oid, gid, uid, ip, req.body.menu_id])
        if (r_res.is_err()) return res.json({error: r_res.error()})
        const r = r_res.gr()
        return res.json(r)
    }
    async last_sessions(req: REQ, res: express.Response) {
        const uid = req.user?.uid
        if (!uid) return res.status(401).json({error: 'Unauthorized'})
        const r_res = await this.main.db.call('api_account_last_sessions', [uid])
        if (r_res.is_err()) return res.json({error: r_res.error()})
        const r = r_res.ga()
        return res.json(r)
    }
    validate_magic_login(query: any) {
        const ts = parseInt(query.ts)
        const now = this.main.timestamp()
        const ttl = now - ts
        if (!ts || ts <= 0) return {valid: false, error: 'Invalid timestamp'}
        if (ttl >= 60) return {valid: false, error: 'Magic link expired'}
        return {valid: true}
    }
    process_user_data(raw_user: any) {
        const clean_user: any = {}
        for (const key in raw_user) if (raw_user[key] !== undefined && raw_user[key] !== null && key !== '') clean_user[key] = raw_user[key]
        if (raw_user.group_acl) {
            const acl_list = raw_user.group_acl.split(',').filter((acl: string) => acl && acl.trim())
            clean_user.acl = acl_list
            acl_list.forEach((acl: string) => {
                clean_user[acl] = true
            })
        }
        return clean_user
    }
    hash_password(password: any) {
        if (!password) {
            password = this.main.timestamp() + this.main.site_config.s
            yellow('!no-password-hashing-timestamp.')
        }
        return this.main.sha256(password)
    }
    login(req: REQ, res: express.Response) {
        let r: any = req.site
        r.site = req.site
        r.referrer = req.query.referrer
        r.LOGIN_MESSAGE = req.query.s
        this.main.display(req, res, 'public', 'login', r)
    }
    is_dev_user(req: REQ, res: express.Response) {
        if (req.user?.ACL_DEV) return true
        this.main.adm_err(req, res, 'req.user.ACL_DEV', 'SOMENTE PARA DESENVOLVEDOR.')
        return false
    }
    change_user_language(req: REQ, res: express.Response) {
        let callback = req.query.callback
        if (!callback) callback = '/'
        const lang = req.params.lang
        yellow('change_user_language', lang)
        this.main.set('lang', lang, req)
        return this.main.redirect(req, res, callback + '?lang=' + lang)
    }
    forgot(_req: any, res: express.Response) {
        let req: REQ = _req as unknown as REQ
        let r = req.site
        return this.main.display(req, res, 'public', 'forgot', r)
    }
    async forgot_save(_req: any, res: express.Response) {
        let self = this
        let req: REQ = _req as unknown as REQ
        const msg = 'Invalid data.'
        const q = req.body
        yellow('q', q)
        if (this.main.site_config.recaptcha_enabled) {
            const ts_hmac = req.params.ts_hmac
            if (!ts_hmac) return self.account_err(req, res, 'NO hmac', msg)
            if (ts_hmac.length < 32) return self.account_err(req, res, 'INVALID hmac LENGTH', msg)
            let rid = this.main.recaptcha_pool[ts_hmac]
            if (!rid) {
                rid = req.session?.captcha
                yellow('rid = req.session?.captcha')
            }
            if (!rid) return self.account_err(req, res, 'NO-SOLVED-CAPCTHA-IN-POOL', 'No ts_hmac in recaptcha_pool.')
            this.main.recaptcha_pool[ts_hmac] = undefined
            const ts_r = q.ts
            const ts = this.main.timestamp()
            const ttl = ts - ts_r
            if (ttl >= 300) return self.account_err(req, res, 'TTL TOO OLD.', 'Please, refresh the page.')
            const rid_r = '-' + ts_r + '.' + q.rid
            const rid_r_hmac = this.main.hmac(rid_r, this.main.site_config.recaptcha_hmac_key)
            if (ts_hmac != rid_r_hmac) return self.account_err(req, res, 'hmac NOT EQUAL', 'Invalid hmac. Please, refresh the page.')
        }
        const username = q.username.toLowerCase()
        const email = q.username
        if (!username) return self.account_err(req, res, 'INPUT ERROR', msg + ' #2')
        const args = [req.site?.site_id, email]
        const r_res = await this.main.db.call('webapp_user_by_mail', args)
        if (r_res.is_err()) return self.account_err(req, res, 'Erro', r_res.error())
        const r = r_res.gr()
        if (!r) {
            return self.account_err(req, res, 'Erro', 'Conta não existe.')
        } else if (!r.id) {
            return self.account_err(req, res, 'Erro', 'Conta não existe.')
        } else {
            const id = r.id
            const user_name = r.user_name
            const user_email = r.user_email
            const ts = this.main.timestamp()
            const hash = this.main.md5(id + ts + 'akdfajdfk29dfafadfajdfja')
            const url = req.site?.site_url + '/forgot_reset/' + ts + '/' + id + '/' + hash
            const subject = '[' + req.site?.title + '] Recuperação de senha'
            const body = `Olá <b>${r.user_name}</b>,<br/> segue o endereço para alteração da senha:<br/><a href="${url}">${url}</a><br/>Atenção: o link é válido por 1 hora.`
            const from = '<EMAIL>'
            const reply_to = req.site?.SITE_EMAIL
            this.main.sendmail(from, user_email, subject, body, '', reply_to, (error?: string) => {
                let msg = user_name + ', o endereço para recuperar a senha foi enviado para o email ' + user_email
                if (error) msg = error
                return res.send(msg)
            })
        }
    }
    async forgot_reset(req: REQ, res: express.Response) {
        let self = this
        const ts: number = Number(req.params.ts)
        const uid: number = Number(req.params.uid)
        const hash: string = req.params.hash
        const msg = 'Erro de validação.'
        if (!ts) return self.account_err(req, res, 'Falta timestamp', msg)
        if (!uid) return self.account_err(req, res, 'Falta id do usuário.', msg)
        if (hash.length < 32) return self.account_err(req, res, 'Hash de segurnaça inválido.', msg)
        const ttl = this.main.timestamp() - ts
        if (ttl >= 3600) return self.account_err(req, res, 'Link inválido, passou de 1 hora.', 'Tente recuperar a senha novamente.')
        const password = randomize('*', 10)
        const password_sha256 = this.main.sha256(password)
        const args = [req.site?.site_id, uid, password_sha256]
        const r_res = await this.main.db.call('webapp_password_reset', args)
        if (r_res.is_err()) return self.account_err(req, res, 'Erro', r_res.error())
        const r = r_res.gr()
        const user_name = r.user_name
        const user_email = r.user_email
        const from = '<EMAIL>'
        const subject = '[' + req.site?.title + '] Nova senha '
        let html = `${user_name}, <br/>sua nova senha é: <b><code style="background-color:#ffff88;">${password}</code><b> <p>Clique <a href="${req.site?.site_url}/login">aqui</a> para entrar.`
        const reply_to = req.site?.SITE_EMAIL
        this.main.sendmail(from, user_email, subject, html, '', reply_to, (error?: string) => {
            if (error) html += '<hr/>Erro no envio do email: ' + error
            return res.send('<h1>Senha alterada</h1><p>' + html + '</p>')
        })
    }
    account_err(req: REQ, res: express.Response, success: any, str: any, uri: any = '') {
        req
        yellow(str)
        let r = {success: success, message: str, uri: uri}
        return res.json(r)
    }
    async validate_email(email: any) {
        if (!isEmail(email)) return false
        const ev = new email_validator({
            timeout: 10000,
            //eslint-disable-next-line @typescript-eslint/naming-convention
            verifyMxRecords: true,
            //eslint-disable-next-line @typescript-eslint/naming-convention
            verifySmtpConnection: false
        })
        return new Promise((resolve: any) => {
            ev.verify(email)
                .then((mx: any) => {
                    yellow('mx', mx)
                    resolve(true)
                })
                .catch((err: any) => {
                    red(err)
                    resolve(false)
                })
        })
    }
}
