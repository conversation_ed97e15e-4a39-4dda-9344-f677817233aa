if (req.body.chamado_status > 0 && req.body.chamado_id > 0) {
    const page = req.body.page
    const chamado_id = req.body.chamado_id
    const chamado_status = req.body.chamado_status
    const args = [
        sid,
        gid,
        uid,
        ip,
        req.body.chamado_id,
        req.body.chamado_status,
        req.body.regulacao_consiencia ? req.body.regulacao_consiencia : 0,
        req.body.regulacao_oxigenacao ? req.body.regulacao_oxigenacao : 0,
        req.body.regulacao_pulso ? req.body.regulacao_pulso : 0,
        req.body.regulacao_exposicao ? req.body.regulacao_exposicao : 0,
        req.body.regulacao_historica_medica,
        req.body.regulacao_diagnostico,
        req.body.regulacao_acidente_de_trabalho,
        req.body.regulacao_saude_mental,
        req.body.regulacao_gravidade,
        req.body.regulacao_equipe ? req.body.regulacao_equipe : 0,
        req.body.regulacao_incidente,
        req.body.APOIO ? req.body.APOIO.join(',') : '',
        req.body.transporte_motivo ? req.body.transporte_motivo : 0,
        req.body.regulacao_apoio_equipe ? req.body.regulacao_apoio_equipe : 0
    ]
    gr(
        'api_account_regulacao_salvar_primeira',
        args,
        function (row) {
            if (row.ERR_SALVANDO_REGULACAO) {
                res.send(row.ERR_SALVANDO_REGULACAO)
            } else {
                if (req.body.call_channel) {
                    main.emit('broadcast', {event_id: 'pbxip', data: {cmd: 'hangup', id: req.body.chamado_id, CHANNEL: req.body.call_channel}})
                }
                return tr.redirect(req, res, '/app/' + page)
            }
        },
        true
    )
} else {
    ga('api_account_regulacao_gravidade', [sid, gid, uid, ip], function (gravidades) {
        const id = req.query.id
        const force_open = req.query.f ? req.query.f : 0
        gr('api_account_chamado_abre_primeira_regulacao', [sid, gid, uid, ip, id, force_open], function (chamado) {
            ga('api_account_regulacao_recurso_apoio', [sid, gid, uid, ip], function (apoio) {
                ga('api_account_regulacao_decisoes', [sid, gid, uid, ip, 1], function (decisoes) {
                    r.chamado = chamado
                    r.apoio = apoio
                    r.gravidades = gravidades
                    r.decisoes = decisoes
                    if (chamado.ERRO_ABERTURA_CHAMADO) {
                        return res.send(chamado.ERRO_ABERTURA_CHAMADO)
                    }
                    ga('api_account_chamado_observacoes', [sid, gid, uid, ip, id], function (observacoes) {
                        r.observacoes = observacoes
                        return tr.display_adm(req, res, automake_name, r)
                    })
                })
            })
        })
    })
}
