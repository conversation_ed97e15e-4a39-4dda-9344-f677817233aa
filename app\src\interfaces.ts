import express from 'express'
import fileUpload from 'express-fileupload'
interface USER {
    uid: number
    acl: string[]
    ACL_DEV?: boolean
    ACL_ADMIN?: boolean
    [key: string]: any
}
//@ts-ignore
export interface REQ extends express.Request {
    user?: USER
    site?: {[key: string]: any}
    global?: {[key: string]: any}
    gid?: number
    uid?: number
    sid?: number
    acl?: string[]
    files?: fileUpload.FileArray
    upload_path?: any
    upload_url?: any
    oid?: number
    site_id?: number
    session?: any
    IP?: string
    country?: string
    country_id?: number
    lang?: string
    agent?: string
    softphone?: string
    UUID?: string
    _form_app?: string
    _form_on_submit?: string
}
