const user_app = req.query.app

if (!user_app) return res.send('<script>alert("erro: sem aplicativo vinculado (falta param app na url).");</script>')

gr('api_account_ramal_por_usuario', [oid, gid, uid, req.IP], function (ramal) {
    r.ramal = ramal
    r.user_app = user_app
    if (!ramal.numero) return res.send('<script>alert("erro: sem ramal vinculado, abrindo terminal sem telefonia.");document.location.href="/app/' + user_app + '";</script>')
    return tr.display_adm(req, res, automake_name, r)
})
