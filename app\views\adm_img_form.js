r.cat = req.query.cat || 0
r.img_id = req.query.img_id || 0
if (req.query.op == 'image') {
    if (!req.files) return res.json({err: 'upload: arquivo não enviado.'})
    const img_size = parseInt(req.query.img_size)
    const image_folder = parseInt(req.query.image_folder)
    const img_quality = 100
    const bucket = req.site.upload_site_images
    return main.upload_and_resize(req.files.IMAGEM, req, img_size, img_quality, bucket, image_folder, function (err, img_data) {
        if (err) {
            io.red(img_data.err)
            return res.json(img_data)
        }
        console.log('img_data', img_data)
        return res.json(img_data)
    })
} else if (req.body.image_id) {
    const img_name = g('img_name')
    const image_folder = g('image_folder')
    let args = [oid, gid, uid, ip, g('image_id')]
    args.push(img_name)
    args.push(image_folder)
    gr(
        'api_adm_img_save',
        args,
        function (r) {
            return tr.redirect(req, res, '/app/adm_img_list?name=' + img_name + '&cat=' + image_folder)
        },
        true
    )
} else {
    gr(
        'api_adm_img_by_id',
        [oid, gid, uid, ip, r.img_id],
        function (img) {
            r.COPIANDO = req.query.COPIANDO > 0 ? 1 : 0
            r.cb = req.query.cb
            console.log(img)
            r.r = img
            return tr.display_adm(req, res, automake_name, r)
        },
        true
    )
}
