function exibir_erro(req, res, msg) {
    var rr = {}
    rr.message = msg
    rr.success = 0
    return res.json(rr)
}
function validar_agendamento(req, res) {
    const token = req.body.TOKEN
    if (!token) {
        return exibir_erro(req, res, 'C&#243;digo de verificação do cadastro inválido')
    }
    const secretKey = req.site.recaptcha_site_secret
    const verificationUrl = 'https://www.google.com/recaptcha/api/siteverify?secret=' + secretKey + '&response=' + token + '&remoteip=' + req.IP
    main.https_get(verificationUrl, function (body, response, error) {
        if (error) {
            console.log(error)
            return exibir_erro(req, res, 'Erro durante verificação (captcha).')
        }
        let result = JSON.parse(body)
        //console.log('result', result);
        if (result.success !== undefined && !result.success) {
            const errstr = result['error-codes'].join(', ')
            return exibir_erro(req, res, 'Erro na verificação do agendamento (' + errstr + ').')
        }
        if (!result.score || result.score < 0.5) {
            return exibir_erro(req, res, 'Erro na verificação do agendamento (pontuação ' + req.session.score + ' baixa).')
        }
        return salvar_agendamento(req, res, result.score)
    })
}
function salvar_agendamento(req, res, score) {
    let dn_moment = moment({year: req.body.idade_ano, month: req.body.idade_mes, day: req.body.idade_dia})
    let dn = dn_moment.format('YYYY-MM-DD')
    if (!dn_moment.isValid()) dn = '0000-00-00'

    let args = []
    args.push(req.site.site_id)
    args.push(req.gid)
    args.push(req.uid)
    args.push(req.IP)
    args.push(req.body.uuid)
    args.push(req.body.apelido)
    args.push(req.body.sexo)
    args.push(req.body.nome)
    args.push(dn)
    args.push(req.body.religiao)
    args.push(req.body.email)
    args.push(req.body.whatsapp)
    args.push(req.body.celular)
    args.push(req.body.endereco)
    args.push(req.body.bairro)
    args.push(req.body.cidade)
    args.push(req.body.cep)
    args.push(req.body.conjuge)
    args.push(req.body.conjuge_profissao)
    args.push(req.body.conjuge_tel)
    args.push(req.body.pai)
    args.push(req.body.pai_profissao)
    args.push(req.body.pai_tel)
    args.push(req.body.mae)
    args.push(req.body.mae_profissao)
    args.push(req.body.mae_tel)
    args.push(req.body.transporte)
    args.push(req.body.escolaridade)
    args.push(req.body.cargo)
    args.push(req.body.afinidade)
    args.push(req.body.objetivo)
    args.push(req.body.aspectos)
    args.push(req.body.dificuldade)
    args.push(req.body.desafio)
    args.push(req.body.talento)
    args.push(req.body.trabalho_interno_externo)
    args.push(req.body.caso_covid)
    args.push(req.body.doenca_cronica)
    args.push(req.body.problema_treinamento)

    gr(
        'api_public_cv_save',
        args,
        function (row) {
            return res.json(row)
        },
        true
    )
}

if (req.body.email) {
    return validar_agendamento(req, res)
} else {
    r.uuid = req.query.uuid || ''
    if (req.query.uuid) {
        gr(
            'api_public_cv_by_uuid',
            [req.site_id, req.gid, req.uid, req.query.uuid],
            function (row) {
                r.cv = row
                console.log(row)
                return tr.display_adm(req, res, automake_name, r)
            },
            true
        )
    } else {
        r.cv = {}

        return tr.display_adm(req, res, automake_name, r)
    }
}
