<?xml version="1.0" standalone="yes"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="-50 0 200 100">
  <g id="qrcode"/>
  <foreignObject x="-50" y="0" width="100" height="100">
    <body xmlns="http://www.w3.org/1999/xhtml" style="padding:0; margin:0">
      <div style="padding:inherit; margin:inherit; height:100%">
        <textarea id="text" style="height:100%; width:100%; position:absolute; margin:inherit; padding:inherit">james</textarea>
      </div>
      <script type="application/ecmascript" src="qrcode.js"></script>
      <script type="application/ecmascript">
var elem = document.getElementById("qrcode");
var qrcode = new QRCode(elem, {
    width : 100,
    height : 100
});

function makeCode () {
    var elText = document.getElementById("text");
 
    if (elText.value === "") {
        //alert("Input a text");
        //elText.focus();
        return;
    }
 
    qrcode.makeCode(elText.value);
}

makeCode();

document.getElementById("text").onkeyup = function (e) {
        makeCode();
};
</script>
    </body>
  </foreignObject>
</svg>
