const set_status = req.query.set_status ? req.query.set_status : 0
const id = req.query.id ? req.query.id : 0
if (req.body.OBSERVACAO && req.body.id) {
    const args = [sid, gid, uid, ip, req.body.id, req.body.OBSERVACAO]
    gr(
        'api_account_chamado_observacao_salvar',
        args,
        function (row) {
            return tr.redirect(req, res, '/app/chamado_observacoes?id=' + req.body.id)
        },
        true
    )
} else if (set_status > 0) {
    const args = [sid, gid, uid, ip, req.query.id, set_status]
    gr(
        'api_account_transferencia_set_status',
        args,
        function (row) {
            return tr.redirect(req, res, '/app/chamado_observacoes?id=' + req.query.id)
        },
        true
    )
} else {
    gr('api_account_transferencia_chamado_por_id', [sid, gid, uid, ip, id], function (chamado) {
        r.chamado = chamado
        ga('api_account_chamado_observacoes', [sid, gid, uid, ip, id], function (observacoes) {
            r.id = id
            r.observacoes = observacoes
            return tr.display_adm(req, res, automake_name, r)
        })
    })
}
