import colors from 'colors'
import express from 'express'
import mysql2, {ConnectionOptions} from 'mysql2/promise'
import {at, yellow} from './colors'
export type DB_ERR = {error: string}
export type ROW = Record<string, any>
export type ROWS = ROW[]
export type DB_VALUE = string | number | boolean | null | undefined
type DB_TYPES = 'ROWS' | 'ROW' | 'STR' | 'ERROR'
export class DB_RES {
    private _type: DB_TYPES = 'ROWS'
    private _error: string = ''
    private _rows: ROWS = []
    private _str: string = ''
    private _args: any[] = []
    constructor(type: DB_TYPES, error: string, rows: ROWS, str: string, args: any[], res?: express.Response) {
        this._type = type
        this._error = error
        this._rows = rows
        this._str = str
        this._args = args
        if (res)
            if (this.is_err())
                res.json({
                    success: false,
                    error: this._error
                })
            else res.json(this.ga())
    }
    debug() {
        yellow(colors.bold.yellow(this._str))
        if (this._args.length > 0) yellow(colors.bold.yellow(JSON.stringify(this._args, null, 2)))
    }
    type(): DB_TYPES {
        return this._type
    }
    error(): string {
        return this._error
    }
    is_err(): boolean {
        return this._error !== ''
    }
    is_ok(): boolean {
        return this._error === ''
    }
    ga(): ROWS {
        if (this.is_err()) throw new Error(this._error)
        return this._rows
    }
    gr(): ROW {
        if (this.is_err()) throw new Error(this._error)
        return this._rows[0]
    }
    go(): DB_VALUE {
        if (this.is_err()) throw new Error(this._error)
        const row = this._rows[0]
        if (!row) return ''
        const first_key = Object.keys(row)[0]
        return row[first_key]
    }
}
export default class DB {
    pool: mysql2.Pool
    pool_info: string = ''
    name: string = ''
    config: ConnectionOptions = {}
    constructor(name: string, config: ConnectionOptions, callback?: (error?: string) => void) {
        this.name = name
        if (!config.host) throw new Error('❌ NO HOST PROVIDED')
        if (!config.port) throw new Error('❌ NO PORT PROVIDED')
        if (!config.user) throw new Error('❌ NO USER PROVIDED')
        if (!config.password) throw new Error('❌ NO PASSWORD PROVIDED')
        if (!config.database) throw new Error('❌ NO DATABASE PROVIDED')
        this.config = config
        this.pool = mysql2.createPool(this.config)
        try {
            this.init(callback)
        } catch (e: any) {
            const message = e.message || 'Unknown error'
            this.red(message)
            if (callback) callback(message)
        }
    }
    private async init(callback?: (error?: string) => void) {
        try {
            const r: any = await this.pool.query('SELECT VERSION() as version, @@server_id as server_id, @@have_ssl as have_ssl')
            const info = r[0][0]
            this.pool_info = `mysql-v${info.version}://${this.config.user}@${this.config.host}:${this.config.port}/${this.config.database} id:${info.server_id}`
            if (info.have_ssl === 'YES' && this.config.ssl)
                this.pool.query('SHOW STATUS LIKE "Ssl_version"', (ssl_err: any, ssl_results: any) => {
                    if (!ssl_err && ssl_results.length > 0) this.pool_info += colors.green(` ssl:${ssl_results[0].Value}`)
                    else this.pool_info += colors.red(' ssl:disabled-error')
                })
            else if (info.have_ssl === 'YES' && !this.config.ssl) this.pool_info += colors.red(' ssl:disabled')
            else this.pool_info += colors.red(' ssl:disabled')
            this.gray(this.pool_info)
            if (callback) callback()
        } catch (e: any) {
            const message = e.message || 'Unknown error'
            this.red(message)
            if (callback) callback(message)
        }
    }
    logger(bg: colors.Color = colors.red, ...args: any[]) {
        const lines: string[] = []
        for (const arg of args)
            if (arg instanceof Object) lines.push(JSON.stringify(arg, null, 2))
            else lines.push(arg?.toString())
        const str = lines.join(' ').trim()
        this.bg_red(`${at()} ${colors.bold.white(this.name)}\n${bg(str)}\n\n`)
    }
    bg_red(...args: any[]) {
        this.logger(colors.bgRed, ...args)
    }
    red(...args: any[]) {
        this.logger(colors.red, ...args)
    }
    green(...args: any[]) {
        this.logger(colors.green, ...args)
    }
    yellow(...args: any[]) {
        this.logger(colors.yellow, ...args)
    }
    bg_yellow(...args: any[]) {
        this.logger(colors.bgYellow, ...args)
    }
    blue(...args: any[]) {
        this.logger(colors.blue, ...args)
    }
    magenta(...args: any[]) {
        this.logger(colors.magenta, ...args)
    }
    cyan(...args: any[]) {
        this.logger(colors.cyan, ...args)
    }
    white(...args: any[]) {
        this.logger(colors.white, ...args)
    }
    gray(...args: any[]) {
        this.logger(colors.gray, ...args)
    }
    mysql_security(str: string) {
        const match = /(\w+)\.(\w+)|(\w+)/gm.exec(str)
        if (!match) {
            this.red('procedure', str)
            this.gray('match', match)
            this.red(`SECURITY ERROR: ${str}`)
            return false
        }
        return true
    }
    async sql(str: string, _args: any[] = [], res?: express.Response): Promise<DB_RES> {
        if (!str) {
            this.red('❌ NO SQL QUERY PROVIDED')
            return new DB_RES('ERROR', 'NO SQL', [], str, _args, res)
        }
        const args = _args.map(arg => {
            if (arg === null || arg === undefined || (typeof arg === 'number' && isNaN(arg))) return 'NULL'
            return arg
        })
        try {
            let res: any = await this.pool.query(str, args)
            let rows: any = res[0]
            const is_procedure = rows && Array.isArray(rows) && rows.length > 1 && rows[1] && typeof rows[1] === 'object' && 'fieldCount' in rows[1]
            if (is_procedure) rows = rows[0]
            return new DB_RES('ROWS', '', rows, str, args, res)
        } catch (e: any) {
            const error = e.message
            this.bg_red(error)
            this.yellow(colors.bold.yellow(str))
            if (args.length > 0) this.yellow(colors.bold.yellow(JSON.stringify(args, null, 2)))
            return new DB_RES('ERROR', error, [], str, args)
        }
    }
    call(procedure: string, args: any[] = [], res?: express.Response): Promise<DB_RES> {
        let l = []
        for (let i in args) {
            let v = args[i]
            if (v === null || v === undefined || (typeof v === 'number' && isNaN(v))) l.push('NULL')
            else l.push(this.pool.escape(v))
        }
        const str = 'CALL ' + procedure + '(' + l.join(', ') + ')'
        return this.sql(str, [], res)
    }
}
