const id = req.query.id > 0 ? req.query.id : 0
const setor = req.query.setor > 0 ? req.query.setor : 14
let ds = req.query.ds
let de = req.query.de
if (!ds) ds = moment(new Date()).subtract(2, 'month').format('YYYY-MM-DD')
if (!de) de = moment(new Date()).format('YYYY-MM-DD')
ds = moment(ds).format('YYYY-MM-DD')
de = moment(de).format('YYYY-MM-DD')

r.setor = setor
r.ds = ds
r.de = de

if (req.query.op == 'parcelas') {
    const pagamento_id = req.query.pagamento_id || 0
    if (pagamento_id) {
        ga('financeiro.api_account_parcelas', [oid, gid, uid, req.IP, pagamento_id], function (parcelas) {
            const q = parcelas.length
            let lines = [`<input type="hidden" id="parcelas_total" name="parcelas_total" value="${q}"/>`]
            for (let i in parcelas) {
                const v = parcelas[i].valor
                const em = parcelas[i].prazo
                const baixa_ok = parcelas[i].baixa_ok
                const pago = baixa_ok ? '<span class="badge badge-success">Pago</span>' : '<span class="badge badge-warning">Aberto</span>'
                lines.push(parcela_template(i, em, v, pago, parcelas[i].parcela_id))
            }
            return res.send(lines.join('\n'))
        })
    } else {
        const q = parseInt(req.query.q || 1)
        const v = parseFloat(req.query.v || 0)
        let d = moment(req.query.d || new Date())
        let lines = [`<input type="hidden" id="parcelas_total" name="parcelas_total" value="${q}"/>`]
        for (let i = 0; i < q; i++) {
            const em = d.format('YYYY-MM-DD')
            const p = i + 1
            d = moment(d).add(1, 'month')
            lines.push(parcela_template(i, em, v, '', 0))
        }
        return res.send(lines.join('\n'))
    }
} else {
    gr('financeiro.api_account_pagamento', [oid, gid, uid, req.IP, id], function (pagamento) {
        //console.log('pagamento', pagamento);
        r.pagamento = pagamento
        r.hoje = moment(new Date()).format('YYYY-MM-DD')
        return tr.display_adm(req, res, automake_name, r)
    })
}

function parcela_template(i, em, v, pago, parcela_id) {
    const j = parseInt(i) + 1
    return `
        <input type="hidden" id="parcela_id_${i}" name="parcela_id[${i}]" class="shadow-sm form-control" value="${parcela_id}"/>
        <div class="form-row" id="pr${i}">
            <div class="form-group col-md-6">
              <label for="vencimento_data_${i}"><i class="fas fa-calendar-alt"></i> Vencimento* #${j}</label>
              <input required type="date" id="vencimento_data_${i}" name="vencimento_data[${i}]" class="shadow-sm form-control" value="${em}"
                onchange="atualizar_parcela(${i}, ${parcela_id})">
            </div>
            <div class="form-group col-md-5">
              <label for="vencimento_valor_${i}"><i class="fas fa-money-check-alt"></i> Valor* ${pago}</label>
              <input required type="text" id="vencimento_valor_${i}" name="vencimento_valor[${i}]" class="shadow-sm form-control moeda" value="${v}"
                onchange="atualizar_parcela(${i},${parcela_id})">
            </div>
            <div class="form-group col-md-1">
              <label for="vencimento_rm_${i}"> </label>
              <button type="button" class="btn btn-sm form-control" onclick="rm_parcela(${i},${parcela_id})" id="vencimento_rm_${i}"
                ><i class="fas fa-trash"></i></button>
            </div>
        </div>        
        `
}
