let ds = req.query.ds
let de = req.query.de
if (!ds) ds = moment(new Date()).subtract(2, 'days').format('YYYY-MM-DD')
if (!de) de = moment(new Date()).format('YYYY-MM-DD')
ds = moment(ds).format('YYYY-MM-DD')
de = moment(de).format('YYYY-MM-DD')
r.ds = ds
r.de = de

const cidade = req.query.cidade ? req.query.cidade : 0
const bairro = req.query.bairro ? req.query.bairro : 0
const evento = req.query.evento ? req.query.evento : 0

ga('api_account_chamados_regulador', [sid, gid, uid, ip, cidade, bairro, evento], function (tree) {
    gr('api_account_estado_cidade_padrao', [sid, gid, uid, ip], function (padrao) {
        r.padrao = padrao
        r.tree = tree
        return tr.display_adm(req, res, automake_name, r)
    })
})
