const app_title = 'Usuários'
const app_desc = 'Gerenciamento de usuários.'
r.app_title = app_title
r.app_desc = app_desc
r.op = req.query.op

if (req.query.op == 'save') {
    let user_password = req.body.password
    if (user_password) {
        user_password = io.sha256(user_password)
    } else {
        user_password = ''
    }
    let args = []
    args.push(req.sid)
    args.push(req.gid)
    args.push(req.uid)
    args.push(req.IP)
    args.push(req.query.id)
    args.push(req.body.user_name)
    args.push(req.body.user_email)
    args.push(user_password)
    args.push(req.body.user_tel)
    args.push(req.body.user_country ? req.body.user_country : 0)
    args.push(req.body.user_lang ? req.body.user_lang : 0)
    args.push(req.body.user_group ? req.body.user_group : 0)
    args.push(req.body.user_status ? 1 : 0)
    gr(
        'adm_users_save',
        args,
        function (tree) {
            if (tree.err) {
                return res.send(tree.err)
            }
            //return res.send('ok');
            return redirect(req, res, '/app/adm_users?id=' + req.query.id)
        },
        true
    )
} else if (req.query.op == 'status') {
    const args = [req.site.site_id, req.gid, req.uid, req.IP, req.query.id, req.query.s, req.query.g]
    gr(
        'adm_users_status',
        args,
        function (tree) {
            return res.json(tree)
        },
        true
    )
} else if (req.query.op == 'edit') {
    gr(
        'adm_users',
        [req.site_id, req.IP, req.gid, req.uid, req.query.id],
        function (tree) {
            r.tree = tree
            console.log('tree', tree)
            return tr.display_adm(req, res, automake_name, r)
        },
        true
    )
} else {
    ga(
        'adm_users',
        [req.site_id, req.IP, req.gid, req.uid, 0],
        function (tree) {
            r.tree = tree
            ga('api_groups', [req.site_id, req.IP, req.gid, req.uid], function (tree) {
                r.grupos = tree
                return tr.display_adm(req, res, automake_name, r)
            })
        },
        true
    )
}
