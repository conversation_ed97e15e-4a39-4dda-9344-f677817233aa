const id = req.query.id
const audio = req.query.audio
const force_open = 1
if (audio) {
    if (req.user.ACL_DEV || req.user.MEDICO || req.user.GERENTE || req.user.RADIO_OPERACAO || req.user.ENFERMAGEM) {
        gr('api_account_chamado_audio', [sid, gid, uid, ip, audio], function (row) {
            return res.download(`${row.file}.mp3`)
        })
    } else {
        res.send('Sem permissão de acesso ao áudio.')
    }
} else {
    gr('api_account_chamado_abre_ultima_regulacao', [sid, gid, uid, ip, id, force_open], function (chamado) {
        ga('api_account_regulacoes_por_chamado', [sid, gid, uid, ip, id], function (regulacoes) {
            r.chamado = chamado
            r.regulacoes = regulacoes
            r.ultima_regulacao = regulacoes[regulacoes.length - 1]
            ga('api_account_chamado_observacoes', [sid, gid, uid, ip, id], function (observacoes) {
                r.observacoes = observacoes
                ga('api_account_chamado_audios', [sid, gid, uid, ip, id], function (audios) {
                    r.audios = audios
                    return tr.display_adm(req, res, automake_name, r)
                })
            })
        })
    })
}
