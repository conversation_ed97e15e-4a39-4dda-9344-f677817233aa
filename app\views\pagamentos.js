r.PAGAMENTO_ID = req.query.PAGAMENTO_ID || 0
r.fornecedor = req.query.fornecedor || 0
r.categoria = req.query.categoria || 0
r.pagas = req.query.pagas || 0
r.setor = req.query.setor || 0
r.q = req.query.q || ''
r.cliente = req.query.cliente || 0
let ds = req.query.ds
let de = req.query.de
if (!ds) ds = moment(new Date()).subtract(2, 'month').format('YYYY-MM-DD')
if (!de) de = moment(new Date()).format('YYYY-MM-DD')
ds = moment(ds).format('YYYY-MM-DD')
de = moment(de).format('YYYY-MM-DD')
r.ds = ds
r.de = de

const args = [req.oid, req.gid, req.uid, req.IP, r.pagas, r.categoria, r.fornecedor, r.q, ds, de, r.cliente]
//return res.json(args);
ga(
    'financeiro.api_account_financeiro_pagamentos',
    args,
    function (tree) {
        r.tree = tree
        return tr.display_adm(req, res, automake_name, r)
    },
    true
)
