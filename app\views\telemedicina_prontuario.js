// DECLARAÇ&#213;ES DAS FUNÇ&#213;ES

// VERIFICO SE O ID VEIO DA PÁGINA ANTERIOR

const iniciaInterface = function () {
    carregaAtendimentoId()
}

// CASO VENHA O ID, EU CARREGO OS DADOS USANDO O ID DO ANTEIMENTO
// CASO NÃO TENHA ID, CARREGO O ÚLTIMO (ACABAMOS DE ADICIONAR UM NOVO ATENDIMENTO)

const carregaAtendimentoId = function () {
    ga('telemedicina.api_verifica_atendimento_usuario', [sid, gid, uid, ip, req.query.id], function (atendimento) {
        r.tree = atendimento
        return tr.display_adm(req, res, automake_name, r)
    })
}

iniciaInterface()
