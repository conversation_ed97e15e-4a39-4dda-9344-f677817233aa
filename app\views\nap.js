let ds = req.query.ds
let de = req.query.de
if (!ds) ds = moment(new Date()).subtract(2, 'days').format('YYYY-MM-DD')
if (!de) de = moment(new Date()).format('YYYY-MM-DD')
ds = moment(ds).format('YYYY-MM-DD')
de = moment(de).format('YYYY-MM-DD')
r.ds = ds
r.de = de

let args = [sid, gid, uid, ip, ds, de]

args.push(req.query.SOLICITANTE_NOME ? req.query.SOLICITANTE_NOME : '')
args.push(req.query.SOLICITANTE_TELEFONE ? req.query.SOLICITANTE_TELEFONE : '')
args.push(req.query.PACIENTE_NOME ? req.query.PACIENTE_NOME : '')
args.push(req.query.CIDADE ? req.query.CIDADE : 0)
args.push(req.query.BAIRRO ? req.query.BAIRRO : 0)
args.push(req.query.chamado_id ? req.query.chamado_id : 0)

ga(
    'api_account_nap_lista',
    args,
    function (tree) {
        r.tree = tree
        return tr.display_adm(req, res, automake_name, r)
    },
    true
)
