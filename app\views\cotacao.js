if (req.query.op == 'salvar') {
    const pedido_id = req.body.pedido_id
    const para = req.body.para
    const cotacao_uuid = req.body.cotacao_uuid
    const assunto = req.body.assunto
    const mensagem = req.body.mensagem
    const from = req.site.site_email
    let args = [oid, gid, uid, req.IP, pedido_id, cotacao_uuid, para, assunto, mensagem]
    gr('financeiro.api_account_financeiro_cotacao_salvar', args, function (row) {
        io.sendmail(from, para, assunto, mensagem, '', from, function (err) {
            let tree = {success: true}
            if (err) {
                tree = {success: false, message: err.message}
            }
            return res.json(tree)
        })
    })
} else {
    gr('financeiro.api_public_financeiro_contacao_por_uuid', [req.query.id, req.IP], function (pedido) {
        r.pedido = pedido
        return tr.display_adm(req, res, automake_name, r)
    })
}
