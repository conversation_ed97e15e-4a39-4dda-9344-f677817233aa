function exibir_erro(req, res, msg) {
    var rr = {}
    rr.message = msg
    rr.success = 0
    return res.json(rr)
}
function validar_mensagem(req, res) {
    const token = req.body.TOKEN
    if (!token) {
        return exibir_erro(req, res, 'C&#243;digo de verificação do cadastro inválido')
    }
    const secretKey = req.site.recaptcha_site_secret
    const verificationUrl = 'https://www.google.com/recaptcha/api/siteverify?secret=' + secretKey + '&response=' + token + '&remoteip=' + req.IP
    main.https_get(verificationUrl, function (body, response, error) {
        if (error) {
            console.log(error)
            return exibir_erro(req, res, 'Erro durante verificação (captcha).')
        }
        let result = JSON.parse(body)
        //console.log('result', result);
        if (result.success !== undefined && !result.success) {
            const errstr = result['error-codes'].join(', ')
            return exibir_erro(req, res, 'Erro na verificação da mensagem (' + errstr + ').')
        }
        if (!result.score || result.score < 0.5) {
            return exibir_erro(req, res, 'Erro na verificação do mensagem (pontuação ' + req.session.score + ' baixa).')
        }
        return enviar_mensagem(req, res, result.score)
    })
}
function enviar_mensagem(req, res, score) {
    let to = req.site.site_email
    if (!to) to = req.site.email_suporte
    if (!to) to = req.site.email_alertas

    const subject = '[' + req.site.id + '] contato de ' + req.body.apelido
    let html = '<p>' + req.body.msg + '</p>'
    html += '<hr/>'
    if (req.body.apelido) html += '<b>Nome: </b>' + req.body.apelido + '<br/>'
    if (req.body.email) html += '<b>E-mail: </b><a href="mailto:' + req.body.email + '">' + req.body.email + '</a><br/>'
    if (req.body.tel) html += '<b>Telefone: </b>' + req.body.tel + '<br/>'
    if (req.body.tel) html += '<b>Abrir no WhatsApp: </b>' + tr.whatsapp(req.body.tel) + '<br/>'
    const text = ''
    io.sendmail(to, to, subject, html, '', req.body.email, function (err) {
        if (err) {
            return res.json({success: false, message: err.message})
        }
        return res.json({success: true, message: 'Mensagem envaida para ' + to})
    })
}

if (req.body.email) {
    return validar_mensagem(req, res)
} else {
    return tr.display_adm(req, res, automake_name, r)
}
