import crypto from 'crypto'
import ejs from 'ejs'
import express from 'express'
import fs from 'fs'
import {encode} from 'html-entities'
import {ConnectionOptions} from 'mysql2'
import path from 'path'
import {v4 as uuidv4} from 'uuid'
import am_mysql from './am_mysql'
import {bg_cyan, bg_green, bg_red, bg_yellow, red, yellow} from './colors'
import DB, {DB_RES, ROW, ROWS} from './db'
import {REQ} from './interfaces'
import Main from './main'
const connectors: {[key: number]: am_mysql} = {}
type MENU = {
    label: string
    url: string
    icone: string
}
type FIELD = {
    field_id: number
    field_automake_id: number
    field_label: string
    field_name: string
    field_order: number
    field_show_on_list: string[]
    field_show_on_form: string[]
    field_type: string
    field_required: number
    select_query: string
    field_default_value: string
    field_select_db: string
    field_select_label: string
    field_select_table: string
    field_select_pk: string
    field_config: any
    field_max_len: number
    field_help: string
    field_select_where: string
    field_select_alias: string
    field_ignore_empty: number
    field_display_password: number
    field_no_display_value: number
    field_read_only: number
    field_tab: string
    field_col: number
    field_row_link: string
    field_alias: string
    field_css_list: string
    field_css_form: string
    field_list_sql: string
    field_list_filter: number
    config_data: {[key: string]: string}
}
type VALID_ACTION = 'error' | 'list' | 'total' | 'add' | 'edit' | 'delete' | 'combo' | 'save_field' | 'get_field'
type PERMISSIONS = 'CAN_LIST' | 'CAN_VIEW' | 'CAN_ADD' | 'CAN_EDIT' | 'CAN_DELETE'
type DTD_ACTIONS = 'ds_exec_sql' | 'databases' | 'tables' | 'fields'
type APP_CONFIG_DATA = {
    error?: string
    db: am_mysql
    pk: number
    page: number
    limit: number
    query: any
    tree: any
    app_config: any
    CAN_LIST: boolean
    CAN_VIEW: boolean
    CAN_ADD: boolean
    CAN_EDIT: boolean
    CAN_DELETE: boolean
    request: REQ
    response: express.Response
    sid: number
    gid: number
    uid: number
    ip: string
    action: VALID_ACTION
    menus: MENU[]
    q: string
    automake_id: number
    automake_name: string
    automake_table: string
    automake_search: string[]
    automake_pk: string
    automake_desc_list: string
    automake_status: boolean
    automake_enabled_add: string
    automake_connector: number
    automake_extra_url: string
    automake_list_data: string
    automake_view_data: string
    automake_delete_data: string
    automake_save_data: string
    automake_lambda_data: string
    automake_enabled_edit: string
    automake_db: string
    automake_chroot_site: string
    automake_order_by: string
    automake_site_id: number
    automake_enabled_del: string
    automake_ts: string
    automake_uid: string
    automake_gid: string
    automake_chroot_type: number
    automake_limit: number
    automake_owner_uid: number
    automake_desc_form: string
    automake_owner_gid: number
    automake_enabled_list: string
    automake_ico: string
    automake_pk_uuid: number
    automake_menu_extra: string
    fields_by_name: {[column: string]: FIELD}
    fields_by_tab_and_col: {[tab_name: string]: {[col: number]: FIELD[]}}
    fields: FIELD[]
    tabs_orders: {[tab_name: string]: number}
    connector_id: number
    connector_type: string
    connector_host: string
    connector_user: string
    connector_port: number
    connector_database: string
    connector_charset: string
    connector_cert_file: string
    connector_cert_key: string
    connector_password: string
    connector_cert_ca: string
    connector_tz: string
    connector_port_dev: number
    connector_site_id: string
    connector_name: string
    connector_port_dev2: number
}
class APP_CONFIG implements APP_CONFIG_DATA {
    private _error?: string
    get is_error(): boolean {
        return !!this._error
    }
    get error(): string | undefined {
        return this._error
    }
    set error(error: string | undefined) {
        this._error = error
    }
    private readonly EMAIL_PATTERN = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
    private readonly URL_PATTERN = /^https?:\/\/([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/
    private readonly IPV4_PATTERN = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/
    private readonly IPV6_PATTERN = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/
    private readonly PHONE_PATTERN = /^[+]?[1-9][\d]{0,15}$/
    private readonly UUID_PATTERN = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
    private readonly HEX_COLOR_PATTERN = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/
    private readonly SLUG_PATTERN = /^[a-z0-9]+(?:-[a-z0-9]+)*$/
    //@ts-ignore
    db: am_mysql
    pk: number = 0
    page: number = 1
    limit: number = 100
    query: any = {}
    tree: ROWS = []
    app_config: any = {}
    CAN_LIST: boolean = false
    CAN_VIEW: boolean = false
    CAN_ADD: boolean = false
    CAN_EDIT: boolean = false
    CAN_DELETE: boolean = false
    request: REQ
    response: express.Response
    sid: number = 0
    gid: number = 0
    uid: number
    ip: string
    action: VALID_ACTION = 'error'
    menus: MENU[] = []
    q: string
    automake_id: number = 0
    automake_name: string = ''
    automake_table: string = ''
    automake_search: string[] = []
    automake_pk: string = ''
    automake_desc_list: string = ''
    automake_status: boolean = false
    automake_enabled_add: string = ''
    automake_connector: number = 0
    automake_extra_url: string = ''
    automake_list_data: string = ''
    automake_view_data: string = ''
    automake_delete_data: string = ''
    automake_save_data: string = ''
    automake_lambda_data: string = ''
    automake_enabled_edit: string = ''
    automake_db: string = ''
    automake_chroot_site: string = ''
    automake_order_by: string = ''
    automake_site_id: number = 0
    automake_enabled_del: string = ''
    automake_ts: string = ''
    automake_uid: string = ''
    automake_gid: string = ''
    automake_chroot_type: number = 0
    automake_limit: number = 0
    automake_owner_uid: number = 0
    automake_desc_form: string = ''
    automake_owner_gid: number = 0
    automake_enabled_list: string = ''
    automake_enabled_view: string = ''
    automake_ico: string = ''
    automake_pk_uuid: number = 0
    automake_menu_extra: string = ''
    connector_id: number = 0
    connector_type: string = ''
    connector_host: string = ''
    connector_user: string = ''
    connector_port: number = 0
    connector_database: string = ''
    connector_charset: string = ''
    connector_cert_file: string = ''
    connector_cert_key: string = ''
    connector_password: string = ''
    connector_cert_ca: string = ''
    connector_tz: string = ''
    connector_port_dev: number = 0
    connector_site_id: string = ''
    connector_name: string = ''
    connector_port_dev2: number = 0
    fields_by_name: {[column: string]: FIELD} = {}
    fields_by_tab_and_col: {[tab_name: string]: {[col: number]: FIELD[]}} = {}
    fields: FIELD[] = []
    tabs_orders: {[tab_name: string]: number} = {}
    main: Main
    constructor(main: Main, request: REQ, response: express.Response, automake_id: number | string) {
        this.main = main
        this.request = request
        this.response = response
        this.sid = request.sid || 0
        this.gid = request.gid || 0
        this.uid = request.uid || 0
        this.ip = request.IP || ''
        this.q = request.query.q || request.body.q || ''
        this.page = parseInt(request.query.page as string) || 1
        this.limit = parseInt(request.query.limit as string)
        this.query = request.query || request.body || {}
        this.automake_id = parseInt(automake_id as string) || 0
        if (!this.automake_id) this.set_error('❌ NO AUTOMAKE ID PROVIDED')
    }
    permissions(permission: PERMISSIONS): {status: boolean; message?: string} {
        let message = ''
        if (permission == 'CAN_LIST')
            if (!this.CAN_LIST) {
                const my_acl = this.request.user?.acl?.join(', ') || 'nenhuma permissão'
                const need_acl = this.automake_enabled_list || 'nenhuma permissão necessária'
                message = 'Acesso negado para listar. Você tem ' + my_acl + '. Você precisa de ' + need_acl + ' para acessar esta aplicação.'
                return {status: false, message: message}
            }
        if (permission == 'CAN_VIEW')
            if (!this.CAN_VIEW) {
                const my_acl = this.request.user?.acl?.join(', ') || 'nenhuma permissão'
                const need_acl = this.automake_enabled_view || 'nenhuma permissão necessária'
                message = 'Acesso negado para visualizar. Você tem ' + my_acl + '. Você precisa de ' + need_acl + ' para acessar esta aplicação.'
                return {status: false, message: message}
            }
        if (permission == 'CAN_ADD')
            if (!this.CAN_ADD) {
                const my_acl = this.request.user?.acl?.join(', ') || 'nenhuma permissão'
                const need_acl = this.automake_enabled_add || 'nenhuma permissão necessária'
                message = 'Acesso negado para adicionar. Você tem ' + my_acl + '. Você precisa de ' + need_acl + ' para acessar esta aplicação.'
                return {status: false, message: message}
            }
        if (permission == 'CAN_EDIT')
            if (!this.CAN_EDIT) {
                const my_acl = this.request.user?.acl?.join(', ') || 'nenhuma permissão'
                const need_acl = this.automake_enabled_edit || 'nenhuma permissão necessária'
                message = 'Acesso negado para editar. Você tem ' + my_acl + '. Você precisa de ' + need_acl + ' para acessar esta aplicação.'
                return {status: false, message: message}
            }
        if (permission == 'CAN_DELETE')
            if (!this.CAN_DELETE) {
                const my_acl = this.request.user?.acl?.join(', ') || 'nenhuma permissão'
                const need_acl = this.automake_enabled_del || 'nenhuma permissão necessária'
                message = 'Acesso negado para deletar. Você tem ' + my_acl + '. Você precisa de ' + need_acl + ' para acessar esta aplicação.'
                return {status: false, message: message}
            }
        return {status: true}
    }
    async init(action: VALID_ACTION): Promise<boolean> {
        if (this.is_error) return false
        const app_config_res = await this.db.call('automake', [this.sid, this.gid, this.uid, this.ip, this.automake_id])
        if (app_config_res.is_err()) return this.set_error(app_config_res.error())
        const r = app_config_res.gr()
        this.db = connectors[r.automake_connector]
        this.CAN_LIST = this.acl(r.automake_enabled_list)
        this.CAN_VIEW = this.acl(r.automake_enabled_list)
        this.CAN_ADD = this.acl(r.automake_enabled_add)
        this.CAN_EDIT = this.acl(r.automake_enabled_edit)
        this.CAN_DELETE = this.acl(r.automake_enabled_del)
        if (r.automake_menu_extra) {
            this.menus = []
            const lista = r.automake_menu_extra.trim().split('\n')
            for (let i in lista) {
                const menu = lista[i].trim().split(',')
                const icone = menu[2] ? menu[2] : 'fas fa-align-justify'
                const r: MENU = {label: menu[0], url: menu[1], icone: icone}
                this.menus.push(r)
            }
        }
        const fields_data_res = await this.db.call('automake_field', [this.uid, this.ip, this.automake_id])
        if (fields_data_res.is_err()) return this.set_error(fields_data_res.error())
        const fields_data = fields_data_res.ga()
        if (fields_data.length < 1) return this.set_error('Aplicação sem campos.')
        for (let f in fields_data) {
            const row: any = fields_data[f]
            let field: FIELD = row
            if (row.field_alias) field.field_name = row.field_alias
            let config: any = row.field_config
            const acl_ok = !row.field_show_on_form || this.acl(row.field_show_on_form)
            if (!acl_ok) continue
            if (config)
                if (config.indexOf('=') == -1) {
                    field.config_data = config.split(',')
                } else {
                    config = config.split(',')
                    let config_kv: any = {}
                    for (let ci in config) {
                        let v = config[ci]
                        if (v && v.indexOf('=') != -1) {
                            v = v.split('=')
                            config_kv[v[0]] = v[1]
                        }
                    }
                    field.config_data = config_kv
                }
            const tab_name = field.field_tab
            const col_index = field.field_col
            const fields_by_tab_and_col = this.fields_by_tab_and_col[tab_name] || {}
            const fields_by_col_index = fields_by_tab_and_col[col_index] || []
            fields_by_col_index.push(field)
            fields_by_tab_and_col[col_index] = fields_by_col_index
            this.fields_by_tab_and_col[tab_name] = fields_by_tab_and_col
            this.fields.push(field)
            const field_name = field.field_name
            this.fields_by_name[field_name] = field
        }
        const automake_connector = this.automake_connector
        const connector_type = this.connector_type.toLowerCase()
        const pk = this.request.params.id || this.request.body.primary_key || 0
        const log_args = [this.sid, this.gid, this.uid, this.ip, action, this.automake_id, pk]
        let connector = connectors[automake_connector]
        if (!connector) {
            const db_config = {
                user: this.connector_user,
                password: this.connector_password,
                database: this.connector_database,
                host: this.connector_host,
                port: this.connector_port,
                //eslint-disable-next-line @typescript-eslint/naming-convention
                decimalNumbers: true,
                //eslint-disable-next-line @typescript-eslint/naming-convention
                supportBigNumbers: true,
                //eslint-disable-next-line @typescript-eslint/naming-convention
                bigNumberStrings: true,
                //eslint-disable-next-line @typescript-eslint/naming-convention
                multipleStatements: false,
                //eslint-disable-next-line @typescript-eslint/naming-convention
                enableKeepAlive: true,
                //eslint-disable-next-line @typescript-eslint/naming-convention
                keepAliveInitialDelay: 10,
                //eslint-disable-next-line @typescript-eslint/naming-convention
                connectTimeout: 1000,
                timezone: this.connector_tz,
                charset: this.connector_charset,
                ssl: this.connector_cert_file
            }
            if (connector_type == 'mysql')
                if (
                    this.db.config.user == db_config.user &&
                    this.db.config.password == db_config.password &&
                    this.db.config.database == db_config.database &&
                    this.db.config.host == db_config.host &&
                    this.db.config.port == db_config.port
                )
                    connector = new am_mysql(this.main.db, this.automake_connector, db_config, (error?: string) => {
                        if (error) throw new Error(error)
                    })
                else
                    connector = new am_mysql(undefined, this.automake_connector, db_config, (error?: string) => {
                        if (error) throw new Error(error)
                    })
            else return this.set_error('CONECTOR INVÁLIDO: ' + connector_type)
            connectors[automake_connector] = connector
        }
        this.db.call('automake_log', log_args)
        this.limit = parseInt(this.request.query.limit as string) || r.automake_limit
        this.db = connector
        this.action = action
        this.automake_id = parseInt(r.automake_id as string) || 0
        this.automake_name = r.automake_name
        this.automake_table = r.automake_table
        this.automake_search = r.automake_search || []
        this.automake_pk = r.automake_pk
        this.automake_desc_list = r.automake_desc_list
        this.automake_status = r.automake_status == 1
        this.automake_enabled_add = r.automake_enabled_add
        this.automake_connector = r.automake_connector
        this.automake_extra_url = r.automake_extra_url
        this.automake_list_data = r.automake_list_data
        this.automake_view_data = r.automake_view_data
        this.automake_delete_data = r.automake_delete_data
        this.automake_save_data = r.automake_save_data
        this.automake_lambda_data = r.automake_lambda_data
        this.automake_enabled_edit = r.automake_enabled_edit
        this.automake_db = r.automake_db
        this.automake_chroot_site = r.automake_chroot_site
        this.automake_order_by = r.automake_order_by
        this.automake_site_id = r.automake_site_id
        this.automake_enabled_del = r.automake_enabled_del
        this.automake_ts = r.automake_ts
        this.automake_uid = r.automake_uid
        this.automake_gid = r.automake_gid
        this.automake_chroot_type = r.automake_chroot_type
        this.automake_limit = r.automake_limit
        this.automake_owner_uid = r.automake_owner_uid
        this.automake_desc_form = r.automake_desc_form
        this.automake_owner_gid = r.automake_owner_gid
        this.automake_enabled_list = r.automake_enabled_list
        //TODO: add supporte to view.
        this.automake_enabled_view = r.automake_enabled_list
        this.automake_ico = r.automake_ico
        this.automake_pk_uuid = r.automake_pk_uuid
        this.automake_menu_extra = r.automake_menu_extra
        this.connector_id = r.connector_id
        this.connector_type = r.connector_type
        this.connector_host = r.connector_host
        this.connector_user = r.connector_user
        this.connector_port = r.connector_port
        this.connector_database = r.connector_database
        this.connector_charset = r.connector_charset
        this.connector_cert_file = r.connector_cert_file
        this.connector_cert_key = r.connector_cert_key
        this.connector_password = r.connector_password
        this.connector_cert_ca = r.connector_cert_ca
        this.connector_tz = r.connector_tz
        this.connector_port_dev = r.connector_port_dev
        this.connector_site_id = r.connector_site_id
        this.connector_name = r.connector_name
        this.connector_port_dev2 = r.connector_port_dev2
        if (!this.automake_id) return this.set_error('❌ NO AUTOMAKE ID PROVIDED')
        if (!this.automake_name) return this.set_error('❌ NO AUTOMAKE NAME PROVIDED')
        if (!this.automake_table) return this.set_error('❌ NO AUTOMAKE TABLE PROVIDED')
        if (!this.automake_pk) return this.set_error('❌ NO AUTOMAKE PK PROVIDED')
        return true
    }
    acl(need_acl: string[] | string): boolean {
        const arr: string[] = Array.isArray(need_acl) ? need_acl : [need_acl]
        const ok: boolean = this.main.acl_check(this.request.user?.acl, arr)
        if (ok) return true
        const group_name = this.request.user?.group_name || 'não autenticado'
        const user_name = this.request.user?.user_name || 'não autenticado'
        const message = `[${group_name}/${user_name}] NÃO AUTORIZADO: PRICISA DE: [${arr.join(', ')}]`
        const you_have = `TEM A PERMISSÃO: ${this.request.user?.acl?.join(', ') || 'nenhuma permissão'}`
        red(message)
        red(you_have)
        return false
    }
    set_error(error: string): boolean {
        red(error)
        this.error = error
        return false
    }
    async sql(q: string): Promise<any> {
        return this.db.exec(q)
    }
    async call(proc_id: string, _args: any[] = []): Promise<DB_RES> {
        return this.db.call(proc_id, _args)
    }
    format_table(db: string, table: string): string {
        return this.db.format_table(db, table)
    }
    field_esc(v: string): string {
        return this.db.field_esc(v)
    }
    esc(v: any): string {
        return this.db.esc(v)
    }
    private validate_email(email: string): boolean {
        if (!email || typeof email !== 'string') return false
        return this.EMAIL_PATTERN.test(email.trim())
    }
    private validate_url(url: string): boolean {
        if (!url || typeof url !== 'string') return false
        return this.URL_PATTERN.test(url.trim())
    }
    private validate_ip_address(ip: string): boolean {
        if (!ip || typeof ip !== 'string') return false
        return this.IPV4_PATTERN.test(ip.trim()) || this.IPV6_PATTERN.test(ip.trim())
    }
    private validate_phone(phone: string): boolean {
        if (!phone || typeof phone !== 'string') return false
        const cleaned = phone.replace(/[\s\-\(\)]/g, '')
        return this.PHONE_PATTERN.test(cleaned)
    }
    private validate_uuid(uuid: string): boolean {
        if (!uuid || typeof uuid !== 'string') return false
        return this.UUID_PATTERN.test(uuid.trim())
    }
    private validate_hex_color(color: string): boolean {
        if (!color || typeof color !== 'string') return false
        return this.HEX_COLOR_PATTERN.test(color.trim())
    }
    private validate_slug(slug: string): boolean {
        if (!slug || typeof slug !== 'string') return false
        return this.SLUG_PATTERN.test(slug.trim())
    }
    private validate_date(date_str: string): boolean {
        if (!date_str || typeof date_str !== 'string') return false
        const date = new Date(date_str)
        return !isNaN(date.getTime()) && date.toISOString().slice(0, 10) === date_str
    }
    private validate_date_time(date_time_str: string): boolean {
        if (!date_time_str || typeof date_time_str !== 'string') return false
        const date = new Date(date_time_str)
        return !isNaN(date.getTime())
    }
    private validate_json(json_str: string): boolean {
        if (!json_str || typeof json_str !== 'string') return false
        try {
            JSON.parse(json_str)
            return true
        } catch {
            return false
        }
    }
    private validate_numeric_range(value: number, min?: number, max?: number): boolean {
        if (typeof value !== 'number' || isNaN(value)) return false
        if (min !== undefined && value < min) return false
        if (max !== undefined && value > max) return false
        return true
    }
    private validate_string_length(str: string, min_len?: number, max_len?: number): boolean {
        if (typeof str !== 'string') return false
        if (min_len !== undefined && str.length < min_len) return false
        if (max_len !== undefined && str.length > max_len) return false
        return true
    }
    private validate_file_extension(filename: string, allowed_extensions: string[]): boolean {
        if (!filename || typeof filename !== 'string') return false
        const ext = filename.toLowerCase().split('.').pop()
        return ext ? allowed_extensions.includes(ext) : false
    }
    private validate_mime_type(mime_type: string, allowed_types: string[]): boolean {
        if (!mime_type || typeof mime_type !== 'string') return false
        return allowed_types.includes(mime_type.toLowerCase())
    }
    private sanitize_html(html: string): string {
        if (!html || typeof html !== 'string') return ''
        return html
            .replace(/<script[^>]*>.*?<\/script>/gi, '')
            .replace(/<iframe[^>]*>.*?<\/iframe>/gi, '')
            .replace(/javascript:/gi, '')
            .replace(/on\w+\s*=/gi, '')
            .replace(/<object[^>]*>.*?<\/object>/gi, '')
            .replace(/<embed[^>]*>/gi, '')
            .replace(/<link[^>]*>/gi, '')
            .replace(/<meta[^>]*>/gi, '')
    }
    private validate_password(password: string, min_length = 8): boolean {
        if (!password || typeof password !== 'string') return false
        if (password.length < min_length) return false
        const has_upper_case = /[A-Z]/.test(password)
        const has_lower_case = /[a-z]/.test(password)
        const has_numbers = /\d/.test(password)
        const has_special_char = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)
        return has_upper_case && has_lower_case && has_numbers && has_special_char
    }
    private validate_credit_card(card_number: string): boolean {
        if (!card_number || typeof card_number !== 'string') return false
        const cleaned = card_number.replace(/\s/g, '')
        if (!/^\d+$/.test(cleaned)) return false
        let sum = 0
        let is_even = false
        for (let i = cleaned.length - 1; i >= 0; i--) {
            let digit = parseInt(cleaned.charAt(i), 10)
            if (is_even) {
                digit *= 2
                if (digit > 9) digit -= 9
            }
            sum += digit
            is_even = !is_even
        }
        return sum % 10 === 0
    }
    private validate_field_value(value: any, field_config: any): {valid: boolean; error?: string} {
        const type = field_config.type
        const required = parseInt(field_config.required || 0)
        const max_len = parseInt(field_config.field_max_len || 0)
        const min_len = parseInt(field_config.field_min_len || 0)
        const pattern = field_config.field_pattern
        const config = field_config.config_data
        if (required > 0 && (value === null || value === undefined || value === '')) return {valid: false, error: `Campo ${field_config.label} é obrigatório`}
        if (!value && required === 0) return {valid: true}
        switch (type) {
            case 'email':
                if (!this.validate_email(value)) return {valid: false, error: `${field_config.label} deve ser um email válido`}
                break
            case 'url':
                if (!this.validate_url(value)) return {valid: false, error: `${field_config.label} deve ser uma URL válida`}
                break
            case 'ip':
                if (!this.validate_ip_address(value)) return {valid: false, error: `${field_config.label} deve ser um endereço IP válido`}
                break
            case 'phone':
                if (!this.validate_phone(value)) return {valid: false, error: `${field_config.label} deve ser um telefone válido`}
                break
            case 'uuid':
                if (!this.validate_uuid(value)) return {valid: false, error: `${field_config.label} deve ser um UUID válido`}
                break
            case 'color':
                if (!this.validate_hex_color(value)) return {valid: false, error: `${field_config.label} deve ser uma cor hexadecimal válida`}
                break
            case 'slug':
                if (!this.validate_slug(value)) return {valid: false, error: `${field_config.label} deve ser um slug válido (apenas letras minúsculas, números e hífens)`}
                break
            case 'date':
                if (!this.validate_date(value)) return {valid: false, error: `${field_config.label} deve ser uma data válida (YYYY-MM-DD)`}
                break
            case 'datetime':
                if (!this.validate_date_time(value)) return {valid: false, error: `${field_config.label} deve ser uma data/hora válida`}
                break
            case 'json':
                if (!this.validate_json(value)) return {valid: false, error: `${field_config.label} deve ser um JSON válido`}
                break
            case 'password':
                {
                    const min_password_len = config?.min_length || 8
                    if (!this.validate_password(value, min_password_len))
                        return {
                            valid: false,
                            error: `${field_config.label} deve ter pelo menos ${min_password_len} caracteres com maiúscula, minúscula, número e caractere especial`
                        }
                }
                break
            case 'creditcard':
                if (!this.validate_credit_card(value)) return {valid: false, error: `${field_config.label} deve ser um número de cartão de crédito válido`}
                break
            case 'int':
            case 'number':
            case 'float':
            case 'double':
            case 'bigint':
                {
                    const num_val = parseFloat(value)
                    if (isNaN(num_val)) return {valid: false, error: `${field_config.label} deve ser um número válido`}
                    const min_val = config?.min_value
                    const max_val = config?.max_value
                    if (!this.validate_numeric_range(num_val, min_val, max_val))
                        return {valid: false, error: `${field_config.label} deve estar entre ${min_val || '-∞'} e ${max_val || '+∞'}`}
                }
                break
            case 'text':
            case 'textarea':
            case 'varchar':
                if (typeof value === 'string') {
                    if (!this.validate_string_length(value, min_len, max_len))
                        return {valid: false, error: `${field_config.label} deve ter entre ${min_len || 0} e ${max_len || '∞'} caracteres`}
                    if (pattern)
                        try {
                            const regex = new RegExp(pattern)
                            if (!regex.test(value)) return {valid: false, error: `${field_config.label} não atende ao padrão exigido`}
                        } catch (e: any) {
                            red('Invalid regex pattern:', pattern, e.message)
                        }
                    if (type === 'textarea' && config?.allow_html !== true) {
                        const sanitized = this.sanitize_html(value)
                        if (sanitized !== value) return {valid: false, error: `${field_config.label} contém HTML não permitido`}
                    }
                }
                break
            case 'file':
                if (config?.allowed_extensions) {
                    const extensions = config.allowed_extensions.split(',').map((ext: string) => ext.trim())
                    if (!this.validate_file_extension(value.name || value, extensions))
                        return {valid: false, error: `${field_config.label} deve ter uma das extensões: ${extensions.join(', ')}`}
                }
                if (config?.allowed_mime_types) {
                    const mime_types = config.allowed_mime_types.split(',').map((type: string) => type.trim())
                    if (!this.validate_mime_type(value.type || '', mime_types))
                        return {valid: false, error: `${field_config.label} deve ser um dos tipos: ${mime_types.join(', ')}`}
                }
                if (config?.max_file_size && value.size > config.max_file_size)
                    return {valid: false, error: `${field_config.label} excede o tamanho máximo de ${config.max_file_size} bytes`}
                break
        }
        return {valid: true}
    }
    private sanitize_input(input: any, type: string): any {
        if (input === null || input === undefined) return input
        switch (type) {
            case 'string':
            case 'text':
            case 'textarea':
                if (typeof input === 'string')
                    return input
                        .trim()
                        .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '')
                        .replace(/\0/g, '')
                break
            case 'html':
                if (typeof input === 'string') return this.sanitize_html(input)
                break
            case 'number':
            case 'int':
            case 'float': {
                const num = parseFloat(input)
                return isNaN(num) ? 0 : num
            }
            case 'boolean':
                return Boolean(input)
            case 'array':
                if (Array.isArray(input)) return input.map(item => this.sanitize_input(item, 'string'))
                return []
            case 'json':
                if (typeof input === 'string')
                    try {
                        return JSON.parse(input)
                    } catch {
                        return null
                    }
                return input
        }
        return input
    }
    private validate_file_upload(file: any): {valid: boolean; error?: string} {
        if (!file) return {valid: true}
        const max_size = 10 * 1024 * 1024
        if (file.size > max_size) return {valid: false, error: 'Arquivo muito grande. Máximo 10MB permitido.'}
        const dangerous_extensions = ['exe', 'bat', 'cmd', 'com', 'pif', 'scr', 'vbs', 'js', 'jar', 'php', 'asp', 'aspx', 'jsp', 'py', 'rb', 'pl', 'sh', 'ps1']
        const filename = file.name || file.originalname || ''
        const extension = filename.toLowerCase().split('.').pop()
        if (extension && dangerous_extensions.includes(extension)) return {valid: false, error: 'Tipo de arquivo não permitido por questões de segurança.'}
        const mime_type = file.mimetype || file.type || ''
        if (this.is_suspicious_mime_type(mime_type, extension)) return {valid: false, error: 'Tipo MIME suspeito detectado.'}
        return {valid: true}
    }
    private is_suspicious_mime_type(mime_type: string, extension: string): boolean {
        const common_mime_types: {[key: string]: string[]} = {
            jpg: ['image/jpeg'],
            jpeg: ['image/jpeg'],
            png: ['image/png'],
            gif: ['image/gif'],
            pdf: ['application/pdf'],
            doc: ['application/msword'],
            docx: ['application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
            xls: ['application/vnd.ms-excel'],
            xlsx: ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
            txt: ['text/plain'],
            csv: ['text/csv', 'application/csv']
        }
        if (!extension || !mime_type) return false
        const expected_mimes = common_mime_types[extension]
        if (expected_mimes && !expected_mimes.includes(mime_type)) return true
        return false
    }
    broadcast_data_change(data: any) {
        const r = {event_id: 'rds_data_change', data: data}
        yellow('ds_broadcast', r)
        this.main.emit('ds_broadcast', r)
    }
    aes_decrypt_data(result: ROWS): ROWS {
        if (!result || result.length === 0) return result
        const fields: string[] = Object.keys(result[0])
        const aes_fields: string[] = []
        for (const field of fields) if (field.indexOf('pre_aes:') !== -1) aes_fields.push(field)
        if (aes_fields.length > 0) for (let i = 0; i < result.length; i++) result[i] = this.aes_decrypt_data_row(result[i], aes_fields)
        return result
    }
    aes_decrypt_data_row(result: ROW, aes_fields: string[] = []): ROW {
        if (aes_fields.length === 0) {
            const fields: string[] = Object.keys(result)
            for (const field of fields) if (field.indexOf('pre_aes:') !== -1) aes_fields.push(field)
        }
        for (const field of aes_fields) if (result[field]) result[field] = this.main.decrypt(result[field])
        return result
    }
    replace_all(target: string, search: string, replacement: string): string {
        return target.split(search).join(replacement)
    }
    am_var_parse(req: REQ, app: APP_CONFIG | undefined, q: string, extra_object?: any) {
        if (!q) return ''
        if (req.uid) q = this.replace_all(q, '${uid}', req.uid ? req.uid.toString() : '0')
        if (req.gid) q = this.replace_all(q, '${gid}', req.gid ? req.gid.toString() : '0')
        if (req.sid) q = this.replace_all(q, '${sid}', req.sid ? req.sid.toString() : '0')
        if (req.IP) q = this.replace_all(q, '${ip}', req.IP ? req.IP : '')
        for (let i in req.site) {
            const k = '${' + i + '}'
            const v = req.site[i]
            q = this.replace_all(q, k, v)
        }
        for (let i of Object.keys(app || {})) {
            const k = '${' + i + '}'
            const v = (app as any)[i]
            q = this.replace_all(q, k, v)
        }
        if (extra_object)
            for (let i in extra_object) {
                const k = '${' + i + '}'
                const v = extra_object[i]
                q = this.replace_all(q, k, v)
            }
        return q
    }
    async combo(): Promise<any> {
        let field: FIELD | undefined
        const id = parseInt(this.request.params.id)
        const name = this.request.params.name
        for (let i in this.fields) {
            const l = this.fields[i]
            if (l.field_id == id || l.field_name == name) {
                field = this.fields[i]
                break
            }
        }
        if (!field) {
            this.error = 'Campo id=' + id + ' não localizado.'
            return {status: false, message: this.error}
        }
        let s = field.select_query
        if (!s) {
            let q = ['select']
            let alias = field.field_select_alias
            if (!alias) alias = field.field_select_table
            let f_id = this.field_esc(alias) + '.' + this.field_esc(field.field_select_pk)
            let f_txt = this.field_esc(alias) + '.' + this.field_esc(field.field_select_label)
            if (field.field_select_label.indexOf('(') || field.field_select_label.toLowerCase().indexOf('select ') != -1) f_txt = field.field_select_label
            q.push(f_id + ' as id, ')
            q.push(f_txt + ' as txt ')
            q.push('FROM ' + this.format_table(field.field_select_db, field.field_select_table) + ' as ' + this.field_esc(alias))
            if (field.field_select_where) q.push('where ' + field.field_select_where)
            s = q.join('\n')
            s = 'select id, txt from (\n' + s + '\n) main order by txt asc'
        }
        s = this.am_var_parse(this.request, this, s)
        let query_type = 'select'
        if (s.toLowerCase().indexOf('call ') != -1 || s.toLowerCase().indexOf('exec ') != -1) query_type = 'call'
        const tree_res = await this.db.exec(s)
        if (tree_res.is_err()) {
            this.error = tree_res.error()
            return {status: false, message: this.error}
        }
        const tree: ROWS = this.aes_decrypt_data(tree_res.ga())
        let row: any = [{id: '', txt: ''}]
        if (query_type == 'call') row = tree
        if (field.field_required) row = tree
        else row = row.concat(tree)
        return {status: true, message: 'Combo carregado com sucesso', result: row}
    }
    async field_query() {
        const id = parseInt(this.request.params.id)
        let field: FIELD | undefined
        for (let i in this.fields) {
            const l = this.fields[i]
            if (l.field_id == id) {
                field = this.fields[i]
                break
            }
        }
        if (!field) {
            this.error = 'Campo id=' + id + ' não localizado.'
            return {status: false, message: this.error}
        }
        let s = field.select_query
        if (!s) {
            this.error = 'Campo id=' + id + ' não possui consulta.'
            return {status: false, message: this.error}
        }
        s = this.am_var_parse(this.request, this, s)
        yellow('//REVIEW', s)
        let result = await this.db.exec(s)
        let row = result && result.ga().length > 0 ? result.ga()[0] : null
        if (result.is_err()) {
            this.error = result.error()
            return {status: false, message: this.error}
        }
        let val = ''
        if (row && row.str) val = row.str
        else
            for (let i in row) {
                val = row[i]
                break
            }
        return val
    }
    async add(): Promise<any> {
        if (this.is_error) {
            red(this.error)
            return
        }
        return {}
    }
    async edit(): Promise<ROW | undefined> {
        if (this.is_error) {
            red(this.error)
            return
        }
        const sid = this.request.sid
        const d = this.db
        let q: string[] = []
        let q_fields: string[] = []
        let q_joins: string[] = []
        q.push('SELECT ')
        if (d.is_sqlsrv()) q.push('TOP 1 ')
        for (let i in this.fields) {
            const r = this.fields[i]
            if (!this.acl(r.field_show_on_form)) continue
            const field = r.field_name
            const field_name = r.field_label
            const type = r.field_type
            const no_display_value = r.field_no_display_value
            const type_info = ' /* ' + r.field_id + ', ' + type + ', ' + field_name + ' */ '
            let field_sql = ''
            let alias = r.field_name
            if (r.field_alias) alias = r.field_alias
            const alias_esc = d.field_esc(alias)
            if (r.field_select_pk) {
                const select_db = r.field_select_db
                const select_table = r.field_select_table
                const select_label = r.field_select_label
                const select_pk = r.field_select_pk
                let select_alias = r.field_select_alias || r.field_select_table
                if (!select_alias) select_alias = select_table
                let ss = ' LEFT JOIN ' + d.field_esc(select_db) + '.' + d.field_esc(select_table)
                ss += ' AS ' + d.field_esc(select_alias) + ' on ' + d.field_esc(select_alias) + '.' + d.field_esc(select_pk)
                ss += ' = ' + d.field_esc(this.automake_table) + '.' + d.field_esc(field)
                if (r.field_select_where) ss += ' and (' + r.field_select_where + ') '
                q_joins.push(ss)
                if (type == 'select-modal') {
                    const label_name = d.field_esc(field + '_label')
                    const label_id = d.field_esc(select_alias) + '.' + d.field_esc(select_label)
                    field_sql += ' ' + label_id + ' AS ' + label_name + ', '
                    field_sql += d.field_esc(this.automake_table) + '.' + d.field_esc(field) + ' as ' + d.field_esc(field)
                } else {
                    field_sql += d.field_esc(this.automake_table) + '.' + d.field_esc(field)
                    field_sql += ' AS ' + alias_esc
                }
            } else {
                if (no_display_value) field_sql += "/* no_display_value */ '' AS " + alias_esc
                else field_sql += d.field_esc(this.automake_table) + '.' + d.field_esc(field) + ' AS ' + alias_esc
            }
            field_sql += type_info
            q_fields.push(field_sql)
        }
        q.push(q_fields.join(',\n'))
        q.push('FROM ' + d.format_table(this.automake_db, this.automake_table))
        q.push(q_joins.join('\n'))
        const pk_value = d.esc(this.request.params.id)
        const table = this.automake_table
        let where: string[] = []
        where.push(d.field_esc(this.automake_pk) + ' = ' + pk_value)
        const chroot_site = this.automake_chroot_site
        const automake_gid = this.automake_gid
        const automake_uid = this.automake_uid
        const chroot_type = this.automake_chroot_type
        if (chroot_type == 1 && chroot_site) {
            const f1 = d.field_esc(table) + '.' + d.field_esc(chroot_site)
            where.push(f1 + ' IN (' + d.esc(sid) + ',0)')
        } else if (chroot_type == 2 && automake_gid) {
            const f1 = d.field_esc(table) + '.' + d.field_esc(automake_gid)
            where.push(f1 + ' IN (' + d.esc(automake_gid) + ',0)')
        } else if (chroot_type == 3 && automake_uid) {
            const f1 = d.field_esc(table) + '.' + d.field_esc(chroot_site)
            where.push(f1 + ' IN (' + d.esc(automake_uid) + ',0)')
        }
        if (where.length > 0) q.push('WHERE ' + where.join(' and\n'))
        if (d.is_mysql()) q.push('LIMIT 0,1')
        const str = this.am_var_parse(this.request, this, q.join('\n'))
        const data_res = await d.exec(str)
        if (data_res.is_err()) {
            red(data_res.error())
            return
        }
        const data: ROW = this.aes_decrypt_data_row(data_res.gr())
        if (!data) return
        return data
    }
    async save(): Promise<any> {
        if (this.is_error) {
            red(this.error)
            return
        }
        const pk = this.request.body.primary_key
        let q = []
        let q_names = []
        let q_values = []
        let q_fields = []
        const automake_pk = this.automake_pk
        const automake_pk_uuid = this.automake_pk_uuid
        const pk_id = this.main.uuid()
        if (this.db.is_mysql()) {
            const tbl = this.db.field_esc(this.automake_db) + '.' + this.db.field_esc(this.automake_table)
            if (pk) {
                q.push('UPDATE LOW_PRIORITY IGNORE ' + tbl + ' SET ')
            } else {
                q.push('INSERT LOW_PRIORITY IGNORE INTO ' + tbl + ' SET ')
                if (automake_pk_uuid) {
                    q_names.push(this.db.field_esc(automake_pk))
                    q_fields.push(this.db.field_esc(automake_pk) + ' = ' + this.db.esc(pk_id))
                    q_values.push(this.db.esc(pk_id))
                }
            }
        } else {
            let tbl = this.db.field_esc(this.automake_db) + '.' + this.db.field_esc(this.automake_table)
            if (this.db.is_mssql()) tbl = this.db.field_esc(this.automake_db) + '.dbo.' + this.db.field_esc(this.automake_table)
            if (pk) {
                q.push('UPDATE ' + tbl + ' ')
            } else {
                q.push('INSERT INTO ' + tbl + ' ')
                if (automake_pk_uuid) {
                    q_names.push(this.db.field_esc(automake_pk))
                    q_fields.push(this.db.field_esc(automake_pk) + ' = ' + this.db.esc(pk_id))
                    q_values.push(this.db.esc(pk_id))
                }
            }
        }
        const chroot_site = this.automake_chroot_site
        const automake_gid = this.automake_gid
        const automake_uid = this.automake_uid
        const automake_ts = this.automake_ts
        if (!pk) {
            if (automake_gid) {
                q_names.push(this.db.field_esc(automake_gid))
                q_fields.push(this.db.field_esc(automake_gid) + ' = ' + this.db.esc(this.request.gid))
                q_values.push(this.db.esc(this.request.gid))
            }
            if (automake_uid) {
                q_names.push(this.db.field_esc(automake_uid))
                q_fields.push(this.db.field_esc(automake_uid) + ' = ' + this.db.esc(this.request.uid))
                q_values.push(this.db.esc(this.request.uid))
            }
            if (automake_ts) {
                q_names.push(this.db.field_esc(automake_ts))
                const now = new Date().toISOString()
                q_fields.push(this.db.field_esc(automake_ts) + ' = ' + this.db.esc(now))
                q_values.push(this.db.esc(now))
            }
            if (chroot_site) {
                q_names.push(this.db.field_esc(chroot_site))
                q_fields.push(this.db.field_esc(chroot_site) + ' = ' + this.db.esc(this.request.sid))
                q_values.push(this.db.esc(this.request.sid))
            }
        }
        const numbers = ['int', 'float', 'double', 'number', 'bigint', 'bool', 'tinyint', 'boolean']
        for (let i = 0; i < this.fields.length; i++) {
            const r = this.fields[i]
            const field = r.field_name
            const field_name = r.field_name
            const label = r.field_label
            let val = this.request.body[field]
            const type = r.field_type
            const ignore_empty = r.field_ignore_empty == 1
            const v_type = typeof val
            if (r.field_show_on_form && !this.acl(r.field_show_on_form)) continue
            if (r.field_read_only) continue
            const validation = this.validate_field_value(val, r)
            if (!validation.valid) {
                this.error = validation.error || `Valor inválido para o campo ${field}`
                return
            }
            if (ignore_empty && !val) continue
            if (typeof val === 'string' && val.length > 100000) {
                this.error = `Erro, ${label} muito longo.`
                return
            }
            if (Array.isArray(val)) {
                if (val.length > 1000) {
                    this.error = `Erro, ${label} tem muitos itens.`
                    return
                }
                for (const item of val)
                    if (typeof item === 'string' && item.length > 1000) {
                        this.error = `Erro, ${label} contém item muito longo.`
                        return
                    }
            }
            if (type === 'file' && val) {
                const file_validation = this.validate_file_upload(val)
                if (!file_validation.valid) {
                    this.error = file_validation.error || 'Arquivo inválido'
                    return
                }
            }
            val = this.sanitize_input(val, type)
            if (type == 'sha256') val = this.main.sha256(val)
            else if (type == 'sha512') val = this.main.sha512(val)
            let val_str
            if (v_type == 'string') {
                val_str = this.db.esc(val)
            } else if (type == 'multicoma') {
                val = val || []
                val_str = this.db.esc(val.join(','))
            } else if (type == 'select-multi') {
                val = val || []
                val_str = this.db.esc(val.join(','))
            } else if (v_type == 'boolean') {
                val = val ? 1 : 0
                val_str = val
            } else if (numbers.indexOf(v_type) > -1) {
                if (!val) val = 0
                val_str = val
            } else if (Array.isArray(val)) {
                val_str = this.db.esc(val.join(','))
            } else if (v_type == 'object') {
                val_str = this.db.esc(JSON.stringify(val))
            } else {
                val_str = this.db.esc(val)
            }
            q_names.push(this.db.field_esc(field_name))
            q_fields.push(this.db.field_esc(field_name) + ' = ' + val_str)
            q_values.push('/* ' + field_name + '/' + field + ' ' + type + ' */ ' + this.db.esc(val))
        }
        if (this.db.is_mysql()) {
            q.push(q_fields.join(',\n\t'))
        } else {
            q.push('(\n\t' + q_names.join(',\n\t') + '\n)')
            q.push('data_values(\n\t' + q_values.join(',\n\t') + '\n)')
        }
        if (pk) {
            q.push('WHERE\n\t' + this.db.field_esc(this.automake_pk) + ' = ' + this.db.esc(pk))
            const automake_chroot_type = this.automake_chroot_type
            if (automake_chroot_type == 1 && chroot_site) {
                const f1 = this.db.field_esc(this.automake_table) + '.' + this.db.field_esc(chroot_site)
                if (this.request.user?.ACL_DEV) q.push('\n\tAND (' + f1 + ' = ' + this.db.esc(this.request.sid) + ')')
                else q.push('\n\tAND (' + f1 + ' = ' + this.db.esc(this.request.sid) + ' OR ' + f1 + '=0)')
            }
            if (automake_chroot_type == 2 && automake_gid) {
                const f1 = this.db.field_esc(this.automake_table) + '.' + this.db.field_esc(automake_gid)
                if (this.request.user?.ACL_DEV) q.push('\nAND (' + f1 + ' = ' + this.db.esc(this.request.sid) + ')')
                else q.push('\nAND (' + f1 + ' = ' + this.db.esc(this.request.sid) + ' OR ' + f1 + '=0)')
            }
            if (automake_chroot_type == 3 && automake_uid) {
                const f1 = this.db.field_esc(this.automake_table) + '.' + this.db.field_esc(automake_uid)
                if (this.request.user?.ACL_DEV) q.push('\nAND (' + f1 + ' = ' + this.db.esc(this.request.sid) + ')')
                else q.push('\nAND (' + f1 + ' = ' + this.db.esc(this.request.sid) + ' OR ' + f1 + '=0)')
            }
        }
        const str = q.join('\n')
        yellow(str)
        let tree: any
        try {
            tree = await this.db.exec(str)
            tree = this.aes_decrypt_data(tree)
            if (tree.error) {
                this.error = tree.error
                return
            }
        } catch (e: any) {
            red(e)
            this.error = e
            return
        }
        let broadcast_data: any = {}
        broadcast_data.automake_id = this.automake_id
        broadcast_data.ip = this.request.IP
        broadcast_data.debug = this.request.query.debug || this.request.body.debug || false
        broadcast_data.sid = this.request.sid
        broadcast_data.gid = this.request.gid
        broadcast_data.uid = this.request.uid
        broadcast_data.action = this.action
        broadcast_data.table = this.automake_table
        let tbl = this.db.field_esc(this.automake_db) + '.' + this.db.field_esc(this.automake_table)
        let stm_last_id = 'SELECT MAX(' + this.db.field_esc(this.automake_pk) + ') AS id FROM ' + tbl
        let stm_last_id_filter = []
        if (chroot_site) stm_last_id_filter.push(this.db.field_esc(chroot_site) + ' = ' + this.db.esc(this.request.sid))
        if (automake_gid) stm_last_id_filter.push(this.db.field_esc(automake_gid) + ' = ' + this.db.esc(this.request.gid))
        if (automake_uid) stm_last_id_filter.push(this.db.field_esc(automake_uid) + ' = ' + this.db.esc(this.request.uid))
        if (stm_last_id_filter.length > 0) stm_last_id += ' where ' + stm_last_id_filter.join(' and ')
        const result = await this.db.exec(stm_last_id)
        if (result.is_err()) {
            this.error = result.error()
            return
        }
        const r = result.gr()
        let id = 0
        if (r.id > 0) id = r.id
        broadcast_data.primary_key = pk ? pk : id
        this.broadcast_data_change(broadcast_data)
        return broadcast_data
    }
    async delete(): Promise<any> {
        if (this.is_error) {
            red(this.error)
            return {status: false, message: this.error}
        }
        const pk = this.request.params.id
        if (!pk || pk == '' || pk.length < 1) {
            this.error = 'Chave primária não informada'
            return {status: false, message: this.error}
        }
        let q = []
        if (this.db.is_mysql()) q.push('DELETE LOW_PRIORITY IGNORE FROM ' + this.db.format_table(this.automake_db, this.automake_table))
        else if (this.db.is_mssql()) q.push('DELETE TOP (1) FROM ' + this.db.format_table(this.automake_db, this.automake_table))
        else q.push('DELETE FROM ' + this.db.format_table(this.automake_db, this.automake_table))
        const chroot_site = this.automake_chroot_site
        const automake_gid = this.automake_gid
        const automake_uid = this.automake_uid
        q.push('WHERE ' + this.db.field_esc(this.automake_pk) + ' = ' + this.db.esc(pk))
        const automake_chroot_type = this.automake_chroot_type
        if (automake_chroot_type == 1 && chroot_site) {
            const f1 = '  ' + this.db.field_esc(this.automake_table) + '.' + this.db.field_esc(chroot_site)
            if (this.request.user?.ACL_DEV) q.push('\nAND (' + f1 + ' = ' + this.db.esc(this.request.sid) + ')')
            else q.push('\nAND (' + f1 + ' = ' + this.db.esc(this.request.sid) + ' OR ' + f1 + '=0)')
        }
        if (automake_chroot_type == 2 && automake_gid) {
            const f1 = '  ' + this.db.field_esc(this.automake_table) + '.' + this.db.field_esc(automake_gid)
            if (this.request.user?.ACL_DEV) q.push('\nAND (' + f1 + ' = ' + this.db.esc(this.request.gid) + ')')
            else q.push('\nAND (' + f1 + ' = ' + this.db.esc(this.request.gid) + ' OR ' + f1 + '=0)')
        }
        if (automake_chroot_type == 3 && automake_uid) {
            const f1 = '  ' + this.db.field_esc(this.automake_table) + '.' + this.db.field_esc(automake_uid)
            if (this.request.user?.ACL_DEV) q.push('\nAND (' + f1 + ' = ' + this.db.esc(this.request.uid) + ')')
            else q.push('\nAND (' + f1 + ' = ' + this.db.esc(this.request.uid) + ' OR ' + f1 + '=0)')
        }
        if (this.db.is_mysql()) q.push('LIMIT 1')
        const str = q.join('\n')
        const tree_result = await this.db.exec(str)
        if (tree_result.is_err()) {
            this.error = tree_result.error()
            return {status: false, message: this.error}
        }
        this.broadcast_data_change({pk, action: 'delete'})
        return {status: true, message: 'Registro deletado com sucesso'}
    }
    async list(total_only: boolean = false) {
        let q: string[] = [' SELECT ']
        let q_fields: string[] = []
        let q_joins: string[] = []
        let group_by_pk = false
        q_fields.push(this.db.field_esc(this.automake_table) + '.' + this.db.field_esc(this.automake_pk) + ' primary_key')
        if (this.automake_ts)
            if (this.db.is_mysql()) {
                const ts_id = 'date_format(' + this.db.field_esc(this.automake_table) + '.' + this.db.field_esc(this.automake_ts) + ",'%d/%m/%y %H:%i')"
                q_fields.push(ts_id + ' AS __TS ')
            } else if (this.db.is_mssql()) {
                const ts_id = 'FORMAT(' + this.db.field_esc(this.automake_table) + '.' + this.db.field_esc(this.automake_ts) + ",'dd/MM/yy hh:mm','pt-BR')"
                q_fields.push(ts_id + ' AS __TS ')
            } else {
                const ts_id = this.db.field_esc(this.automake_table) + '.' + this.db.field_esc(this.automake_ts)
                q_fields.push(ts_id + ' AS __TS ')
            }
        if (this.automake_uid && this.db.is_mysql()) {
            q_fields.push('USER_INFO.user_name __USER_NAME ')
            q_fields.push(this.db.field_esc(this.automake_table) + '.' + this.db.field_esc(this.automake_uid) + ' __UID')
            let ss = 'LEFT JOIN users USER_INFO ON USER_INFO.user_id = ' + this.db.field_esc(this.automake_table) + '.' + this.db.field_esc(this.automake_uid)
            q_joins.push(ss)
        }
        if (this.automake_gid) q_fields.push(this.db.field_esc(this.automake_table) + '.' + this.db.field_esc(this.automake_gid) + ' __GID')
        if (this.automake_chroot_site) q_fields.push(this.db.field_esc(this.automake_table) + '.' + this.db.field_esc(this.automake_chroot_site) + ' __SID')
        let fields_by_name: any = this.fields_by_name
        let filters_by_field: string[] = []
        for (let i = 0; i < this.fields.length; i++) {
            const r: FIELD = this.fields[i]
            const field_id = r.field_id
            const type = r.field_type
            const field_name = r.field_name
            const field_list_sql = r.field_list_sql
            const field = field_name
            const no_display_value = r.field_no_display_value
            let alias = r.field_name
            if (r.field_alias) alias = r.field_alias
            const alias_esc = this.db.field_esc(alias)
            fields_by_name[alias] = r
            const filters: {field_name: string; value: string}[] = Array.isArray(this.request.query.filters)
                ? (this.request.query.filters as {field_name: string; value: string}[])
                : []
            const has_filter = filters.length > 0 && filters.find(f => f.field_name == field_name)
            if (has_filter) {
                let user_input_search = filters.find(f => f.field_name == field_name)?.value
                if (!user_input_search) continue
                if (typeof user_input_search !== 'string') {
                    red('Invalid search filter type:', typeof user_input_search)
                    continue
                }
                if (user_input_search.length > 1000) {
                    red('Search term too long:', user_input_search.length)
                    continue
                }
                let term, op
                if (user_input_search.indexOf('>=') != -1) {
                    term = user_input_search.replace('>=', '')
                    op = '>='
                } else if (user_input_search.indexOf('<=') != -1) {
                    term = user_input_search.replace('<=', '')
                    op = '<='
                } else if (user_input_search.indexOf('!=') != -1) {
                    term = user_input_search.replace('!=', '')
                    op = '!='
                } else if (user_input_search.indexOf('>') != -1) {
                    term = user_input_search.replace('>', '')
                    op = '>'
                } else if (user_input_search.indexOf('<') != -1) {
                    term = user_input_search.replace('<', '')
                    op = '<'
                } else if (user_input_search.indexOf('=') != -1) {
                    term = user_input_search.replace('=', '')
                    op = '='
                } else {
                    if (this.db.is_mysql()) {
                        op = 'REGEXP'
                        term = user_input_search
                        try {
                            new RegExp(term)
                        } catch (e: any) {
                            red(e)
                            red('Invalid regex pattern:', term)
                            continue
                        }
                    } else {
                        op = 'LIKE'
                        if (user_input_search.indexOf('%') == -1) term = '%' + user_input_search.trim() + '%'
                        else term = user_input_search
                    }
                }
                term = term.trim()
                const filter = this.db.field_esc(this.automake_table) + '.' + this.db.field_esc(field) + ' ' + op + ' ' + this.db.esc(term)
                filters_by_field.push('/* filtro ' + field_id + ' */ ' + filter)
            }
            if (!this.main.acl_check(this.request.user?.acl, r.field_show_on_list)) continue
            const select_db = r.field_select_db
            const select_table = r.field_select_table
            const select_label = r.field_select_label
            const select_pk = r.field_select_pk
            let select_alias = r.field_select_alias
            if (!select_alias) select_alias = select_table
            if (select_db && select_table && select_pk && select_pk) {
                const field_full_id = this.db.field_esc(select_alias) + '.' + this.db.field_esc(select_label)
                let s = field_full_id
                if (select_label.indexOf('(') != -1 || select_label.toLowerCase().indexOf('select ') != -1) s = select_label
                else if ((type == 'multicoma' || type == 'select-multi') && this.db.is_mysql())
                    s = 'GROUP_CONCAT( ' + field_full_id + ' ORDER BY ' + field_full_id + " SEPARATOR ', ') "
                s += ' as ' + alias_esc
                let lj_table = this.db.field_esc(select_db) + '.' + this.db.field_esc(select_table)
                if (this.db.is_mssql()) lj_table = this.db.field_esc(select_db) + '.dbo.' + this.db.field_esc(select_table)
                let ss = ' LEFT JOIN ' + lj_table + ' as ' + select_alias
                if (type == 'multicoma' || type == 'select-multi') {
                    group_by_pk = true
                    ss += ' on FIND_IN_SET(' + this.db.field_esc(select_alias) + '.' + this.db.field_esc(select_pk)
                    ss += ', ' + this.db.field_esc(this.automake_table) + '.' + this.db.field_esc(field) + ')'
                } else {
                    ss += ' on ' + this.db.field_esc(select_alias) + '.' + this.db.field_esc(select_pk)
                    ss += ' = ' + this.db.field_esc(this.automake_table) + '.' + this.db.field_esc(field)
                }
                if (r.field_select_where) ss += ' and (' + r.field_select_where + ') '
                q_joins.push(ss)
                q_fields.push(s + ' /* ' + r.field_label + ' */ ')
            } else {
                if (no_display_value) {
                    q_fields.push(" '' AS " + this.db.field_esc(field) + ' /* ' + r.field_label + '/no_display_value */')
                } else {
                    const info = ' /* ' + r.field_label + ' */ '
                    if (field.indexOf(' ') != -1 || field.indexOf('.') != -1 || field.indexOf("'") != -1 || field.indexOf('(') != -1) {
                        q_fields.push(field + ' AS ' + alias_esc + info)
                    } else if (field_list_sql) {
                        q_fields.push(field_list_sql + ' as ' + alias_esc + info)
                    } else {
                        const field_esc = this.db.field_esc(this.automake_table) + '.' + this.db.field_esc(field)
                        if (type == 'date')
                            if (this.db.is_mssql()) q_fields.push(' FORMAT(' + field_esc + ",'dd/MM/yy','pt-BR') AS " + alias_esc + info)
                            else if (this.db.is_mysql()) q_fields.push(' DATE_FORMAT(' + field_esc + ",'%d/%m/%y') AS " + alias_esc + info)
                            else q_fields.push(' ' + field_esc + ' AS ' + alias_esc + info)
                        else if (type == 'datetime')
                            if (this.db.is_mssql()) q_fields.push(' FORMAT(' + field_esc + ",'dd/MM/yy hh:mm','pt-BR') AS " + alias_esc + info)
                            else if (this.db.is_mysql()) q_fields.push(' DATE_FORMAT(' + field_esc + ",'%d/%m/%y %H:%i') AS " + alias_esc + info)
                            else q_fields.push(' ' + field_esc + ' AS ' + alias_esc + info)
                        else q_fields.push(field_esc + ' as ' + alias_esc + info)
                    }
                }
            }
        }
        if (total_only) q.push('\n\tcount(*) as total ')
        else q.push('\n\t' + q_fields.join(',\n\t'))
        q.push('\nFROM ' + this.db.format_table(this.automake_db, this.automake_table))
        q.push('\n' + q_joins.join('\n'))
        this.automake_search = this.request.query.q || this.request.body.q || []
        let where: string[] = []
        const chroot_site = this.automake_chroot_site
        const automake_gid = this.automake_gid
        const automake_uid = this.automake_uid
        const chroot_type = this.automake_chroot_type
        if (chroot_type == 1 && chroot_site) {
            const f1 = this.db.field_esc(this.automake_table) + '.' + this.db.field_esc(chroot_site)
            if (this.request.user?.acl?.indexOf('ACL_DEV') == -1) where.push('\n(' + f1 + ' = ' + this.db.esc(this.request.sid) + ')')
            else where.push('\n(' + f1 + ' IN (' + this.db.esc(this.request.sid) + ',0) )')
        } else if (chroot_type == 2 && automake_gid) {
            const f1 = this.db.field_esc(this.automake_table) + '.' + this.db.field_esc(automake_gid)
            if (this.request.user?.acl?.indexOf('ACL_DEV') == -1) where.push('\n(' + f1 + ' = ' + this.db.esc(this.request.gid) + ')')
            else where.push('\n(' + f1 + ' IN (' + this.db.esc(this.request.gid) + ',0) )')
        } else if (chroot_type == 3 && automake_uid) {
            const f1 = this.db.field_esc(this.automake_table) + '.' + this.db.field_esc(automake_uid)
            if (this.request.user?.acl?.indexOf('ACL_DEV') == -1) where.push('\n(' + f1 + ' = ' + this.db.esc(this.request.uid) + ')')
            else where.push('\n(' + f1 + ' IN (' + this.db.esc(this.request.uid) + ',0) )')
        }
        if (this.automake_search) {
            const user_input_search = this.request.query.q || this.request.body.q || []
            const filters = []
            let term, op
            if (user_input_search.indexOf('>=') != -1) {
                term = user_input_search.replace('>=', '')
                op = '>='
            } else if (user_input_search.indexOf('<=') != -1) {
                term = user_input_search.replace('<=', '')
                op = '<='
            } else if (user_input_search.indexOf('!=') != -1) {
                term = user_input_search.replace('!=', '')
                op = '!='
            } else if (user_input_search.indexOf('>') != -1) {
                term = user_input_search.replace('>', '')
                op = '>'
            } else if (user_input_search.indexOf('<') != -1) {
                term = user_input_search.replace('<', '')
                op = '<'
            } else if (user_input_search.indexOf('=') != -1) {
                term = user_input_search.replace('=', '')
                op = '='
            } else {
                if (this.db.is_mysql()) {
                    op = 'REGEXP'
                    term = user_input_search
                } else {
                    op = 'LIKE'
                    if (user_input_search.indexOf('%') == -1) term = '%' + user_input_search.trim() + '%'
                    else term = user_input_search
                }
            }
            term = term.trim()
            for (let i in this.automake_search) {
                const field = this.automake_search[i].trim()
                filters.push('\n' + this.db.field_esc(this.automake_table) + '.' + this.db.field_esc(field) + ' ' + op + ' ' + this.db.esc(term))
            }
            where.push('(' + filters.join(' OR ') + ')')
        }
        if (this.automake_ts && (this.request.query.ds || this.request.query.de)) {
            let date_filters = []
            if (this.automake_ts && this.request.query.ds) {
                const ds = this.db.esc(this.request.query.ds + ' 00:00:00')
                const ts = this.db.field_esc(this.automake_table) + '.' + this.db.field_esc(this.automake_ts)
                date_filters.push(ts + ' >= ' + ds)
            }
            if (this.automake_ts && this.request.query.de) {
                const de = this.db.esc(this.request.query.de + ' 23:59:59')
                const ts = this.db.field_esc(this.automake_table) + '.' + this.db.field_esc(this.automake_ts)
                date_filters.push(ts + ' <= ' + de)
            }
            where.push('(' + date_filters.join('\nAND ') + ')')
        }
        if (filters_by_field.length > 0) where.push(filters_by_field.join('\nAND'))
        if (where.length > 0) q.push('\nWHERE\n' + where.join('AND\n'))
        if (group_by_pk && !total_only) q.push('\nGROUP BY ' + this.db.field_esc(this.automake_table) + '.' + this.db.field_esc(this.automake_pk))
        let automake_order_by = this.automake_order_by
        if (automake_order_by) q.push('\nORDER BY ' + automake_order_by)
        else if (this.db.is_mssql())
            if (this.automake_ts) {
                q.push('\nORDER BY ' + this.db.field_esc(this.automake_table) + '.' + this.db.field_esc(this.automake_ts) + ' DESC')
            } else if (this.automake_search) {
                let s = this.automake_search[0]
                q.push('\nORDER BY ' + this.db.field_esc(this.automake_table) + '.' + this.db.field_esc(s) + ' ASC')
            } else {
                q.push('\nORDER BY ' + this.db.field_esc(this.automake_table) + '.' + this.db.field_esc(this.automake_pk) + ' DESC')
            }
        const limit = this.request.query.limit || this.automake_limit || 10
        if (!total_only)
            if (this.db.is_mysql()) {
                const offset = this.request.query.offset || 0
                q.push('\nLIMIT ' + offset + ',' + limit)
            } else if (this.db.is_sqlsrv()) {
                const offset = this.request.query.offset || 0
                q.push('\nOFFSET ' + offset + ' ROWS FETCH NEXT ' + limit + ' ROWS ONLY')
            }
        let query = q.join('\n')
        query = this.am_var_parse(this.request, this, query)
        const source_result = await this.db.exec(query)
        if (source_result.is_err()) {
            this.set_error(source_result.error())
            return []
        }
        const rows: ROWS = source_result.ga()
        let result: any[] = []
        if (rows.length)
            for (let i in rows) {
                let r = rows[i]
                let row: any = {}
                for (let field in r) {
                    let f = fields_by_name[field]
                    row[field] = r[field]
                    if (!f) continue
                    if (f.row_link) {
                        let row_link = this.am_var_parse(this.request, this, f.row_link, r)
                        row[field] = '<a class="btn btn-outline-primary btn-sm" role="button" href="' + row_link + '">' + r[field] + '</a>'
                    }
                }
                result.push(row)
            }
        return result
    }
    async total() {
        return this.list(true)
    }
    async get_field_data(_field: string, _id: number) {
        yellow('//REVIEW', _field, _id)
        let value: any = {}
        const field = this.fields_by_name[_field]
        if (!field) return {error: `Campo não encontrado: ${_field}`}
        const row = await this.db.exec(field.select_query)
        if (row.is_err()) return {error: row.error()}
        const row_data = row.ga()[0]
        if (row_data) value = row_data[_field]
        return {value, field}
    }
    async save_field(_field: string, _id: number) {
        yellow('//REVIEW', _field, _id)
        return {status: true, message: 'Campo salvo com sucesso', result: _field}
    }
}
export default class AUTOMAKE {
    IV: string = ''
    site_config: any
    is_dev: boolean
    site_cache_changed: (id: string, r?: any) => void = () => {}
    db: DB
    main: Main
    db_config: ConnectionOptions = {}
    private request_counts: Map<string, {count: number; last_reset: number}> = new Map()
    private readonly RATE_LIMIT_WINDOW = 60000
    private readonly RATE_LIMIT_MAX_REQUESTS = 100
    constructor(main: Main) {
        this.main = main
        this.db = main.db
        this.site_cache_changed = main.site_cache_changed
        this.site_config = main.site_config
        this.is_dev = main.is_dev
        main.GET('/a/:automake_id', this.automake_list.bind(this), true)
        main.GET('/crud/total/:automake_id', this.automake_total.bind(this), true)
        main.GET('/crud/admin/:conector/:action', this.adm_dtd.bind(this), true)
        main.GET('/aa/:automake_id', this.automake_add.bind(this), true)
        main.GET('/ae/:automake_id/:id', this.automake_edit.bind(this), true)
        main.GET('/arm/:automake_id/:id', this.automake_rm.bind(this), true)
        main.POST('/as/:automake_id', this.automake_save.bind(this), true)
        main.GET('/crud/select-data/:automake_id/:id', this.automake_load_select.bind(this), true)
        main.GET('/crud/select-data/modal/:automake_id/:id', this.automake_load_select_modal.bind(this), true)
        main.GET('/app/:app_name', this.app_view.bind(this))
        main.POST('/app/:app_name', this.app_view.bind(this))
        main.GET('/i/:app_name', this.app_view.bind(this))
        main.POST('/i/:app_name', this.app_view.bind(this))
        main.GET('/a/:uri/:app_name', this.app_view.bind(this))
        main.POST('/a/:uri/:app_name', this.app_view.bind(this))
        main.GET('/b/:uri/:app_name/:id', this.app_view.bind(this))
        main.POST('/b/:uri/:app_name/:id', this.app_view.bind(this))
        main.GET('/crud/adm', this.adm_automake.bind(this), true)
        main.POST('/crud/adm', this.adm_automake.bind(this), true)
        main.GET('/crud/adm/fields/list/:automake_id', this.adm_automake_fields_list.bind(this), true)
        main.POST('/crud/adm/fields/save/:automake_id', this.adm_automake_field_save.bind(this), true)
        main.GET('/crud/adm/fields/form/:automake_id/:field_id', this.adm_automake_fields_form.bind(this), true)
        main.GET('/crud/adm/fields/rm/:automake_id/:field_id', this.adm_automake_fields_rm.bind(this), true)
        main.POST('/crud/adm/save', this.adm_automake_save.bind(this), true)
        main.GET('/crud/adm/data/:type/:automake_id', this.adm_automake_data.bind(this), true)
        main.POST('/crud/adm/save_editor/:id/:type', this.adm_automake_save_editor.bind(this), true)
        main.GET('/crud/adm/rm/:db/:automake_id', this.adm_automake_rm.bind(this), true)
    }
    async automake_list(req: REQ, res: express.Response) {
        let self = this
        const automake_id = parseInt(req.params.automake_id) || parseInt(req.query.automake_id as string) || 0
        const app: APP_CONFIG | undefined | undefined = await this.setup(req, res, automake_id, 'list')
        if (!app) return res.json({error: 'Aplicação não pode ser iniciada'})
        if (app.is_error) return res.json({error: app.error})
        let r = app
        r.tree = await app.list(false)
        if (req.query.op == 'list' || req.xhr) {
            let html = ''
            for (const i in r.tree) {
                const row = r.tree[i]
                const tr_class = !row.__SID ? ' am_td_g' : ''
                const tr_title = `${row.__TS ? 'DATA: ' + row.__TS : ''} ${row.__USER_NAME ? 'Usuário: ' + row.__USER_NAME : ''}`
                html += `<tr class="${tr_class} am_tr small text-small" title="${tr_title}">`
                if (app.CAN_EDIT) {
                    html += `<td class="small text-small action_btn" title="Editar">`
                    html += `<a name="${row.primary_key}" id="${row.primary_key}" href="#${row.primary_key}" onclick="automake_edit('${app.automake_id}','${row.primary_key}'); return false;">`
                    html += `<i class="fas fa-edit"></i></a></td>`
                }
                for (const tab_name in app.fields_by_tab_and_col) {
                    const tabs: {[col: number]: FIELD[]} = app.fields_by_tab_and_col[tab_name]
                    for (const col_index in tabs) {
                        const cols: FIELD[] = tabs[col_index]
                        for (const field_index in cols) {
                            const f: FIELD = cols[field_index]
                            const value = row[f.field_name] || ''
                            const type = f.field_type
                            const config_data = f.config_data
                            const css_list = f.field_css_list || f.field_css_list
                            const help = f.field_help || f.field_help
                            let td = ''
                            let td_content = value || ''
                            let td_class = ''
                            let td_style = ''
                            let td_title = help || ''
                            if (css_list && css_list !== '0') td_class += ` ${css_list}`
                            if (type == 'bool') {
                                td += 'width=1 nowrap align=center'
                                td_content = value ? '<i class="fas fa-check text-success"></i>' : '<i class="fas fa-times text-danger"></i>'
                            }
                            if (type == 'color') {
                                td += `width=1 nowrap align=center`
                                td_style += `background-color:${value};`
                                td_content = value
                            }
                            if (type == 'icon' || type == 'icone') {
                                td += ' width=1 nowrap align=center'
                                td_content = '<i class="' + value + '"></i> ' + value + ''
                            }
                            if (type == 'date') {
                                td += ' width=1 nowrap align=center'
                                td_content = self.main.date_format(value, 'DD/MM/YY')
                            }
                            if (type == 'datetime') {
                                td += ' width=1 nowrap align=center'
                                const comment = self.main.moment(value).fromNow()
                                const date = self.main.date_format(value, 'DD/MM/YY HH:mm')
                                td_content = `<b>${date}</b><div class="text-muted text-small">${comment}</div>`
                            }
                            if (type == 'int' || type == 'number' || type == 'numeric') td += ' width=1 nowrap align=right'
                            if (type == 'coma' && config_data) td_content = config_data[value]
                            const td_attrs = []
                            if (td) td_attrs.push(td.trim())
                            if (td_class) td_attrs.push(`class="${td_class.trim()}"`)
                            if (td_style) td_attrs.push(`style="${td_style}"`)
                            if (td_title) td_attrs.push(`title="${td_title}"`)
                            if (app.CAN_EDIT && !f.field_read_only && !f.field_read_only) {
                                td_attrs.push(`data-field="${f.field_name}"`)
                                td_attrs.push(`data-field-type="${type}"`)
                                td_attrs.push(`data-pk="${app.automake_pk}"`)
                                td_attrs.push(`data-app="${app.automake_id}"`)
                                td_attrs.push(`ondblclick="automake_inline_edit(this)"`)
                                td_class += ' editable-field'
                                td_style = `cursor: pointer; ${td_style}`
                            }
                            html += `<td ${td_attrs.join(' ')}>${td_content}</td>`
                        }
                    }
                }
                if (app.CAN_ADD) {
                    html += `<td class="action_btn" title="Copiar">`
                    html += `<a name="N${app.automake_pk}" id="N${app.automake_pk}" href="#${app.automake_pk}" onclick="automake_copy('${app.automake_id}','${app.automake_pk}'); return false;">`
                    html += `<i class="fas fa-copy"></i></a></td>`
                }
                html += '</tr>'
            }
            return res.send(html)
        } else {
            self.main.display(req, res, 'adm', 'automake', r)
        }
    }
    async automake_total(req: REQ, res: express.Response) {
        const automake_id = parseInt(req.params.automake_id) || parseInt(req.query.automake_id as string) || 0
        if (!automake_id || automake_id == 0) return res.json({error: 'automake_id não informado como parte da url.'})
        const app = await this.setup(req, res, automake_id, 'total')
        if (!app) return res.json({error: 'Aplicação não pode ser iniciada'})
        if (app.is_error) return res.json({error: app.error})
        const total = app.total()
        res.json({total})
    }
    private async setup(req: REQ, res: express.Response, _automake_id: number | string, action: VALID_ACTION): Promise<APP_CONFIG | undefined> {
        const app = new APP_CONFIG(this.main, req, res, _automake_id)
        if (app.is_error) return
        const ok = await app.init(action)
        if (!ok) return
        return app
    }
    async automake_add(req: REQ, res: express.Response) {
        const automake_id = parseInt(req.params.automake_id) || parseInt(req.query.automake_id as string) || 0
        if (!automake_id || automake_id == 0) return res.json({error: 'automake_id não informado como parte da url.'})
        const app: APP_CONFIG | undefined = await this.setup(req, res, automake_id, 'add')
        if (!app) return res.json({error: 'Aplicação não pode ser iniciada'})
        if (app.is_error) return res.json({error: app.error})
        if (!app) return res.json({error: 'Aplicação inválida'})
        const acl = app.permissions('CAN_ADD')
        if (!acl.status) return res.json({error: acl.message || `Acesso negado para adicionar ${app.automake_name}`})
        let r: any = app
        r.site = req.site
        r.primary_key = ''
        r.COPY = req.query.COPY ? 1 : 0
        r.user = req.user
        r.ip = req.IP
        r.xhr = req.xhr
        r.data_values = await app.edit()
        r.form_fields = []
        r.cb = req.query.cb || ''
        for (let i = 0; i < app.fields.length; i++) {
            const f = app.fields[i]
            if (!this.main.acl_check(req.user?.acl, f.field_show_on_form)) continue
            const field_id = f.field_id
            r.data_values[field_id] = f.field_default_value
            r.form_fields.push(f)
        }
        return this.main.display(req, res, 'adm', 'automake_form', r)
    }
    async automake_edit(req: REQ, res: express.Response) {
        const automake_id = parseInt(req.params.automake_id) || parseInt(req.query.automake_id as string) || 0
        if (!automake_id || automake_id == 0) return res.json({error: 'automake_id não informado como parte da url.'})
        const app: APP_CONFIG | undefined = await this.setup(req, res, automake_id, 'add')
        if (!app) return res.json({error: 'Aplicação não pode ser iniciada'})
        if (app.is_error) return res.json({error: app.error})
        if (!app) return res.json({error: 'Aplicação inválida'})
        if (!app.fields) return res.json({error: 'Sem campos'})
        if (app.fields.length < 1) return res.json({error: 'Aplicação sem campos.'})
        const acl = app.permissions('CAN_EDIT')
        if (!acl.status) return res.json({error: acl.message || `Acesso negado para editar ${app.automake_name}`})
        let r: any = app
        r.site = req.site
        r.primary_key = req.params.id
        r.user = req.user
        r.ip = req.IP
        r.data_values = await app.edit()
        r.form_fields = []
        r.cb = req.query.cb || ''
        for (let i = 0; i < app.fields.length; i++) {
            const f = app.fields[i]
            if (!this.main.acl_check(req.user?.acl, f.field_show_on_form)) continue
            r.form_fields.push(f)
        }
        if (req.query) r.COPY = req.query.copy
        return this.main.display(req, res, 'adm', 'automake_form', r)
    }
    async automake_save(req: REQ, res: express.Response) {
        const automake_id = parseInt(req.params.automake_id) || parseInt(req.query.automake_id as string) || 0
        if (!automake_id || automake_id == 0) return res.json({error: 'automake_id não informado como parte da url.'})
        const app: APP_CONFIG | undefined = await this.setup(req, res, automake_id, 'add')
        if (!app) return res.json({error: 'Aplicação não pode ser iniciada'})
        if (app.is_error) return res.json({error: app.error})
        if (!app) return res.json({error: 'Aplicação inválida'})
        const acl = app.permissions('CAN_ADD')
        if (!acl.status) return res.json({error: acl.message || `Acesso negado para adicionar ${app.automake_name}`})
        if (!this.check_rate_limit(req.IP || '')) return res.json({error: 'RATE_LIMIT_EXCEEDED', message: 'Too many requests. Please try again later.', details: ''})
        if (!this.validate_request_size(req)) return res.json({error: 'REQUEST_TOO_LARGE', message: 'Request size exceeds limits.', details: ''})
        if (!this.validate_request_origin(req)) {
            red('Invalid request origin detected:', req.headers.origin, req.headers.referer)
            return res.json({error: 'INVALID_ORIGIN', message: 'Request origin validation failed.', details: ''})
        }
        if (this.detect_automated_requests(req)) {
            red('Automated request detected from:', req.IP, req.headers['user-agent'])
            return res.json({error: 'AUTOMATED_REQUEST', message: 'Automated requests are not allowed.', details: ''})
        }
        if (req.body.bulk_operation) {
            const bulk_validation = this.validate_bulk_operation(req)
            if (!bulk_validation.valid) return res.json({error: 'BULK_VALIDATION_FAILED', message: bulk_validation.error || 'Bulk operation validation failed.', details: ''})
        }
        if (!this.validate_table_access(req, app.automake_table)) {
            this.log_suspicious_activity(req, 'UNAUTHORIZED_TABLE_ACCESS', {
                table: app.automake_table,
                action: 'save'
            })
            return res.json({error: 'ACESSO_NEGADO', message: 'Sem permissão para acessar esta tabela.', details: ''})
        }
        const id = await app.save()
        if (!id) return res.json({error: 'Erro ao salvar'})
        return this.main.redirect(req, res, '/automake/' + app.automake_name)
    }
    async automake_rm(req: REQ, res: express.Response) {
        const automake_id = parseInt(req.params.automake_id) || parseInt(req.query.automake_id as string) || 0
        if (!automake_id || automake_id == 0) return res.json({error: 'automake_id não informado como parte da url.'})
        const app = await this.setup(req, res, automake_id, 'delete')
        if (!app) return res.json({error: 'Aplicação não pode ser iniciada'})
        if (app.is_error) return res.json({error: app.error})
        if (!app) return res.json({error: 'Aplicação inválida'})
        const acl = app.permissions('CAN_DELETE')
        if (!acl.status) return res.json({error: acl.message || `Acesso negado para deletar ${app.automake_name}`})
        if (!app.fields) return res.json({error: 'Sem campos', message: 'Sem campos cadastrados na aplicação.'})
        if (app.fields.length < 1) return res.json({error: '!fields', message: 'Aplicação sem campos.'})
        const result = await app.delete()
        this.site_cache_changed(app.automake_table)
        if (req.xhr) {
            return res.json(result)
        } else {
            const uri = `/automake/${app.automake_name}?status=${result.status}&message=${result.message}`
            return this.main.redirect(req, res, uri)
        }
    }
    async automake_load_select(req: REQ, res: express.Response) {
        const automake_id = parseInt(req.params.automake_id) || parseInt(req.query.automake_id as string) || 0
        if (!automake_id || automake_id == 0) return res.json({error: 'automake_id não informado como parte da url.'})
        const app = await this.setup(req, res, automake_id, 'combo')
        if (!app) return res.json({error: 'Aplicação não pode ser iniciada'})
        if (app.is_error) return res.json({error: app.error})
        if (!app) return res.json({error: 'Aplicação inválida'})
        const acl = app.permissions('CAN_VIEW')
        if (!acl.status) return res.json({error: acl.message || `Acesso negado para visualizar ${app.automake_name}`})
        const result = await app.combo()
        return res.json(result)
    }
    async automake_load_select_modal(req: REQ, res: express.Response) {
        const automake_id = parseInt(req.params.automake_id) || parseInt(req.query.automake_id as string) || 0
        if (!automake_id || automake_id == 0) return res.json({error: 'automake_id não informado como parte da url.'})
        const app = await this.setup(req, res, automake_id, 'combo')
        if (!app) return res.json({error: 'Aplicação não pode ser iniciada'})
        if (app.is_error) return res.json({error: app.error})
        if (!app) return res.json({error: 'Aplicação inválida'})
        const acl_view = app.permissions('CAN_VIEW')
        const acl_list = app.permissions('CAN_LIST')
        const acl_edit = app.permissions('CAN_EDIT')
        const any_acl = acl_view.status || acl_list.status || acl_edit.status
        if (!any_acl) return res.json({error: 'Acesso negado para visualizar, listar ou editar ' + app.automake_name})
        const result = await app.combo()
        return this.main.display(req, res, 'index_public', 'automake_list_modal', result)
    }
    async automake_load_query(req: REQ, res: express.Response) {
        const automake_id = parseInt(req.params.automake_id) || parseInt(req.query.automake_id as string) || 0
        if (!automake_id || automake_id == 0) return res.json({error: 'automake_id não informado como parte da url.'})
        const app = await this.setup(req, res, automake_id, 'combo')
        if (!app) return res.json({error: 'Aplicação não pode ser iniciada'})
        if (app.is_error) return res.json({error: app.error})
        if (!app) return res.json({error: 'Aplicação inválida'})
        const acl = app.permissions('CAN_VIEW')
        if (!acl.status) return res.json({error: acl.message || `Acesso negado para visualizar ${app.automake_name}`})
        const result = await app.field_query()
        return res.send(result)
    }
    async am_conectors_init() {
        const tree_res = await this.db.call('rds_connectors_init')
        if (tree_res.is_err()) return red(tree_res.error())
        const tree = tree_res.ga()
        for (let i in tree) {
            const r = tree[i]
            const connector_id = parseInt(r.connector_id)
            const type = r.connector_type
            if (connectors[connector_id]) continue
            if (type != 'mysql') return red('CONECTOR INVÁLIDO: ' + type)
            const name = r.connector_name
            const db_config: ConnectionOptions = {
                user: r.connector_user,
                password: r.connector_password,
                database: r.connector_database,
                host: r.connector_host,
                port: parseInt(r.connector_port || '3306'),
                //eslint-disable-next-line @typescript-eslint/naming-convention
                decimalNumbers: true,
                //eslint-disable-next-line @typescript-eslint/naming-convention
                supportBigNumbers: true,
                //eslint-disable-next-line @typescript-eslint/naming-convention
                bigNumberStrings: true,
                //eslint-disable-next-line @typescript-eslint/naming-convention
                multipleStatements: false,
                //eslint-disable-next-line @typescript-eslint/naming-convention
                enableKeepAlive: true,
                //eslint-disable-next-line @typescript-eslint/naming-convention
                keepAliveInitialDelay: 10,
                //eslint-disable-next-line @typescript-eslint/naming-convention
                connectTimeout: 1000,
                timezone: r.connector_tz,
                charset: r.connector_charset,
                ssl: undefined
            }
            if (
                this.db.config.user == this.db_config.user &&
                this.db.config.password == this.db_config.password &&
                this.db.config.database == this.db_config.database &&
                this.db.config.host == this.db_config.host &&
                this.db.config.port == this.db_config.port
            )
                connectors[connector_id] = new am_mysql(this.db, name, db_config, (error?: string) => {
                    if (error) throw new Error(error)
                })
            else
                connectors[connector_id] = new am_mysql(undefined, name, db_config, (error?: string) => {
                    if (error) throw new Error(error)
                })
        }
    }
    async dtd(_conexao_id: number | string, action: DTD_ACTIONS, db?: string, table?: string, sql?: string) {
        const conexao_id: number = parseInt(_conexao_id as string)
        const conexao = connectors[conexao_id]
        if (!conexao) return {error: `Conector ${conexao_id} não encontrado.`}
        if (action == 'ds_exec_sql') {
            if (!sql) return {error: 'NO-SQL'}
            if (!this.validate_sql(sql)) {
                red('SQL injection attempt blocked:', sql)
                return {error: 'INVALID-SQL'}
            }
            const result = await conexao.exec(sql)
            if (result.is_err()) return {error: result.error()}
            return result.ga()
        } else if (action == 'databases') {
            const result = await conexao.databases()
            if (result.is_err()) return {error: result.error()}
            return result.ga()
        } else if (action == 'tables') {
            if (!db) return {error: 'NO-DB'}
            const sanitized_db = this.sanitize_identifier(db)
            if (sanitized_db !== db) return {error: 'INVALID-DB-NAME'}
            const result = await conexao.tables(sanitized_db)
            if (result.is_err()) return {error: result.error()}
            return result.ga()
        } else if (action == 'fields') {
            if (!db) return {error: 'NO-DB'}
            if (!table) return {error: 'NO-TABLE'}
            const sanitized_db = this.sanitize_identifier(db)
            const sanitized_table = this.sanitize_identifier(table)
            if (sanitized_db !== db || sanitized_table !== table) return {error: 'INVALID-IDENTIFIER'}
            const result = await conexao.fields(sanitized_db, sanitized_table)
            if (result.is_err()) return {error: result.error()}
            return result.ga()
        } else {
            return {error: 'AÇÃO ' + action + ' INVÁLIDA'}
        }
    }
    uuid() {
        return crypto.createHash('md5').update(uuidv4()).digest('hex')
    }
    sha256(str: string) {
        return crypto.createHash('sha256').update(str).digest('hex')
    }
    sha512(str: string) {
        return crypto.createHash('sha512').update(str).digest('hex')
    }
    am_acl(req: REQ, id: string): boolean {
        return this.main.acl_check(req.user?.acl, [id])
    }
    async adm_automake_rm(req: REQ, res: express.Response) {
        let self = this
        if (!req.user?.ACL_DEV) return res.json({error: 'SOMENTE PARA DESENVOLVEDOR.'})
        const automake_id = parseInt(req.params.automake_id) || parseInt(req.query.automake_id as string) || 0
        if (!automake_id || automake_id == 0) return res.json({error: 'automake_id não informado como parte da url.'})
        const r_res = await this.db.call('adm_automake_rm', [req.user?.uid, req.IP, automake_id])
        if (r_res.is_err()) return res.json({error: r_res.error()})
        self.main.redirect(req, res, '/crud/adm')
    }
    adm_automake_save_index: number = 0
    private validate_automake_fields(body: any): string[] {
        const errors: string[] = []
        if (!body.automake_name || typeof body.automake_name !== 'string' || body.automake_name.trim().length === 0)
            errors.push(`automake_name=${body.automake_name} Nome da aplicação é obrigatório`)
        else if (body.automake_name.length > 100) errors.push(`automake_name=${body.automake_name} Nome da aplicação muito longo (máximo 100 caracteres)`)
        else if (!/^[a-zA-ZÀ-ÿ_][a-zA-ZÀ-ÿ0-9_]*$/.test(body.automake_name.trim()))
            errors.push(`automake_name=${body.automake_name} Nome da aplicação deve começar com letra ou underscore e conter apenas letras, números e underscores`)
        if (!body.automake_db || typeof body.automake_db !== 'string' || body.automake_db.trim().length === 0)
            errors.push(`automake_db=${body.automake_db} Nome do banco de dados é obrigatório`)
        else if (body.automake_db.length > 64) errors.push(`automake_db=${body.automake_db} Nome do banco muito longo (máximo 64 caracteres)`)
        else if (!/^[a-zA-ZÀ-ÿ_][a-zA-ZÀ-ÿ0-9_]*$/.test(body.automake_db.trim()))
            errors.push(`automake_db=${body.automake_db} Nome do banco deve começar com letra ou underscore e conter apenas letras, números e underscores`)
        if (!body.automake_table || typeof body.automake_table !== 'string' || body.automake_table.trim().length === 0)
            errors.push(`automake_table=${body.automake_table} Nome da tabela é obrigatório`)
        else if (body.automake_table.length > 64) errors.push(`automake_table=${body.automake_table} Nome da tabela muito longo (máximo 64 caracteres)`)
        else if (!/^[a-zA-ZÀ-ÿ_][a-zA-ZÀ-ÿ0-9_]*$/.test(body.automake_table.trim()))
            errors.push(`automake_table=${body.automake_table} Nome da tabela deve começar com letra ou underscore e conter apenas letras, números e underscores`)
        if (!body.automake_pk || typeof body.automake_pk !== 'string' || body.automake_pk.trim().length === 0)
            errors.push(`automake_pk=${body.automake_pk} Campo chave primária é obrigatório`)
        else if (body.automake_pk.length > 64) errors.push(`automake_pk=${body.automake_pk} Nome do campo chave primária muito longo (máximo 64 caracteres)`)
        else if (!/^[a-zA-ZÀ-ÿ_][a-zA-ZÀ-ÿ0-9_]*$/.test(body.automake_pk.trim()))
            errors.push(`automake_pk=${body.automake_pk} Campo chave primária deve começar com letra ou underscore e conter apenas letras, números e underscores`)
        if (!body.automake_connector) {
            errors.push(`automake_connector=${body.automake_connector} Conector é obrigatório`)
        } else {
            const connector_id = parseInt(body.automake_connector)
            if (isNaN(connector_id) || connector_id <= 0) errors.push(`automake_connector=${body.automake_connector} ID do conector deve ser um número positivo`)
        }
        if (body.automake_search && typeof body.automake_search === 'string') {
            const search_fields = body.automake_search
                .split(',')
                .map((f: string) => f.trim())
                .filter((f: string) => f.length > 0)
            for (const field of search_fields)
                if (!/^[a-zA-ZÀ-ÿ_][a-zA-ZÀ-ÿ0-9_]*$/.test(field)) errors.push(`automake_search=${body.automake_search} Campo de busca inválido: ${field}`)
        }
        if (body.automake_limit) {
            const limit = parseInt(body.automake_limit)
            if (isNaN(limit) || limit <= 0 || limit > 10000) errors.push(`automake_limit=${body.automake_limit} Limite deve ser um número entre 1 e 10000`)
        }
        if (body.automake_chroot_type !== undefined && body.automake_chroot_type !== '') {
            const chroot_type = parseInt(body.automake_chroot_type)
            if (isNaN(chroot_type) || chroot_type < 0 || chroot_type > 3)
                errors.push(`automake_chroot_type=${body.automake_chroot_type} Tipo de isolamento deve ser um número entre 0 e 3`)
        }
        const field_names = ['automake_ts', 'automake_uid', 'automake_gid', 'automake_chroot_site']
        for (const field_name of field_names) {
            const value = body[field_name]
            if (value && typeof value === 'string' && value.trim().length > 0)
                if (!/^[a-zA-ZÀ-ÿ_][a-zA-ZÀ-ÿ0-9_]*(\s+(ASC|DESC|asc|desc))?$/.test(value.trim())) errors.push(`Campo ${field_name} inválido: deve ser um nome de campo válido`)
        }
        const acl_fields = ['automake_enabled_add', 'automake_enabled_edit', 'automake_enabled_del', 'automake_enabled_list']
        for (const acl_field of acl_fields) {
            const value = body[acl_field]
            if (value && typeof value === 'string' && value.trim().length > 0)
                if (value.length > 100) errors.push(`${acl_field} muito longo (máximo 100 caracteres)`)
                else if (!/^[A-ZÀ-Ÿ_,\s]+$/.test(value.trim())) errors.push(`${acl_field} deve conter apenas letras maiúsculas, underscores e vírgulas`)
        }
        if (body.automake_ico && typeof body.automake_ico === 'string')
            if (body.automake_ico.length > 50) errors.push('Ícone muito longo (máximo 50 caracteres)')
            else if (!/^[a-zA-ZÀ-ÿ0-9\s\-_]+$/.test(body.automake_ico.trim())) errors.push('Ícone deve conter apenas letras, números, espaços, hífens e underscores')
        if (body.automake_desc_list && typeof body.automake_desc_list === 'string' && body.automake_desc_list.length > 500)
            errors.push(`automake_desc_list=${body.automake_desc_list} Descrição da lista muito longa (máximo 500 caracteres)`)
        if (body.automake_desc_form && typeof body.automake_desc_form === 'string' && body.automake_desc_form.length > 500)
            errors.push(`automake_desc_form=${body.automake_desc_form} Descrição do formulário muito longa (máximo 500 caracteres)`)
        if (body.automake_extra_url && typeof body.automake_extra_url === 'string' && body.automake_extra_url.trim().length > 0)
            if (body.automake_extra_url.length > 500) errors.push(`automake_extra_url=${body.automake_extra_url} URL extra muito longa (máximo 500 caracteres)`)
        if (body.automake_menu_extra && typeof body.automake_menu_extra === 'string' && body.automake_menu_extra.trim().length > 0) {
            if (body.automake_menu_extra.length > 2000) errors.push(`automake_menu_extra=${body.automake_menu_extra} Menu extra muito longo (máximo 2000 caracteres)`)
            const lines = body.automake_menu_extra.split('\n')
            for (let i = 0; i < lines.length; i++) {
                const line = lines[i].trim()
                if (line.length > 0) {
                    const parts = line.split(',')
                    if (parts.length < 2)
                        errors.push(`automake_menu_extra=${body.automake_menu_extra} Linha ${i + 1} do menu extra deve ter pelo menos 2 partes separadas por vírgula`)
                }
            }
        }
        return errors
    }
    async adm_automake_save(req: REQ, res: express.Response) {
        this.adm_automake_save_index++
        let self = this
        if (!req.user?.ACL_DEV && !req.user?.ACL_ADMIN) return res.json({error: 'SOMENTE PARA DESENVOLVEDOR.'})
        const validation_errors = this.validate_automake_fields(req.body)
        if (validation_errors.length > 0) {
            red(req.body)
            return res.json({error: 'ERRO DE VALIDAÇÃO', validation_errors: validation_errors.join('\n')})
        }
        const is_new = !req.body.automake_id
        const args: any = []
        args.push(req.site?.site_id)
        args.push(req.user?.gid)
        args.push(req.user?.uid)
        args.push(req.IP)
        args.push(req.body.automake_id)
        args.push(req.body.automake_name)
        args.push(req.body.automake_db)
        args.push(req.body.automake_table)
        args.push(req.body.automake_pk)
        args.push(req.body.automake_search)
        args.push(req.body.automake_status)
        args.push(req.body.automake_desc_list)
        args.push(req.body.automake_desc_form)
        args.push(req.body.automake_enabled_add)
        args.push(req.body.automake_enabled_edit)
        args.push(req.body.automake_enabled_del)
        args.push(req.body.automake_connector)
        args.push(req.body.automake_extra_url)
        args.push(req.body.automake_list_data)
        args.push(req.body.automake_view_data)
        args.push(req.body.automake_delete_data)
        args.push(req.body.automake_save_data)
        args.push(req.body.automake_chroot_site)
        args.push(req.body.automake_order_by)
        args.push(req.body.automake_ts)
        args.push(req.body.automake_uid)
        args.push(req.body.automake_gid)
        args.push(req.body.automake_chroot_type)
        args.push(req.body.automake_limit)
        args.push(req.body.automake_ico)
        args.push(req.body.automake_enabled_list)
        args.push(req.body.automake_pk_uuid)
        args.push(req.body.automake_menu_extra)
        const am_res = await this.db.call('adm_automake_save', args)
        if (am_res.is_err()) return res.json({error: am_res.error()})
        const am = am_res.gr()
        const automake_id = am.id
        if (is_new) {
            req.query.db = req.body.automake_db
            req.query.table = req.body.automake_table
            req.params.conector = req.body.automake_connector
            req.params.action = 'fields'
            const conector: number = parseInt(req.params.conector)
            if (!conector) return res.json({error: 'id do conector não encontrado'})
            const conexao = connectors[conector]
            if (!conexao) return res.json({error: 'Conector não encontrado'})
            const fields = await self.dtd(req.params.conector, 'fields', req.body.automake_db, req.body.automake_table)
            if (fields.error) return res.json({error: fields.error})
            let col = 0
            let j = 0
            for (let i in fields) {
                const f = fields[i]
                const id = f.id
                if (j > 10) {
                    j = 0
                    col++
                }
                j++
                if (req.body.automake_pk == id) continue
                if (req.body.automake_uid == id) continue
                if (req.body.automake_gid == id) continue
                if (req.body.automake_ts == id) continue
                if (req.body.automake_chroot_site == id) continue
                const size = f.size > 0 ? f.size : 0
                let type = f._type
                const value = f._default ? f._default : ''
                const help = f._comment ? f._comment : ''
                let label = id.toUpperCase()
                label = self.main.replace_all(label, '_', ' ')
                let config
                if (type == 'char' && size == 1) {
                    type = 'coma'
                    config = 'I=Inativo,A=Ativo'
                }
                let args = []
                args.push(req.user?.uid)
                args.push(req.IP)
                args.push(automake_id)
                args.push('')
                args.push(id)
                args.push(label)
                args.push('')
                args.push(i)
                args.push(type)
                args.push('')
                args.push('')
                args.push(1)
                args.push(value)
                args.push('')
                args.push('')
                args.push('')
                args.push('')
                args.push('')
                args.push('')
                args.push(size)
                args.push(config)
                args.push(help)
                args.push(0)
                args.push(0)
                args.push(0)
                args.push(0)
                args.push('')
                args.push(col)
                args.push('')
                args.push('')
                args.push('')
                args.push('')
                args.push(0)
                args.push('')
                this.db.call('adm_automake_field_save', args)
            }
            if (req.xhr) return res.json({message: 'Salvo #' + self.adm_automake_save_index})
            return self.main.redirect(req, res, '/crud/adm?/crud/adm=' + automake_id)
        } else {
            if (req.xhr) return res.json({message: 'Salvo #' + this.adm_automake_save_index})
            return self.main.redirect(req, res, '/crud/adm?/crud/adm=' + automake_id)
        }
    }
    async adm_automake_data(req: REQ, res: express.Response) {
        if (!req.user?.ACL_DEV) return res.json({error: 'SOMENTE PARA DESENVOLVEDOR.'})
        let r: any = {}
        r.primary_key = parseInt(req.params.automake_id) || parseInt(req.query.automake_id as string) || 0
        if (!r.primary_key || r.primary_key == 0) return res.json({error: 'automake_id não informado como parte da url.'})
        r.type = req.params.type
        const args = [req.user?.uid, req.IP, req.params.type, r.primary_key]
        const r_res = await this.db.call('adm_automake_get_editor', args)
        if (r_res.is_err()) return res.json({error: r_res.error()})
        const v = r_res.gr()
        const s1 = {type: r.type, id: r.primary_key, content: encode(v.src)}
        return res.json(s1)
    }
    async adm_automake_save_editor(req: REQ, res: express.Response) {
        if (!req.user?.ACL_DEV) return res.json({error: 'SOMENTE PARA DESENVOLVEDOR.'})
        const src = req.body.src
        const args = [req.user?.uid, req.IP, req.params.id, req.params.type, src]
        this.db.call('adm_automake_save_editor', args, res)
    }
    async adm_automake_fields_rm(req: REQ, res: express.Response) {
        if (!req.user?.ACL_DEV) return res.json({error: 'SOMENTE PARA DESENVOLVEDOR.'})
        let r: any = {}
        r.automake_id = parseInt(req.params.automake_id) || parseInt(req.query.automake_id as string) || 0
        if (!r.automake_id || r.automake_id == 0) return res.json({error: 'automake_id não informado como parte da url.'})
        r.field_id = req.params.field_id
        this.db.call('adm_automake_field_rm', [req.user?.uid, req.IP, r.field_id], res)
    }
    adm_automake_field_save(req: REQ, res: express.Response) {
        const automake_id = parseInt(req.params.automake_id) || parseInt(req.query.automake_id as string) || 0
        if (!automake_id || automake_id == 0) return res.json({error: 'automake_id não informado como parte da url.'})
        if (!req.user?.ACL_DEV) return res.json({error: 'SOMENTE PARA DESENVOLVEDOR.'})
        let args: any = []
        args.push(req.user?.uid)
        args.push(req.IP)
        args.push(automake_id)
        args.push(req.body.field_id)
        args.push(req.body.field_name)
        args.push(req.body.field_label.trim())
        args.push(req.body.field_list_sql.trim())
        args.push(req.body.field_order)
        args.push(req.body.field_type)
        args.push(req.body.field_show_on_list)
        args.push(req.body.field_show_on_form)
        args.push(req.body.field_required)
        args.push(req.body.field_default_value.trim())
        args.push(req.body.select_query.trim())
        args.push(req.body.field_select_db.trim())
        args.push(req.body.field_select_table.trim())
        args.push(req.body.field_select_label.trim())
        args.push(req.body.field_select_pk.trim())
        args.push(req.body.field_select_where.trim())
        args.push(req.body.field_max_len)
        args.push(req.body.field_config.trim())
        args.push(req.body.field_help.trim())
        args.push(req.body.field_ignore_empty)
        args.push(req.body.field_display_password)
        args.push(req.body.field_no_display_value)
        args.push(req.body.field_read_only)
        args.push(req.body.field_tab)
        args.push(req.body.field_col)
        args.push(req.body.field_row_link)
        args.push(req.body.field_alias)
        args.push(req.body.field_css_list)
        args.push(req.body.field_css_form)
        args.push(req.body.field_list_filter)
        args.push(req.body.field_select_alias)
        if (!req.body.automake_id) return res.json({result: 'error', message: '( ! req.automake_id )'})
        if (!req.body.field_name) return res.json({result: 'error', message: '( ! req.field_name )'})
        if (!req.body.field_label) return res.json({result: 'error', message: '( ! req.field_label )'})
        if (!req.body.field_type) return res.json({result: 'error', message: '( ! req.field_type )'})
        this.db.call('adm_automake_field_save', args, res)
    }
    validate_sql(sql: string): boolean {
        if (!sql || typeof sql !== 'string') return false
        const dangerous_patterns = [
            /;\s*DROP\s+/i,
            /;\s*DELETE\s+FROM\s+/i,
            /;\s*TRUNCATE\s+/i,
            /;\s*UPDATE\s+.*\s+SET\s+/i,
            /;\s*INSERT\s+INTO\s+/i,
            /UNION\s+SELECT/i,
            /OR\s+1\s*=\s*1/i,
            /--\s*$/,
            /\/\*.*\*\//,
            /;\s*EXEC\s+/i,
            /;\s*EXECUTE\s+/i,
            /xp_cmdshell/i,
            /sp_executesql/i,
            /INFORMATION_SCHEMA/i,
            /mysql\.user/i,
            /pg_catalog/i
        ]
        for (const pattern of dangerous_patterns)
            if (pattern.test(sql)) {
                red('SQL injection attempt detected:', sql)
                return false
            }
        return true
    }
    sanitize_identifier(identifier: string): string {
        if (!identifier || typeof identifier !== 'string') return ''
        return identifier.replace(/[^a-zA-Z0-9_]/g, '')
    }
    private check_rate_limit(ip: string): boolean {
        const now = Date.now()
        const key = ip
        const record = this.request_counts.get(key)
        if (!record) {
            this.request_counts.set(key, {count: 1, last_reset: now})
            return true
        }
        if (now - record.last_reset > this.RATE_LIMIT_WINDOW) {
            record.count = 1
            record.last_reset = now
            return true
        }
        if (record.count >= this.RATE_LIMIT_MAX_REQUESTS) {
            red(`Rate limit exceeded for IP: ${ip}`)
            return false
        }
        record.count++
        return true
    }
    private validate_request_size(req: REQ): boolean {
        const body_str = JSON.stringify(req.body || {})
        if (body_str.length > 1000000) {
            red('Request body too large:', body_str.length)
            return false
        }
        const query_str = JSON.stringify(req.query || {})
        if (query_str.length > 10000) {
            red('Query string too large:', query_str.length)
            return false
        }
        return true
    }
    async adm_automake_rm_handler(req: REQ, res: express.Response) {
        if (!req.user?.ACL_DEV) return res.json({error: 'SOMENTE PARA DESENVOLVEDOR.'})
        const id = parseInt(req.params.id || '0')
        const uid = req.user?.uid || 0
        const gid = req.user?.gid || 0
        try {
            const result = await this.db.call('adm_automake_rm', [gid, uid, id])
            if (result.is_err()) return res.send(result.error())
            this.main.redirect(req, res, '/crud/adm?op=list')
        } catch (error) {
            red('Error in adm_automake_rm_handler:', error)
            res.status(500).send('Internal server error')
        }
    }
    async adm_automake_field_save_handler(req: REQ, res: express.Response) {
        yellow('adm_automake_field_save_handler')
        if (!req.user?.ACL_DEV) return res.json({error: 'SOMENTE PARA DESENVOLVEDOR.'})
        const uid = req.user?.uid || 0
        const ip = req.IP || ''
        if (!req.body.automake_id) return res.json({error: '( ! req.automake_id )'})
        if (!req.body.field_name) return res.json({error: '( ! req.field_name )'})
        if (!req.body.field_label) return res.json({error: '( ! req.field_label )'})
        if (!req.body.field_type) return res.json({error: '( ! req.field_type )'})
        const args = [
            uid,
            ip,
            req.body.automake_id,
            req.body.field_id,
            req.body.field_name,
            req.body.field_label.trim(),
            req.body.field_list_sql.trim(),
            req.body.field_order,
            req.body.field_type,
            req.body.field_show_on_list,
            req.body.field_show_on_form,
            req.body.field_required,
            req.body.field_default_value.trim(),
            req.body.select_query.trim(),
            req.body.field_select_db.trim(),
            req.body.field_select_table.trim(),
            req.body.field_select_label.trim(),
            req.body.field_select_pk.trim(),
            req.body.field_select_where.trim(),
            req.body.field_max_len,
            req.body.field_config.trim(),
            req.body.field_help.trim(),
            req.body.field_ignore_empty,
            req.body.field_display_password,
            req.body.field_no_display_value,
            req.body.field_read_only,
            req.body.field_tab,
            req.body.field_col,
            req.body.field_row_link,
            req.body.field_alias,
            req.body.field_css_list,
            req.body.field_css_form,
            req.body.field_list_filter,
            req.body.field_select_alias
        ]
        try {
            const r_res = await this.db.call('adm_automake_field_save', args)
            if (r_res.is_err()) return res.send(r_res.error())
            const result = {message: 'Campo salvo com sucesso.', field_id: r_res.gr().field_id}
            return res.json(result)
        } catch (error) {
            red('Error in adm_automake_field_save_handler:', error)
            res.status(500).json({result: 'error', message: 'Internal server error'})
        }
    }
    async adm_automake_fields_of_handler(req: REQ, res: express.Response) {
        if (!req.user?.ACL_DEV) return res.json({error: 'SOMENTE PARA DESENVOLVEDOR.'})
        const uid = req.user?.uid || 0
        const arg_db = req.query.db
        const arg_table = req.query.table
        const args = [uid, arg_db, arg_table]
        try {
            const tree = await this.db.call('adm_automake_fields_of', args)
            return res.json(tree)
        } catch (error) {
            red('Error in adm_automake_fields_of_handler:', error)
            res.status(500).json({error: 'Internal server error'})
        }
    }
    async adm_automake_edit_form_handler(req: REQ, res: express.Response) {
        yellow('adm_automake_edit_form_handler')
        if (!req.user?.ACL_DEV) return res.json({error: 'SOMENTE PARA DESENVOLVEDOR.'})
        const id = req.query.id
        const uid = req.user?.uid || 0
        const ip = req.IP || ''
        const site_id = req.site?.site_id || 0
        const connectors_res = await this.db.call('automake_connectors', [uid])
        if (connectors_res.is_err()) return res.json({error: connectors_res.error()})
        const connectors = connectors_res.ga()
        const data_values_res = await this.db.call('adm_automake_edit', [uid, ip, id])
        if (data_values_res.is_err()) return res.json({error: data_values_res.error()})
        const data_values = data_values_res.gr()
        const adm_automake_res = await this.db.call('adm_automake', [uid, ip, site_id])
        if (adm_automake_res.is_err()) return res.json({error: adm_automake_res.error()})
        const adm_automake = adm_automake_res.ga()
        const acl_by_site_res = await this.db.call('acl_by_site', [site_id])
        if (acl_by_site_res.is_err()) return res.json({error: acl_by_site_res.error()})
        const acl_by_site = acl_by_site_res.ga()
        let r: any = {
            primary_key: id,
            automake_id: id,
            connectors,
            data_values,
            adm_automake,
            tables: [],
            op: req.query.op,
            acl_by_site,
            adm_automake_headers: ''
        }
        if (!req.xhr) {
            const adm_automake_headers = await ejs.renderFile(path.join(__dirname, '..', 'views', 'adm_automake_headers.ejs'), r)
            r.adm_automake_headers = adm_automake_headers
        }
        return this.main.display(req, res, 'adm', 'adm_automake_edit_form', r)
    }
    async adm_automake_edit_handler(req: REQ, res: express.Response) {
        if (!req.user?.ACL_DEV) return res.json({error: 'SOMENTE PARA DESENVOLVEDOR.'})
        const id = parseInt((req.query.automake_id as string) || '0')
        if (!id) return res.json({error: 'ID não informado.'})
        const uid = req.user?.uid || 0
        const site_id = req.site?.site_id || 0
        const connectors = await this.db.call('automake_connectors', [uid])
        const acl_by_site = await this.db.call('acl_by_site', [site_id])
        let r: any = {
            primary_key: id,
            automake_id: id,
            connectors,
            acl_by_site
        }
        const data_values_res = await this.db.call('adm_automake_edit', [req.user.uid, req.IP, id])
        if (data_values_res.is_err()) return res.json({error: data_values_res.error()})
        r.data_values = data_values_res.ga()
        const adm_automake_res = await this.db.call('adm_automake', [req.user.uid, req.IP, id])
        if (adm_automake_res.is_err()) return res.json({error: adm_automake_res.error()})
        r.adm_automake = adm_automake_res.ga()
        return this.main.display(req, res, 'adm', 'adm_automake_edit', r)
    }
    async adm_automake_add_handler(req: REQ, res: express.Response) {
        if (!req.user?.ACL_DEV) return res.json({error: 'SOMENTE PARA DESENVOLVEDOR.'})
        const uid = req.user?.uid || 0
        const site_id = req.site?.site_id || 0
        const connectors = await this.db.call('automake_connectors', [uid])
        const tables = await this.db.call('automake_tables', [uid, 'site'])
        const acl_by_site = await this.db.call('acl_by_site', [site_id])
        const r = {
            automake_id: 0,
            connectors,
            op: req.query.op,
            acl_by_site,
            tables,
            data_values: {
                automake_db: 'site',
                automake_ico: 'fas fa-cogs',
                automake_status: 'FUNCIONARIOS',
                automake_enabled_list: 'FUNCIONARIOS',
                automake_enabled_add: 'FUNCIONARIOS',
                automake_enabled_del: 'FUNCIONARIOS',
                automake_enabled_edit: 'FUNCIONARIOS'
            },
            adm_automake: []
        }
        return this.main.display(req, res, 'adm', 'adm_automake_edit', r)
    }
    private validate_bulk_operation(req: REQ, max_items = 100): {valid: boolean; error?: string} {
        const items = req.body.items || req.body.ids || []
        if (!Array.isArray(items)) return {valid: false, error: 'Dados de operação em lote inválidos.'}
        if (items.length > max_items) return {valid: false, error: `Máximo de ${max_items} itens permitidos por operação.`}
        for (const item of items) {
            if (typeof item !== 'string' && typeof item !== 'number') return {valid: false, error: 'IDs de itens devem ser strings ou números.'}
            if (typeof item === 'string' && item.length > 50) return {valid: false, error: 'ID de item muito longo.'}
        }
        return {valid: true}
    }
    private detect_automated_requests(req: REQ): boolean {
        const user_agent = req.headers['user-agent'] || ''
        const suspicious_patterns = [/bot/i, /crawler/i, /spider/i, /scraper/i, /curl/i, /wget/i, /python/i, /java/i, /php/i, /perl/i, /ruby/i]
        for (const pattern of suspicious_patterns) if (pattern.test(user_agent)) return true
        const browser_headers = ['accept', 'accept-language', 'accept-encoding']
        const missing_headers = browser_headers.filter(header => !req.headers[header])
        return missing_headers.length > 1
    }
    private validate_request_origin(req: REQ): boolean {
        const origin = req.headers.origin
        const referer = req.headers.referer
        const host = req.headers.host
        if (origin && host)
            try {
                const origin_url = new URL(origin)
                return origin_url.host === host
            } catch {
                return false
            }
        if (referer && host)
            try {
                const referer_url = new URL(referer)
                return referer_url.host === host
            } catch {
                return false
            }
        return true
    }
    private validate_table_access(req: REQ, table_name: string): boolean {
        const user_role = req.user?.role || 'guest'
        const restricted_tables = ['users', 'user_auth_log', 'api_help', 'site_config']
        if (restricted_tables.includes(table_name) && user_role !== 'admin' && !req.user?.ACL_DEV) return false
        return true
    }
    private log_suspicious_activity(req: REQ, activity: string, details: any): void {
        const log_entry = {
            timestamp: new Date().toISOString(),
            ip: req.IP,
            user_id: req.user?.uid,
            user_agent: req.headers['user-agent'],
            activity: activity,
            details: details,
            session_id: req.sessionID
        }
        red('SUSPICIOUS ACTIVITY:', JSON.stringify(log_entry))
        this.db.call('ds_sql_err_log', [req.sid || 0, req.gid || 0, req.uid || 0, req.IP || '', 'SECURITY_ALERT', activity, JSON.stringify(details), '', '', '', '']).catch(err => {
            red('Failed to log suspicious activity:', err)
        })
    }
    async adm_dtd(req: REQ, res: express.Response) {
        const action = req.params.action as DTD_ACTIONS
        const conector = parseInt(req.params.conector) || 0
        if (conector == 0) return res.json({error: 'Conector não informado.'})
        const db = req.query.db as string
        const table = req.query.table as string
        const sql = req.query.sql as string
        const result = await this.dtd(conector, action, db, table, sql)
        return res.json(result)
    }
    async adm_automake(req: REQ, res: express.Response) {
        if (!req.user?.ACL_DEV) return res.json({error: 'SOMENTE PARA DESENVOLVEDOR.'})
        const op = req.query.op || req.params.action
        yellow('adm_automake', op)
        if (op == 'rm') return this.adm_automake_rm_handler(req, res)
        else if (op == 'field_save') return this.adm_automake_field_save_handler(req, res)
        else if (op == 'fields_of') return this.adm_automake_fields_of_handler(req, res)
        else if (op == 'edit_form') return this.adm_automake_edit_form_handler(req, res)
        else if (op == 'edit') return this.adm_automake_edit_handler(req, res)
        else if (op == 'add') return this.adm_automake_add_handler(req, res)
        else return this.adm_automake_list_handler(req, res)
    }
    async adm_automake_fields_list(req: REQ, res: express.Response) {
        let self = this
        if (!req.user?.ACL_DEV) return res.json({error: 'SOMENTE PARA DESENVOLVEDOR.'})
        let r: any = {}
        r.automake_id = parseInt(req.params.automake_id) || parseInt(req.query.automake_id as string) || 0
        if (!r.automake_id) return res.json({error: 'automake_id NÃO INFORMADO.'})
        const fields_res = await this.db.call('adm_automake_fields', [req.user?.uid, req.IP, r.automake_id])
        if (fields_res.is_err()) return res.json({error: fields_res.error()})
        r.fields = fields_res.ga()
        r.edited = req.query.edited || 0
        self.main.display(req, res, 'adm', 'adm_automake_fields_list', r)
    }
    async adm_automake_fields_form(req: REQ, res: express.Response) {
        let self = this
        if (!req.user?.ACL_DEV) return res.json({error: 'SOMENTE PARA DESENVOLVEDOR.'})
        let r: any = {}
        r.automake_id = parseInt(req.params.automake_id) || parseInt(req.query.automake_id as string) || 0
        if (!r.automake_id || r.automake_id == 0) return res.json({error: 'automake_id não informado como parte da url.'})
        r.field_id = req.params.field_id
        const fields_res = await this.db.call('adm_automake_fields', [req.user?.uid, req.IP, r.automake_id])
        if (fields_res.is_err()) return res.json({error: fields_res.error()})
        r.fields = fields_res.ga()
        const field_res = await this.db.call('adm_automake_field_by_id', [req.user?.uid, req.IP, r.automake_id, r.field_id])
        if (field_res.is_err()) return res.json({error: field_res.error()})
        r.field = field_res.gr()
        yellow('adm_automake_fields_form', r.field)
        const acl_by_site_res = await this.db.call('acl_by_site', [req.site?.site_id])
        if (acl_by_site_res.is_err()) return res.json({error: acl_by_site_res.error()})
        r.acl_by_site = acl_by_site_res.ga()
        let adm_automake_headers = ''
        if (!req.xhr) adm_automake_headers = await ejs.renderFile(path.join(__dirname, '..', 'views', 'adm_automake_headers.ejs'), r)
        r.adm_automake_headers = adm_automake_headers
        return self.main.display(req, res, 'adm', 'adm_automake_fields_form', r)
    }
    async adm_automake_list_handler(req: REQ, res: express.Response) {
        if (!req.user?.ACL_DEV) return res.json({error: 'SOMENTE PARA DESENVOLVEDOR.'})
        const uid = req.user?.uid || 0
        const ip = req.IP || ''
        const site_id = req.site?.site_id || 0
        const tree = await this.db.call('adm_automake', [uid, ip, site_id])
        let r: any = {tree, data_values: {}, automake_id: 0}
        if (!req.xhr) r.adm_automake_headers = await ejs.renderFile(path.join(__dirname, '..', 'views', 'adm_automake_headers.ejs'), r)
        return this.main.display(req, res, 'adm', 'adm_automake', r)
    }
    public app_view(req: REQ, res: express.Response) {
        const app_name = req.params.app_name
        if (!app_name) return res.json({error: 'app_view: APLICAÇÃO INVÁLIDA'})
        try {
            this.app_load(req, res, app_name)
        } catch (err: any) {
            const error = err.toString()
            red('app_view error', error)
            return res.json({error})
        }
    }
    public app_load(req: REQ, res: express.Response, automake_name: string) {
        if (!automake_name) return res.json({error: 'app_view: APLICAÇÃO INVÁLIDA'})
        if (!req.global) {
            red('error !req.global')
            return res.json({error: 'APLICAÇÃO [' + automake_name + '] NÃO ENCONTRADA'})
        }
        const is_public = automake_name.includes('public_') || automake_name.includes('_doc_')
        const is_auth: boolean = req.user && req.user.uid ? true : false
        const acl: string[] = req.user && req.user.acl ? req.user.acl : []
        const is_dev: boolean = is_auth && acl.indexOf('ACL_DEV') !== -1 ? true : false
        const is_adm: boolean = is_auth && acl.indexOf('ACL_ADMIN') !== -1 ? true : false
        const is_tr: boolean = is_auth && acl.indexOf('ACL_TRANSLATOR') !== -1 ? true : false
        if (automake_name.includes('adm'))
            if (!is_dev && !is_adm) {
                red('acl:', is_dev, is_adm)
                red('user:', req.user)
                return res.json({error: 'APLICAÇÃO [' + automake_name + '] ERR-ADMIN-ONLY'})
            }
        if (automake_name.includes('adm_tr')) if (!is_dev && !is_adm && !is_tr) return res.json({error: 'APLICAÇÃO [' + automake_name + '] ERR-TR-ONLY'})
        if (automake_name.includes('dev')) if (!is_dev) return res.json({error: 'APLICAÇÃO [' + automake_name + '] ERR-DEV-ONLY'})
        if (automake_name.includes('user')) if (!is_auth) return res.json({error: 'APLICAÇÃO [' + automake_name + '] NÃO AUTORIZADA'})
        if (!req.site) red('error !req.site')
        let r = req.site || {}
        r.site_url = 'https://' + req.headers.host
        for (let i in req.site) r[i] = req.site[i]
        r.automake_add = this.main.automake_add
        r.is_public = is_public
        r.is_auth = is_auth
        r.is_dev = is_dev
        r.is_adm = is_adm
        r.is_tr = is_tr
        try {
            const file_js = path.join(__dirname, '..', 'views', automake_name + '.js')
            if (fs.existsSync(file_js)) {
                const code = fs.readFileSync(file_js, 'utf8')
                this.app_exec(req, res, automake_name, r, code)
            } else {
                r.tree = []
                return this.main.display(req, res, 'adm', automake_name, r)
            }
        } catch (err: any) {
            const error = err.toString()
            red('eval try error', error)
            return res.json({error: 'APLICAÇÃO [' + automake_name + '] HOUVE ERRO DE EXECUÇÃO: ' + error})
        }
    }
    app_exec(req: REQ, res: express.Response, automake_name: string, r: any, eval_js: string) {
        if (!automake_name) return res.json({error: 'APLICAÇÃO [' + automake_name + '] INVÁLIDA'})
        if (!eval_js) return res.json({error: 'APLICAÇÃO [' + automake_name + '] INVÁLIDA'})
        const self = this
        let rds_debug: any[] = []
        function rds_debug_add(rds_id: any, rds_args: any, rds_res: any) {
            if (!req.user || (req.user.gid != 2 && req.user.gid != 3)) return
            const str = 'CALL ' + rds_id + " ('" + rds_args.join("', '") + "') "
            const json = JSON.stringify(rds_res ? rds_res[0] : [], undefined, 4)
            rds_debug.push({str: str, tree: json})
        }
        let ga = async function (ga_sp_id: string, args: any, cb: (tree: any) => void) {
            if (!self.main.security(req, res, ga_sp_id)) return false
            const r_res = await self.main.db.call(ga_sp_id, args)
            if (r_res.is_err()) return {error: r_res.error(), tree: []}
            const tree = r_res.ga()
            rds_debug_add(ga_sp_id, args, tree)
            if (tree && tree[0] && tree[0].err) {
                red('CALL RESULT ERR> ', ga_sp_id, args, tree[0].err)
                return {error: tree[0].err, tree: []}
            }
            if (cb) return cb(tree)
        }
        let gr = async function (gr_sp_id: string, args: any, cb: (tree: any) => void) {
            if (!self.main.security(req, res, gr_sp_id)) return false
            const r_res = await self.main.db.call(gr_sp_id, args)
            if (r_res.is_err()) return {error: r_res.error(), tree: []}
            const tree = r_res.gr()
            rds_debug_add(gr_sp_id, args, tree)
            if (tree && tree[0] && tree[0].err) {
                red('CALL RESULT ERR> ', gr_sp_id, args, tree[0].err)
                return {error: tree[0].err, tree: []}
            }
            if (cb) return cb(tree)
        }
        const uid = req.user ? req.user.uid : false
        const gid = req.user ? req.user.gid : false
        r = r || {}
        r.rds_debug = rds_debug
        r.automake_name = automake_name
        r.uid = uid
        r.gid = gid
        r.app = automake_name
        r.tree = []
        r.q = req.query
        r.query = req.query
        r.post = req.body
        r.redirect = (req: REQ, res: express.Response, uri: string) => self.main.redirect(req, res, uri)
        r.DUMP = (o: any) => res.send(self.main.debug_dump(o))
        r.DUMP_FORM = (o: any) => res.send(self.main.debug_dump_form(o, automake_name))
        r.FORM_TABLE = (main: Main, tbl: any) => self.main.form_table_gen(main, tbl, automake_name, res)
        r.convert = (str: string, lang: string, maintain_case: boolean) => self.main.convert(str, lang, maintain_case)
        r.whatsapp = (num: string) => self.main.whatsapp(num)
        r.datepicker = (id: string, fctl: any, label: string, ds: string, de: string) => self.main.datepicker(id, fctl, label, ds, de)
        r.date_format = (num: any, f: any) => self.main.date_format(num, f)
        r.humanize = (num: any) => self.main.humanize(num)
        r.moment = (num: string) => self.main.moment(num)
        r.FORM = (app: string, on_submit: any) => {
            red('FORM_ACTION=' + app + ' on_submit=' + on_submit)
            self.main.debug_form(req, app, on_submit)
        }
        r.ga = ga
        r.gr = gr
        r.g = (k: string, d: any = '') => {
            if (!d) d = ''
            let v = req.body[k] ? req.body[k] : req.query[k]
            return v ? v : d
        }
        r.gi = (k: string) => {
            const v = r.g(k)
            return v ? v : 0
        }
        const g = (k: string, d: any = '') => {
            if (!d) d = ''
            const v = req.body[k] ? req.body[k] : req.query[k]
            return v ? v : d
        }
        r.g = g
        r.gi = (k: string) => {
            const v = g(k)
            return v ? v : 0
        }
        r.image_save = (buffer: any, name: string) => self.main.image_save(buffer, name, req.site?.site_id || 0, uid)
        r.moeda = (num: string) => self.main.moeda(num)
        r.upload_path = req.upload_path
        r.upload_url = req.upload_url
        let db: DB = self.main.db
        db
        let io: Main = self.main
        io
        let stdlib: Main = self.main
        stdlib
        let tr: Main = self.main
        tr
        const oid: number = req.site?.site_id || 0
        oid
        const sid: number = req.site?.site_id || 0
        sid
        //eslint-disable-next-line @typescript-eslint/naming-convention
        const _sid: number = req.site?.site_id || 0
        _sid
        //eslint-disable-next-line @typescript-eslint/naming-convention
        const _uid: number = req.user?.uid || 0
        _uid
        //eslint-disable-next-line @typescript-eslint/naming-convention
        const _gid: number = req.user?.gid || 0
        _gid
        const ip: string = req.IP || ''
        ip
        //eslint-disable-next-line @typescript-eslint/naming-convention
        const _ip: string = req.IP || ''
        _ip
        const fctl_js = `(function(){
            try {
                ${eval_js}
            } catch (e) {
                console.error('Error executing eval_js:', e.message);
                throw e;
            }
        })()`
        try {
            bg_green(`app_exec: ${automake_name}`)
            bg_cyan(eval_js)
            eval(fctl_js)
        } catch (e: any) {
            bg_yellow(fctl_js)
            const strerr = e.message
            bg_red(strerr)
            if (res.headersSent) {
                red('Headers already sent, cannot send app_exec error response')
                return
            }
            try {
                return res.status(500).json({error: strerr, message: 'Application execution failed'})
            } catch (err: any) {
                const error = err.message
                red('Failed to send app_exec error response:', error)
            }
        }
    }
    app_page_adm(req: REQ, res: express.Response) {
        req.query.page_name = req.params.page_name
        return this.app_load(req, res, 'adm_page')
    }
    app_page_public(req: REQ, res: express.Response) {
        req.query.page_name = req.params.page_name
        return this.app_load(req, res, 'public_page')
    }
}
