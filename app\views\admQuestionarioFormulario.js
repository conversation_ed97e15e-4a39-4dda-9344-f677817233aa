const app_save_data = app
const app_rm_data = app
const app_edit_data = app
const app_title = '<PERSON><PERSON><PERSON>s'
const app_desc = '--'
const adm = req.user.ACL_DEV || req.user.ACL_ADMIN ? 1 : 0
r.op = req.query.op
r.app_save_data = app_save_data

let QUESTOES
let QUESTOES_T
let RESPOSTAS
let RESPOSTA_ID
let RESPOSTAS_T
r.FONTS = [
    'FONT_SANS_32_WHITE',
    'FONT_SANS_8_BLACK',
    'FONT_SANS_10_BLACK',
    'FONT_SANS_12_BLACK',
    'FONT_SANS_14_BLACK',
    'FONT_SANS_16_BLACK',
    'FONT_SANS_32_BLACK',
    'FONT_SANS_64_BLACK',
    'FONT_SANS_128_BLACK',
    'FONT_SANS_8_WHITE',
    'FONT_SANS_16_WHITE',
    'FONT_SANS_32_WHITE',
    'FONT_SANS_64_WHITE',
    'FONT_SANS_128_WHITE'
]

function salvar_resposta(id, i, q, j) {
    if (j < RESPOSTAS_T) {
        const resposta = RESPOSTAS[j]
        const resposta_id = RESPOSTA_ID[j] ? RESPOSTA_ID[j] : 0
        gr(
            'quiz.adm_resposta_salvar',
            [adm, q, resposta, j, resposta_id],
            function (row) {
                j++
                return salvar_resposta(id, i, q, j)
            },
            true
        )
    } else {
        return salvar_questao(id, i)
    }
}

function salvar_questao(id, i) {
    if (i < QUESTOES_T) {
        const q = QUESTOES[i]
        const QUESTAO = q.QUESTAO
        const QUESTAO_ID = req.body.QUESTAO_ID ? req.body.QUESTAO_ID[i] : 0
        RESPOSTAS = q.RESPOSTA
        RESPOSTA_ID = q.RESPOSTA_ID ? q.RESPOSTA_ID : 0
        RESPOSTAS_T = RESPOSTAS.length
        const OK = q.OK
        const args = [adm, id, QUESTAO, i, OK, QUESTAO_ID]
        gr(
            'quiz.adm_questao_salvar',
            args,
            function (row) {
                i = row._posicao + 1
                const q = row.id
                return salvar_resposta(id, i, q, 0)
            },
            true
        )
    } else {
        return finalizado(id)
    }
}
let click_src = '0'
let CATEGORIAS
function categoria_salvar(id, i) {
    if (i < CATEGORIAS.length) {
        const cat = CATEGORIAS[i]
        gr(
            'quiz.adm_questao_cat_salvar',
            [adm, id, cat, i],
            function (row) {
                i = row._posicao + 1
                return categoria_salvar(id, i)
            },
            true
        )
    } else {
        let url = '/app/' + app + '?id=' + id
        if (click_src == 'p') url = '/b/edited/s/' + id
        return tr.redirect(req, res, url)
    }
}

function finalizado(id) {
    CATEGORIAS = req.body.CATEGORIAS.split(' ')
    return categoria_salvar(id, 0)
}

if (req.query.op == 'save') {
    let args = []
    click_src = req.body.click_src
    const TITULO = req.body.TITULO
    const DESCRICAO = req.body.DESCRICAO
    const URL = io.convert(TITULO)
    args.push(adm)
    args.push(uid)
    args.push(req.IP)
    args.push(req.site.site_id)
    args.push(req.body.id)
    args.push(URL)
    args.push(TITULO)
    args.push(DESCRICAO)
    args.push(req.body.CATEGORIAS)
    args.push(req.body.questionario_imagem)
    QUESTOES = req.body.QUESTOES || []
    QUESTOES_T = QUESTOES.length

    gr(
        'quiz.adm_questionarios_salvar',
        args,
        function (row) {
            return salvar_questao(row.id, 0)
        },
        true
    )
} else if (req.query.op == 'image_tr') {
    return main.image_blur_and_text(req, res, req.body)
} else if (req.query.op == 'image') {
    if (!req.files) return res.send('erro: sem imagem.')
    return main.upload(req, res, 'IMAGEM', 480, 90)
} else if (req.query.op == 'status') {
    const args = [adm, uid, ip, req.site.site_id, req.body.id, req.body.s]
    ga(
        'quiz._adm_questionario_ativo',
        args,
        function (tree) {
            console.log(automake_name, req.query.op, tree)
            return res.json(tree)
        },
        true
    )
} else if (req.query.op == 'status2') {
    const args = [adm, uid, ip, req.site.site_id, req.body.id, req.body.s]
    ga(
        'quiz._adm_questionario_status',
        args,
        function (tree) {
            console.log(automake_name, req.query.op, tree)
            return res.json(tree)
        },
        true
    )
} else if (req.query.op == 'rm') {
    const args = [adm, uid, req.IP, req.site.site_id, req.body.id]
    ga(
        'quiz._adm_questionario_rm',
        args,
        function (tree) {
            console.log(automake_name, req.query.op, tree)
            return res.json(tree)
        },
        true
    )
} else if (req.query.op == 'new') {
    r.app_title = 'EDITOR DE QUESTIONÁRIOS'
    r.app_desc = '- editor de teste -'
    r.r = {}
    return tr.display_adm(req, res, automake_name, r)
} else {
    const id = req.query.id ? req.query.id : '0'
    ga(
        'quiz.adm_questionarios',
        [adm, uid, req.IP, req.site.site_id, id],
        function (tree) {
            r.click_src = req.query.src
            r.app_save_data = app_save_data
            r.app_rm_data = app_rm_data
            r.app_edit_data = app_edit_data
            r.app_title = app_title
            r.app_desc = app_desc
            r.tree = tree
            r.r = {}
            r.id = id
            if (id && tree[0]) r.r = tree[0]
            return tr.display_adm(req, res, automake_name, r)
        },
        true
    )
}
