let ds = req.query.ds
let de = req.query.de
if (!ds) ds = moment(new Date()).subtract(2, 'days').format('YYYY-MM-DD')
if (!de) de = moment(new Date()).format('YYYY-MM-DD')
ds = moment(ds).format('YYYY-MM-DD')
de = moment(de).format('YYYY-MM-DD')
r.ds = ds
r.de = de

const equipe = req.query.equipe ? req.query.equipe : 0

ga('api_account_formacao_relatorio', [sid, gid, uid, ip, equipe, ds, de], function (tree) {
    r.tree = tree
    return tr.display_adm(req, res, automake_name, r)
})
