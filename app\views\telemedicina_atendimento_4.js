// DECLARAÇ&#213;ES DAS FUNÇ&#213;ES

// VERIFICO SE O ID VEIO DA PÁGINA ANTERIOR

const iniciaInterface = function () {
    if (req.query.editaDados === '1')
        // RECEBEMOS A FLAG PARA EDITAR
        carregaAtendimentoId()
    else if (req.query.novoAtendimento === '1')
        // RECEBEMOS A FLAG PARA NOVO ATENDIMENTO
        carregaUltimoAtendimento()
    else if (req.body.id && req.body.id !== '0')
        // SALVANDO DADOS RECUPERADOS
        adicionaDadosClinicos() // O USUÁRIO NÃO PODE CARREGAR A PÁGINA DIRETO, DEPOIS EU TRATO ISSO
    else carregaUltimoAtendimento()
}

// CASO VENHA O ID, EU CARREGO OS DADOS USANDO O ID DO ANTEIMENTO
// CASO NÃO TENHA ID, CARREGO O ÚLTIMO (ACABAMOS DE ADICIONAR UM NOVO ATENDIMENTO)

const carregaAtendimentoId = function () {
    ga('telemedicina.api_verifica_triagem', [sid, gid, uid, ip, req.query.id], function (chamado) {
        r.chamado = chamado
        return tr.display_adm(req, res, automake_name, r)
    })
}

const carregaUltimoAtendimento = function () {
    ga('telemedicina.api_verifica_triagem_usuario', [sid, gid, uid, ip, 1, 1], function (chamado) {
        // VERIFICA COM WENDEL COMO PEGO O ID DO USER LOGADO

        r.chamado = chamado
        return tr.display_adm(req, res, automake_name, r)
        //return res.json(chamado);
    })
}

const adicionaDadosClinicos = function () {
    const args = [sid, gid, uid, ip, req.body.glasgow1, req.body.glasgow2, req.body.glasgow3, req.body.id]
    gr(
        'telemedicina.api_triagem_passo_4',
        args,
        function (row) {
            return tr.redirect(req, res, '/app/telemedicina_lista')
        },
        true
    )
}

iniciaInterface()
