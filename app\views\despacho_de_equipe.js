const regiao = req.query.regiao ? req.query.regiao : 0
ga(
    'api_account_despacho_regioes',
    [sid, gid, uid, ip],
    function (regioes) {
        r.regioes = regioes
        ga(
            'api_account_despacho_equipes',
            [sid, gid, uid, ip, regiao],
            function (tree) {
                r.tree = tree
                return tr.display_adm(req, res, automake_name, r)
            },
            true
        )
    },
    true
)
