let ds = req.query.ds
let de = req.query.de
if (!ds) ds = moment(new Date()).subtract(2, 'days').format('YYYY-MM-DD')
if (!de) de = moment(new Date()).format('YYYY-MM-DD')
ds = moment(ds).format('YYYY-MM-DD')
de = moment(de).format('YYYY-MM-DD')
r.ds = ds
r.de = de
ga('api_account_relatorio_transporte_por_motivo', [sid, gid, uid, ip, ds, de], function (tree) {
    r.tree = tree
    return tr.display_adm(req, res, automake_name, r)
})
