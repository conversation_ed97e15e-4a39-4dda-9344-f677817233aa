const id = req.query.id > 0 ? req.query.id : 0
const setor = req.query.setor > 0 ? req.query.setor : 14
let ds = req.query.ds
let de = req.query.de
if (!ds) ds = moment(new Date()).subtract(2, 'month').format('YYYY-MM-DD')
if (!de) de = moment(new Date()).format('YYYY-MM-DD')
ds = moment(ds).format('YYYY-MM-DD')
de = moment(de).format('YYYY-MM-DD')

gr('financeiro.api_account_financeiro_pedido_por_id', [oid, gid, uid, req.IP, id], function (pedido) {
    r.pedido = pedido
    return tr.display_adm(req, res, automake_name, r)
})
