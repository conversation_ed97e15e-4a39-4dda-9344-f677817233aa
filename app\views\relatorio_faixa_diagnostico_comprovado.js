let ds = req.query.ds
let de = req.query.de
if (!ds) ds = moment(new Date()).subtract(2, 'days').format('YYYY-MM-DD')
if (!de) de = moment(new Date()).format('YYYY-MM-DD')
ds = moment(ds).format('YYYY-MM-DD')
de = moment(de).format('YYYY-MM-DD')
r.ds = ds
r.de = de
const tipo = req.query.tipo ? req.query.tipo : 0
if (!tipo) {
    return res.send('Erro: tipo não informado.')
}
gr('api_account_tipo_de_diagnostico_por_id', [sid, gid, uid, ip, tipo], function (row) {
    r.tipo = tipo
    r.tipo_info = row
    ga('api_account_relatorio_diagnostico_comprovado', [sid, gid, uid, ip, ds, de, tipo], function (tree) {
        r.tree = tree
        return tr.display_adm(req, res, automake_name, r)
    })
})
