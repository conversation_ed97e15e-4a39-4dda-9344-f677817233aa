// DECLARAÇ&#213;ES DAS FUNÇ&#213;ES

// VERIFICO SE O ID VEIO DA PÁGINA ANTERIOR

const iniciaInterface = function () {
    carregaAtendimentoId()
}

// CASO VENHA O ID, EU CARREGO OS DADOS USANDO O ID DO ANTEIMENTO
// CASO NÃO TENHA ID, CARREGO O ÚLTIMO (ACABAMOS DE ADICIONAR UM NOVO ATENDIMENTO)

const carregaAtendimentoId = function () {
    ga('telemedicina.api_verifica_triagem', [sid, gid, uid, ip, req.query.id], function (chamado) {
        r.chamado = chamado

        ga('telemedicina.api_verifica_atendimento_triagem', [sid, gid, uid, ip, req.query.id], function (atendimento) {
            r.atendimento = atendimento

            ga('telemedicina.api_calcula_diagnostico', [sid, gid, uid, ip, req.query.id], function (diagnostico) {
                r.diagnostico = diagnostico

                // POC ITEM *******.3
                // POC ITEM *******.4
                r.orientacao = calculaOrientacao(diagnostico)

                ga('telemedicina.api_unidades_saude', [sid, gid, uid, ip], function (unidades) {
                    // POC ITEM *******.5

                    r.unidades = unidades
                    return tr.display_adm(req, res, automake_name, r)
                })
            })
        })
    })
}

const calculaOrientacao = function (diagnostico) {
    const TRIAGEM_PERGUNTA_PESO = diagnostico.TRIAGEM_PERGUNTA_PESO
    const PESO_MIN = diagnostico.PESO_MIN
    const PESO_MAX = diagnostico.PESO_MAX

    let orientacao = 'Calculando orientação'

    if (TRIAGEM_PERGUNTA_PESO < PESO_MIN) orientacao = 'Paciente sem risco'
    else if (TRIAGEM_PERGUNTA_PESO > PESO_MAX) orientacao = 'Paciente com risco'
    else orientacao = 'Paciente com n&#237;veis aceitaveis para o atendimento'

    return orientacao
}

iniciaInterface()
