// Automake Inline Editing Functions
let currentEditModal = null

function automake_inline_edit(element) {
    const field = element.getAttribute('data-field')
    const fieldType = element.getAttribute('data-field-type')
    const pk = element.getAttribute('data-pk')
    const app = element.getAttribute('data-app')

    if (!field || !pk || !app) {
        console.error('Missing required data attributes for inline editing')
        return
    }

    // Get current field data
    fetch(`/crud/field/${app}/${pk}/${field}`, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showInlineEditModal(data.result, element)
            } else {
                alert('Erro ao carregar dados do campo: ' + (data.message || 'Erro desconhecido'))
            }
        })
        .catch(error => {
            console.error('Error fetching field data:', error)
            alert('Erro ao carregar dados do campo')
        })
}

function showInlineEditModal(fieldData, element) {
    const {value, field_config, pk} = fieldData
    const fieldType = field_config.type
    const fieldLabel = field_config.label || field_config.field_label || field_config.name
    const required = field_config.required
    const maxLen = field_config.field_max_len || field_config.max_len

    // Create modal HTML
    const modalId = 'automake-inline-edit-modal'
    let existingModal = document.getElementById(modalId)
    if (existingModal) {
        existingModal.remove()
    }

    const modalHtml = `
        <div class="modal fade" id="${modalId}" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Editar Campo: ${fieldLabel}</h5>
                        <button type="button" class="close" data-dismiss="modal">
                            <span>&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form id="inline-edit-form">
                            <div class="form-group">
                                <label for="field-value">${fieldLabel} ${required ? '<span class="text-danger">*</span>' : ''}</label>
                                ${generateFieldInput(fieldType, value, maxLen, field_config)}
                                ${maxLen ? `<small class="form-text text-muted">Máximo ${maxLen} caracteres</small>` : ''}
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar</button>
                        <button type="button" class="btn btn-primary" onclick="saveInlineEdit('${element.getAttribute('data-app')}', '${pk}', '${field_config.name}', this)">Salvar</button>
                    </div>
                </div>
            </div>
        </div>
    `

    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalHtml)

    // Show modal
    const modal = document.getElementById(modalId)
    currentEditModal = {
        modal: modal,
        element: element,
        originalValue: value
    }

    // Use Bootstrap modal if available, otherwise show with basic display
    if (typeof $ !== 'undefined' && $.fn.modal) {
        $(modal).modal('show')
        $(modal).on('hidden.bs.modal', function () {
            modal.remove()
            currentEditModal = null
        })
    } else {
        modal.style.display = 'block'
        modal.classList.add('show')
    }

    // Focus on input
    const input = modal.querySelector('#field-value')
    if (input) {
        input.focus()
        if (input.type === 'text' || input.tagName === 'TEXTAREA') {
            input.select()
        }
    }
}

function generateFieldInput(fieldType, value, maxLen, fieldConfig) {
    const escapedValue = (value || '').toString().replace(/"/g, '&quot;').replace(/'/g, '&#39;')
    const maxLenAttr = maxLen ? `maxlength="${maxLen}"` : ''

    switch (fieldType) {
        case 'bool':
        case 'boolean':
            return `
                <select id="field-value" class="form-control" required>
                    <option value="1" ${value == 1 ? 'selected' : ''}>Sim</option>
                    <option value="0" ${value == 0 ? 'selected' : ''}>Não</option>
                </select>
            `

        case 'textarea':
        case 'text':
            return `<textarea id="field-value" class="form-control" rows="4" ${maxLenAttr}>${escapedValue}</textarea>`

        case 'date':
            const dateValue = value ? new Date(value).toISOString().split('T')[0] : ''
            return `<input type="date" id="field-value" class="form-control" value="${dateValue}">`

        case 'datetime':
            const datetimeValue = value ? new Date(value).toISOString().slice(0, 16) : ''
            return `<input type="datetime-local" id="field-value" class="form-control" value="${datetimeValue}">`

        case 'int':
        case 'number':
        case 'numeric':
            return `<input type="number" id="field-value" class="form-control" value="${escapedValue}" step="any">`

        case 'email':
            return `<input type="email" id="field-value" class="form-control" value="${escapedValue}" ${maxLenAttr}>`

        case 'url':
            return `<input type="url" id="field-value" class="form-control" value="${escapedValue}" ${maxLenAttr}>`

        case 'password':
            return `<input type="password" id="field-value" class="form-control" value="${escapedValue}" ${maxLenAttr}>`

        case 'color':
            return `<input type="color" id="field-value" class="form-control" value="${escapedValue || '#000000'}">`

        case 'coma':
            if (fieldConfig.config_data) {
                let options = ''
                if (Array.isArray(fieldConfig.config_data)) {
                    fieldConfig.config_data.forEach((option, index) => {
                        options += `<option value="${index}" ${value == index ? 'selected' : ''}>${option}</option>`
                    })
                } else {
                    Object.keys(fieldConfig.config_data).forEach(key => {
                        options += `<option value="${key}" ${value == key ? 'selected' : ''}>${fieldConfig.config_data[key]}</option>`
                    })
                }
                return `<select id="field-value" class="form-control">${options}</select>`
            }
            return `<input type="text" id="field-value" class="form-control" value="${escapedValue}" ${maxLenAttr}>`

        default:
            return `<input type="text" id="field-value" class="form-control" value="${escapedValue}" ${maxLenAttr}>`
    }
}

function saveInlineEdit(app, pk, field, button) {
    const modal = currentEditModal?.modal
    if (!modal) return

    const input = modal.querySelector('#field-value')
    if (!input) return

    const newValue = input.value

    // Disable button during save
    button.disabled = true
    button.textContent = 'Salvando...'

    // Send update request
    fetch(`/crud/field/${app}/${pk}/${field}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({value: newValue})
    })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update the cell content
                const fieldType = currentEditModal.element.getAttribute('data-field-type')
                updateCellContent(currentEditModal.element, newValue, fieldType)

                // Close modal
                closeInlineEditModal()

                // Show success message (optional)
                if (typeof toastr !== 'undefined') {
                    success('Campo atualizado com sucesso!')
                } else {
                    console.log('Campo atualizado com sucesso!')
                }
            } else {
                alert('Erro ao salvar: ' + (data.message || 'Erro desconhecido'))
            }
        })
        .catch(error => {
            console.error('Error saving field:', error)
            alert('Erro ao salvar campo')
        })
        .finally(() => {
            button.disabled = false
            button.textContent = 'Salvar'
        })
}

function updateCellContent(element, newValue, fieldType) {
    // Update cell content based on field type
    let displayValue = newValue

    switch (fieldType) {
        case 'bool':
        case 'boolean':
            displayValue = newValue == 1 ? '<i class="fas fa-check text-success"></i>' : '<i class="fas fa-times text-danger"></i>'
            break

        case 'date':
            if (newValue) {
                const date = new Date(newValue)
                displayValue = date.toLocaleDateString('pt-BR')
            }
            break

        case 'datetime':
            if (newValue) {
                const date = new Date(newValue)
                displayValue = `<b>${date.toLocaleDateString('pt-BR')} ${date.toLocaleTimeString('pt-BR', {hour: '2-digit', minute: '2-digit'})}</b>`
            }
            break

        case 'color':
            element.style.backgroundColor = newValue
            displayValue = newValue
            break

        default:
            displayValue = newValue || ''
    }

    element.innerHTML = displayValue
}

function closeInlineEditModal() {
    if (currentEditModal) {
        const modal = currentEditModal.modal

        if (typeof $ !== 'undefined' && $.fn.modal) {
            $(modal).modal('hide')
        } else {
            modal.style.display = 'none'
            modal.classList.remove('show')
            modal.remove()
        }

        currentEditModal = null
    }
}

// Handle Escape key to close modal
document.addEventListener('keydown', function (event) {
    if (event.key === 'Escape' && currentEditModal) {
        closeInlineEditModal()
    }
})

// Handle Enter key in form inputs to save
document.addEventListener('keydown', function (event) {
    if (event.key === 'Enter' && currentEditModal) {
        const target = event.target
        if (target.id === 'field-value' && target.tagName !== 'TEXTAREA') {
            event.preventDefault()
            const saveButton = currentEditModal.modal.querySelector('.btn-primary')
            if (saveButton) {
                saveButton.click()
            }
        }
    }
})
