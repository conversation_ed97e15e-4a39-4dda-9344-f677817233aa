const fd = io('/', {query:"ts="+new Date()});
fd.on('online',function(r){
  console.log('online',r);
  if(DASHBOARD_STATS) DASHBOARD_STATS();
});

fd.on('alert_error',function(r){
  console.log('alert_error',r.h1,r.h2);
  alert_error(r.h1,r.h2);
});

let data_index = 0;
function gr(app, offset, limit, args, cb, debug ){
  ga(app, offset, limit, args, cb, debug, true );
}
function ga(app, offset, limit, args, cb, debug, is_gr ){
  let q = {offset:offset, limit: limit, args: args, debug: debug, gr: is_gr };
  data(app, q, cb );
}
function data(app, q, cb ){
  data_index++;
  const ts = new Date().getTime();
  const key = ts+'.'+data_index;
  const arg = {key: key, app: app, q: q };
  console.log('/api/v2/data',arg);
  fd.on(key, function(res){
      const result = res.result;
      const debug = res.debug;
      const args = res.args;
      if( debug ){
        const t = (result?result.length:0);
        console.log('debug> '+app+' t='+t, args);
        //if(result) console.log(result[0]);
      }
      fd.off(key);
      cb(result,debug);
  });
  fd.emit('/api/v2/data', arg);
}