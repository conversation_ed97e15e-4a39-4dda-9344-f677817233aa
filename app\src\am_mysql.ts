import {ConnectionOptions} from 'mysql2'
import DB, {DB_RES} from './db'
export default class AM_MYSQL {
    connection: DB
    config: any
    dev_env: boolean = false
    constructor(db: DB | undefined, connector_name: number, arg_config: ConnectionOptions, arg_callback: (error?: string) => void, arg_dev_env: boolean = false) {
        this.config = arg_config
        this.dev_env = arg_dev_env
        const name = 'am-mysql-' + connector_name.toString()
        this.connection = db || new DB(name, arg_config, arg_callback)
    }
    format_table(db: string, table: string): string {
        return '`' + db + '`.`' + table + '`'
    }
    async databases(): Promise<any> {
        const str = 'SELECT SCHEMA_NAME id,SCHEMA_NAME txt FROM information_schema.schemata ORDER BY SCHEMA_NAME ASC'
        return this.exec(str)
    }
    async tables(arg_db: string): Promise<any> {
        const str = `
            SELECT TABLE_NAME id,
            if(automake_name is not null,
                concat(TABLE_NAME,' (',automake_name,')'), TABLE_NAME) txt,
            TABLE_COMMENT _descricao,
            if(automake_name is not null,'#CDEB8B','#FFFFFF') __BG
            FROM information_schema.TABLES
            LEFT JOIN automake ON automake_table = TABLE_NAME and TABLE_SCHEMA = automake_db
            WHERE TABLE_SCHEMA = '${arg_db}'
            ORDER BY CREATE_TIME DESC;`
        return this.exec(str)
    }
    async fields(arg_db: string, arg_table: string): Promise<any> {
        const str = `
    select a.*,
			concat(label,' ',
				if(index_name='PRIMARY','+PRIMARY',''),
				if(index_name IS NOT NULL,'+INDEX',''),
				if(index_unique,'+UNIQUE','') ) txt,
				
			CASE 
				WHEN index_name='PRIMARY' THEN '#C3D9FF'
				WHEN index_unique=1 THEN '#C3D9FF'
				WHEN index_name IS NOT NULL THEN '#CDEB8B'
				ELSE '#FFFFFF' END AS __BG
	from (

	SELECT COLUMNS.COLUMN_NAME id,
		concat(COLUMNS.COLUMN_NAME,' ',COLUMNS.COLUMN_TYPE) label,
		COLUMNS.COLUMN_NAME _column,
		COLUMNS.DATA_TYPE _type,
		COLUMNS.IS_NULLABLE _null,
		COLUMNS.COLUMN_COMMENT _comment,
		IF(NUMERIC_PRECISION,
			NUMERIC_PRECISION,
			CHARACTER_MAXIMUM_LENGTH) size,
		COLUMN_DEFAULT _default,
		
		IDX.INDEX_NAME index_name,
		
		if(IDX.NON_UNIQUE=1 && LJ.REFERENCED_COLUMN_NAME IS NULL,1,0) index_unique,
		
		LJ.REFERENCED_TABLE_SCHEMA lj_db,
		LJ.REFERENCED_TABLE_NAME lj_table,
		LJ.REFERENCED_COLUMN_NAME lj_key
		
		FROM information_schema.COLUMNS
		
		LEFT JOIN information_schema.KEY_COLUMN_USAGE LJ
			ON LJ.TABLE_SCHEMA = COLUMNS.TABLE_SCHEMA
			AND LJ.TABLE_NAME = COLUMNS.TABLE_NAME
			AND LJ.COLUMN_NAME = COLUMNS.COLUMN_NAME
			AND REFERENCED_COLUMN_NAME IS NOT NULL
			
		LEFT JOIN information_schema.STATISTICS IDX
			ON IDX.TABLE_SCHEMA = COLUMNS.TABLE_SCHEMA
			AND IDX.TABLE_NAME = COLUMNS.TABLE_NAME
			AND IDX.COLUMN_NAME = COLUMNS.COLUMN_NAME
			
			
		WHERE COLUMNS.TABLE_SCHEMA = '${arg_db}' 
		AND COLUMNS.TABLE_NAME = '${arg_table}'
		ORDER BY COLUMNS.ORDINAL_POSITION ASC
		) a
		`
        return this.exec(str)
    }
    is_mysql(): boolean {
        return true
    }
    is_mssql(): boolean {
        return false
    }
    is_sqlsrv(): boolean {
        return false
    }
    field_esc(v: string): string {
        return '`' + v.replace(/`/g, '``') + '`'
    }
    esc(v: any): string {
        if (typeof v === 'string') return this.connection.pool.escape(v)
        if (typeof v === 'number') return v.toString()
        if (typeof v === 'boolean') return v ? '1' : '0'
        if (typeof v === 'object') return JSON.stringify(v)
        if (typeof v === 'function') return v.toString()
        if (typeof v === 'undefined') return 'NULL'
        return this.connection.pool.escape(v)
    }
    get_connection(): DB {
        return this.connection
    }
    async exec(str: string): Promise<DB_RES> {
        return this.connection.sql(str, [])
    }
    async call(proc_id: string, _args: any[] = []): Promise<DB_RES> {
        let l = []
        const args = _args.map(arg => {
            if (arg === null || arg === undefined || (typeof arg === 'number' && isNaN(arg))) return 'NULL'
            return arg
        })
        for (let i in args) l.push(this.esc(args[i]))
        const str = 'CALL ' + proc_id + '(' + l.join(', ') + ')'
        return this.exec(str)
    }
}
