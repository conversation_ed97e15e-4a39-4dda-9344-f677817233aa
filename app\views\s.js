const id = req.params.id;
let i = req.query.i || 0;
let j = req.query.j || '';
let _LIMIT = 1;
const args = [id, i];
gr('quiz.ad', args, function (rr) {
    
    if( rr.last==1 ){
        i = 0;
        _LIMIT = rr.total;
    }
    let r = req.site;
        r.TITLE = rr.titulo;
        r.ad = rr;

    if( rr.cat ){
        r.cat = rr.cat.split(' ');
    }else{
        r.cat = [];
    }

        
    if( j ){
        r.responses = j+'.';
    }else{
        r.responses = '';
    }
    const ESCOLHAS = j.split('.');
    let CORRETAS = 0;
    let ERRADAS = 0;
    ga('quiz.ad_q', [id, i, _LIMIT], function (perguntas) {
        //console.log('ESCOLHAS',ESCOLHAS);
        for( let i in perguntas ){
            const correta = perguntas[i].correta;
            const escolha = ESCOLHAS[i];
            let pergunta = perguntas[i].pergunta;
            if( escolha == correta ){
                CORRETAS++;
            }else{
                ERRADAS++;
            }
            pergunta = main.nl2br(pergunta);
            perguntas[i].pergunta = pergunta;
        }
        r.TOTAL = ESCOLHAS.length;
        r.PCT = parseFloat( (CORRETAS/r.TOTAL)*100 ).toFixed(1);
        r.CORRETAS = CORRETAS;
        r.ERRADAS = ERRADAS;
        
        r.BG = '#CCC';
        if( r.PCT >= 75 ){
            r.BG = '#CDEB8B';
        }else if( r.PCT >= 50 ){
            r.BG = '#FFFF88';
        }else if( r.PCT >= 40 ){
            r.BG = '#C79810';
        }else if( r.PCT >= 30 ){
            r.BG = '#FF7400';
        }
        
        r.perguntas = perguntas;
        ga('quiz.ad_r', [id], function (respostas) {
            let RESPOSTAS = {};
            for( let j in respostas ) {
                let r = respostas[j];
                //console.log(j,r);
                let qq = RESPOSTAS[r.q] || [];
                    qq.push( r );
                    //qq.c = (r.correta==j) ? '*' : '';
                RESPOSTAS[r.q] = qq;
            }
            //console.log(RESPOSTAS);
            r.RESPOSTAS = RESPOSTAS;
            r.uri = req.params.uri;
            return tr.public_index_display(req, res, '/s', r);
        });
    });
});