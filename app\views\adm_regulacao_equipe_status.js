r.equipe_status_id = req.query.equipe_status_id || 0
if (req.body.equipe_status_id) {
    const str = req.body.botoes.join(',')
    //return res.json(req.body.botoes);
    gr(
        'api_account_regulacao_equipe_status_salvar',
        [oid, gid, uid, req.IP, req.body.equipe_status_id, str],
        function () {
            return res.send('<script>document.location.href="/app' + automake_name + '"</script>')
        },
        true
    )
} else {
    ga('api_account_regulacao_equipe_status', [oid, gid, uid, req.IP], function (tree) {
        gr(
            'api_account_regulacao_equipe_status_por_id',
            [oid, gid, uid, req.IP, r.equipe_status_id],
            function (botao) {
                r.tree = tree
                r.botao = botao
                r.botoes = botao.botoes ? botao.botoes.split(',') : []
                return tr.display_adm(req, res, automake_name, r)
            },
            true
        )
    })
}
