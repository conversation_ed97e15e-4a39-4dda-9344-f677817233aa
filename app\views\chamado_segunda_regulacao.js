if (req.body.chamado_status > 0 && req.body.chamado_id > 0) {
    const chamado_id = req.body.chamado_id
    const chamado_status = req.body.chamado_status
    const args = [
        sid,
        gid,
        uid,
        ip,

        req.body.chamado_id,
        req.body.chamado_status,
        req.body.regulacao_consciencia,
        req.body.regulacao_oxigenacao,
        req.body.regulacao_pulso,
        req.body.regulacao_exposicao,
        req.body.regulacao_diagnostico,
        req.body.regulacao_acidente_de_trabalho,
        req.body.regulacao_saude_mental,
        req.body.regulacao_gravidade,
        req.body.regulacao_equipe_atual,
        req.body.regulacao_equipe > 0 ? req.body.regulacao_equipe : 0,
        req.body.regulacao_incidente,
        req.body.regulacao_destino,
        req.body.regulacao_conduta,
        req.body.regulacao_reporte_equipe,
        req.body.regulacao_orientacao_equipe,
        req.body.fcbpm,
        req.body.frbpm,
        req.body.pad,
        req.body.sato2,
        req.body.glicemia,
        req.body.tempax,
        req.body.glasgow,
        req.body.rts,
        req.body.rtsp
    ]

    gr(
        'api_account_regulacao_salvar_segunda',
        args,
        function (row) {
            if (row.ERR_SALVANDO_REGULACAO) {
                return res.send(row.ERR_SALVANDO_REGULACAO)
            } else {
                if (req.body.call_channel) {
                    main.emit('broadcast', {event_id: 'pbxip', data: {cmd: 'hangup', id: req.body.chamado_id, CHANNEL: req.body.call_channel}})
                }
                return tr.redirect(req, res, '/app/chamados_regulador')
            }
        },
        true
    )
} else {
    ga('api_account_regulacao_gravidade', [sid, gid, uid, ip], function (gravidades) {
        const id = req.query.id
        const force_open = req.query.f ? req.query.f : 0
        gr('api_account_chamado_abre_ultima_regulacao', [sid, gid, uid, ip, id, force_open], function (chamado) {
            ga('api_account_regulacao_decisoes', [sid, gid, uid, ip, 2], function (decisoes) {
                ga('api_account_regulacoes_por_chamado', [sid, gid, uid, ip, id], function (regulacoes) {
                    r.chamado = chamado
                    r.regulacoes = regulacoes
                    r.ultima_regulacao = regulacoes[regulacoes.length - 1]
                    r.gravidades = gravidades
                    r.decisoes = decisoes
                    if (chamado.ERRO_ABERTURA_CHAMADO) {
                        return res.send(chamado.ERRO_ABERTURA_CHAMADO)
                    }
                    ga('api_account_chamado_observacoes', [sid, gid, uid, ip, id], function (observacoes) {
                        r.observacoes = observacoes
                        return tr.display_adm(req, res, automake_name, r)
                    })
                })
            })
        })
    })
}
