{"name": "bootstrap-wysiwyg", "description": "A tiny Bootstrap and jQuery based WYSIWYG rich text editor based on the browser function execCommand.", "version": "1.0.5", "keywords": ["css", "js", "responsive", "front-end", "web", "wysiwyg", "editor"], "homepage": "https://github.com/steveathon/bootstrap-wysiwyg", "main": ["js/bootstrap-wysiwyg.min.js"], "ignore": [".*", "index.html", "CHANGES", "LICENSE", "SUPPORTED"], "license": "MIT", "dependencies": {"jquery": "~2.1.4", "jquery.hotkeys": "https://github.com/jeresig/jquery.hotkeys.git#master", "fontawesome": "~4.5.0", "bootstrap": "~3.3.5", "google-code-prettify": "~1.0.4"}, "_release": "1.0.5", "_resolution": {"type": "version", "tag": "1.0.5", "commit": "b4b774eddcc69b00778361eb98f170f633260636"}, "_source": "https://github.com/steveathon/bootstrap-wysiwyg.git", "_target": "^1.0.5", "_originalSource": "bootstrap-wysiwyg-steve<PERSON>on"}