r.menu_cat_id = req.query.menu_cat_id || 0
if (req.body.menu_cat_name) {
    const menu_cat_name = g('menu_cat_name')
    console.log(req.body)
    let acl = req.body.menu_cat_acl || []
    acl = acl.join(',')
    let args = [oid, gid, uid, ip, g('id')]
    args.push(menu_cat_name)
    args.push(g('menu_cat_order'))
    args.push(g('menu_cat_area'))
    args.push(g('menu_cat_ico'))
    args.push(g('menu_cat_status'))
    args.push(g('menu_show_title'))
    args.push(g('menu_color'))
    args.push(g('menu_itens_bg_color'))
    args.push(g('menu_itens_fg_color'))
    args.push(acl)
    args.push(g('menu_cat_desc'))

    //return res.json(req.body.menu_cat_acl);
    //return res.json(args);

    gr(
        'api_adm_menu_cat_save',
        args,
        function (r) {
            return tr.redirect(req, res, '/app/adm_menu_cat?name=' + menu_cat_name)
        },
        true
    )
} else {
    gr(
        'api_adm_menu_cat_by_id',
        [oid, gid, uid, ip, r.menu_cat_id],
        function (menu_cat) {
            //console.log('menu_cat', menu_cat);

            r.COPIANDO = req.query.COPIANDO > 0 ? 1 : 0
            r.cb = req.query.cb
            r.r = menu_cat
            r.COLORS = main.colors_palletes()
            return tr.display_adm(req, res, automake_name, r)
        },
        true
    )
}
