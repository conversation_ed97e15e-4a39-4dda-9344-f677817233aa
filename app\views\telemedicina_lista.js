// USUARIO E STATUS INICIAL
const userId = 1
const statusAtendimento = 1

// DECLARAÇ&#213;ES DAS FUNÇ&#213;ES

const iniciaInterface = function () {
    const arquivaAtendimento = req.query.arquivaAtendimento ? req.query.arquivaAtendimento : 0

    if (arquivaAtendimento && arquivaAtendimento === '1') arquivaAtendimentoTriagem()
    else carregaTodasTriagens()
}

const carregaTodasTriagens = function () {
    ga('telemedicina.api_verifica_triagem_usuario', [uid, gid, ip, site, userId, statusAtendimento], function (tree) {
        r.tree = tree
        //return res.json(tree);
        return tr.display_adm(req, res, automake_name, r)
    })
}

const arquivaAtendimentoTriagem = function () {
    ga('telemedicina.api_arquiva_triagem', [uid, gid, ip, site, req.query.id], function (tree) {
        return carregaTodasTriagens()
    })
}

iniciaInterface()
