r.OP = req.query.OP || ''
r.CADASTRO = req.session.CADASTRO || {}
r.SCORE = req.session.SCORE || 0
r.PROTOCOLO = req.session.PROTOCOLO || ''
if (r.OP == '1_TOKEN_INIT') {
    const TOKEN = req.query.TOKEN
    if (!TOKEN) {
        res.send({success: false, msg: 'STOP: TOKEN NÃO INFORMADO.'})
    } else {
        main.recaptcha(req, TOKEN, function (success, score_or_err) {
            if (!success) {
                res.send({success: false, msg: score_or_err})
            } else {
                req.session.SCORE = score_or_err
                req.session.PROTOCOLO = main.md5(main.gen_password() + req.query.TOKEN + main.uuid())
                res.send({success: true, PROTOCOLO: req.session.PROTOCOLO, msg: 'OK: SCORE=' + req.session.SCORE})
            }
        })
    }
} else if (r.OP == 'PLANO' && req.query.PLANO) {
    // /a/57?q=wendel&page=1&offset=0&limit=100&ds=2015-05-23&de=2020-05-23&filters[op]=edit&filters[id]=57&op=list&output=html
    const PLANO = main.replaceAll(req.query.PLANO, "'", '`')
    const sql = 'exec public_api_plano_por_id ' + req.site_id + ",0,0,'" + PLANO + "'"
    main.am_exec(req, res, req.query.APP, sql, function (tree, app, err_code, err_msg, err_sql) {
        let FOUND = []
        if (tree && tree[0]) FOUND = tree[0]
        return res.json(FOUND)
    })
} else if (r.OP == '2_CHECK' && req.query.q && r.PROTOCOLO) {
    // /a/57?q=wendel&page=1&offset=0&limit=100&ds=2015-05-23&de=2020-05-23&filters[op]=edit&filters[id]=57&op=list&output=html
    const q = main.replaceAll(req.query.q, "'", '`')
    const sql = "SELECT TOP 1 CLIENTE_ID FROM CD_CLIENTE WHERE CLIENTE_EMAIL = '" + q + "' OR CLIENTE_CPF = '" + q + "'"
    main.am_exec(req, res, req.query.APP, sql, function (tree, app, err_code, err_msg, err_sql) {
        let FOUND = false
        if (tree && tree[0] && tree[0].CLIENTE_ID) FOUND = true
        return res.json({FOUND: FOUND})
    })
} else if (req.query.OP == '3_SAVE' && r.PROTOCOLO && req.query.NOME && req.query.DOCUMENTO && req.query.EMAIL && req.query.TEL && req.query.PLANO) {
    if (!r.PROTOCOLO) return res.json({success: false, message: 'PROTOCOLO não informado.'})
    if (!req.query.NOME) return res.json({success: false, message: 'NOME não informado.'})
    if (!req.query.DOCUMENTO) return res.json({success: false, message: 'DOCUMENTO não informado.'})
    if (!req.query.EMAIL) return res.json({success: false, message: 'EMAIL não informado.'})
    if (!req.query.TEL) return res.json({success: false, message: 'TEL não informado.'})
    if (!req.query.PLANO) return res.json({success: false, message: 'PLANO não informado.'})
    if (!req.query.PLACA) return res.json({success: false, message: 'PLACA não informada.'})

    let args = [req.site_id, req.IP]
    args.push(r.PROTOCOLO)
    args.push(req.query.NOME)
    args.push(req.query.DOCUMENTO)
    args.push(req.query.EMAIL)
    args.push(req.query.TEL)
    args.push(req.query.PLANO)
    args.push(req.query.PLACA)
    const sql = 'exec api_public_cd_planos ' + req.site_id
    main.am_exec(
        req,
        res,
        57,
        sql,
        function (tree, app, err_code, err_msg, err_sql) {
            let rr = {}
            if (tree && tree[0]) rr = tree[0]
            const uri = '/app/cd-card/?cartao=' + r.PROTOCOLO
            return res.json({success: true, PROTOCOLO: rr.PROTOCOLO, message: rr.message})
        },
        true
    )
} else {
    const sql = 'exec api_public_cd_planos ' + req.site_id
    main.am_exec(
        req,
        res,
        57,
        sql,
        function (tree, app, err_code, err_msg, err_sql) {
            r.planos = tree
            return tr.display_adm(req, res, automake_name, r)
        },
        true
    )
}
