let ds = req.query.ds
let de = req.query.de
if (!ds) ds = moment(new Date()).subtract(2, 'days').format('YYYY-MM-DD')
if (!de) de = moment(new Date()).format('YYYY-MM-DD')
ds = moment(ds).format('YYYY-MM-DD')
de = moment(de).format('YYYY-MM-DD')
r.ds = ds
r.de = de

const cidade = req.query.cidade ? req.query.cidade : 0
const bairro = req.query.bairro ? req.query.bairro : 0
const sexo = req.query.sexo ? req.query.sexo : 0
const regulador = req.query.regulador ? req.query.regulador : 0
const decisoes = req.query.decisoes ? req.query.decisoes : 0
const tipo_de_diagnostico = req.query.tipo_de_diagnostico ? req.query.tipo_de_diagnostico : 0
const diagnostico = req.query.diagnostico ? req.query.diagnostico : 0
const motivo = req.query.motivo ? req.query.motivo : 0
const equipe = req.query.equipe ? req.query.equipe : 0
const exportar = req.query.exportar ? req.query.exportar : 0

function csv(tree) {
    let lines = []
    for (let i in tree) {
        const r = tree[i]
        let line = []
        if (i == 0) {
            let header = []
            for (let j in r) {
                header.push(`"${j}"`)
            }
            lines.push(header.join(';'))
        }
        for (let j in r) {
            line.push(`"${r[j]}"`)
        }
        lines.push(line.join(';'))
    }
    return lines.join('\n')
}

ga('api_account_relatorio_regulacoes', [sid, gid, uid, ip, ds, de, cidade, bairro, regulador, decisoes, tipo_de_diagnostico, diagnostico, motivo, equipe], function (tree) {
    r.tree = tree
    r.cidade = cidade
    r.bairro = bairro
    r.regulador = regulador
    r.decisoes = decisoes
    r.tipo_de_diagnostico = tipo_de_diagnostico
    r.diagnostico = diagnostico
    r.motivo = motivo
    r.equipe = equipe
    if (exportar == 1) {
        const csvstr = csv(tree)
        return res.send(csvstr)
    } else if (exportar == 2) {
        return res.json(tree)
    } else {
        return tr.display_adm(req, res, automake_name, r)
    }
})
